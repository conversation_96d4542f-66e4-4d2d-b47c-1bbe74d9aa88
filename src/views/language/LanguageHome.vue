<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { languagePageNames } from '@/router/modules/language'
import { useGlobal, useLoading } from '@/hooks'
const { globalProperties } = useGlobal()
const { startLoading, endLoading } = useLoading()
const router = useRouter()
import { languageIcons } from './config'
import type { VolSubjectOutVO, VolTypeVO } from 'va.study.service.volquiz'

type LanguageItem = VolSubjectOutVO & {
  icon?: string
  type?: string
  eventCode?: string
}
const navList = ref<LanguageItem[]>([])

const getTypeList = async () => {
  const params: VolTypeVO = {
    volType: 33
  }
  try {
    startLoading()
    const res = await globalProperties.$api.VolQuiz.getSubjectList(params)
    navList!.value = res.dataList.map((item) => {
      const chooseItem = languageIcons.find((v) => v.subjectCode === item.subjectCode)
      return {
        ...item,
        icon: chooseItem?.icon,
        type: chooseItem?.type,
        eventCode: chooseItem?.eventCode
      }
    })
  } finally {
    endLoading()
  }
}

// 方法列表
const jumpToQuizList = (
  title: string,
  subjectCode: number,
  languageType: string,
  eventCode: string
) => {
  router.push({
    name: languagePageNames.LanguageQuizList,
    query: { title, languageType, subjectCode, volType: 33 }
  })
  globalProperties.$msg.transferAppMsg(title)
  globalProperties.$aplusPush.pushEvent({ eventCode })
}

getTypeList()
</script>
<template>
  <div class="pt-[20px] text-[#3F454F]">
    <div
      class="card-item flex items-center justify-between rounded-[10px] px-[15px] py-[19px] mx-[15px] mb-[20px] text-[18px]"
      v-for="(item, index) in navList"
      :key="index"
      @click="
        jumpToQuizList(item.subjectTitle, item.subjectCode, item?.type || '', item?.eventCode || '')
      "
    >
      <div class="flex items-center mx-[15px]">
        <img :src="item.icon" alt="icon" class="icon inline-block h-[40px] w-[40px] mr-[10px]" />
        <span class="text-[16px] font-bold">{{ item.subjectTitle }}</span>
      </div>
      <van-icon name="arrow" class="text-[#999]" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.card-item {
  box-shadow: 2px 2px 12px -3px #d3cfe2;
  background-color: #fff;
  background-image: url('@/assets/images/language/language-home-bg.png');
  background-repeat: no-repeat;
  background-position: right bottom;
  background-size: 80px 40px;
}
</style>
