// Flutter 公共事件类型定义
const FLUTTER_EVENTS = {
  getSinglePhoto: 'getSinglePhoto',
  back: 'back'
} as const

type ValueFlutterEvent = (typeof FLUTTER_EVENTS)[keyof typeof FLUTTER_EVENTS]

import type { EventHandler, IMessage } from '@/types/webview_bridge'
import { nanoid } from 'nanoid'

// 定义Bridge - 单例模式
class WebBridge {
  private static instance: WebBridge
  private pendingCallbacks: Set<string> = new Set()
  private channelName: string = 'FlutterBridge' // 默认 channel 名称

  private constructor() {}

  // 获取单例实例
  static getInstance(): WebBridge {
    if (!WebBridge.instance) {
      WebBridge.instance = new WebBridge()
    }
    return WebBridge.instance
  }

  // 设置 channel 名称
  setChannelName(name: string): void {
    this.channelName = name
  }

  // 获取当前 channel 名称
  getChannelName(): string {
    return this.channelName
  }

  // 根据消息类型获取超时时间，返回null表示不设置超时
  private getTimeoutForMessage(message: IMessage): number | null {
    // 优先使用消息中指定的超时时间
    if (message.timeout !== undefined) {
      return message.timeout > 0 ? message.timeout : null
    }

    // 默认不设置超时
    return null
  }

  async sendMsg(message: IMessage): Promise<any> {
    // 检查是否为 inappwebview 环境
    if (window.flutter_inappwebview) {
      try {
        const result = await window.flutter_inappwebview.callHandler(
          this.channelName,
          JSON.stringify(message)
        )
        return result
      } catch (error) {
        console.error('❌ inappwebview 调用失败:', error)
        return Promise.reject(error)
      }
    }

    // 使用原有的 FlutterBridge 回调模式
    return new Promise((resolve, reject) => {
      // 生成安全的回调函数名（只包含字母数字和下划线）
      const safeId = nanoid().replace(/[^a-zA-Z0-9]/g, '_')
      const callbackName = `msg_callback_${safeId}`

      // 获取超时时间
      const timeoutMs = this.getTimeoutForMessage(message)

      // 记录待处理的回调
      this.pendingCallbacks.add(callbackName)

      // 清理回调函数
      const cleanup = () => {
        if (window[callbackName]) {
          delete window[callbackName]
        }
        this.pendingCallbacks.delete(callbackName)
      }

      // 设置超时处理，防止回调永远不被调用（仅在指定超时时间时）
      let timeout: number | null = null
      if (timeoutMs !== null) {
        timeout = setTimeout(() => {
          cleanup()
          reject(new Error(`Flutter bridge timeout after ${timeoutMs}ms`))
        }, timeoutMs)
      }

      // 将函数挂载到window上
      window[callbackName] = (data: any) => {
        if (timeout) {
          clearTimeout(timeout)
        }
        // 执行回调
        if (data && data.error) {
          reject(new Error(data.error))
        } else {
          resolve(data)
        }
        // 执行完毕后删除
        cleanup()
      }

      // 验证回调函数是否正确挂载
      if (typeof window[callbackName] !== 'function') {
        console.error('❌ 回调函数挂载失败:', callbackName)
        cleanup()
        reject(new Error('Failed to mount callback function'))
        return
      }

      // 检查动态 channel 是否存在
      const channelObject = window[this.channelName]
      if (!channelObject || typeof channelObject.postMessage !== 'function') {
        console.error(`❌ ${this.channelName} 不可用或缺少 postMessage 方法`)
        cleanup()
        reject(new Error(`${this.channelName} is not available or missing postMessage method`))
        return
      }

      try {
        const messageData = {
          ...message,
          callback: `window.${callbackName}`
        }

        channelObject.postMessage(JSON.stringify(messageData))
      } catch (error) {
        console.error('❌ 发送消息失败:', error)
        cleanup()
        reject(error)
      }
    })
  }

  // 清理所有待处理的回调（可选的清理方法）
  clearAllCallbacks(): void {
    this.pendingCallbacks.forEach((callbackName) => {
      if (window[callbackName]) {
        delete window[callbackName]
      }
    })
    this.pendingCallbacks.clear()
  }
}

// Flutter 事件中心 - 单例模式
class FlutterEventHub {
  private static instance: FlutterEventHub
  private handlers: Map<ValueFlutterEvent, EventHandler> = new Map()

  private constructor() {
    // 不需要初始化，Map 默认为空即可
  }

  // 获取单例实例
  static getInstance(): FlutterEventHub {
    if (!FlutterEventHub.instance) {
      FlutterEventHub.instance = new FlutterEventHub()
    }
    return FlutterEventHub.instance
  }

  // 注意：推荐使用 useFlutterEvent hooks，它会自动处理清理逻辑
  on(event: ValueFlutterEvent, handler: EventHandler): void {
    // 直接设置处理器，覆盖之前的处理器
    this.handlers.set(event, handler)
  }

  // 移除事件处理器
  off(event: ValueFlutterEvent): void {
    this.handlers.delete(event)
  }

  // 触发事件 - 只调用单个处理器
  emit(event: ValueFlutterEvent, ...args: any[]): any {
    const handler = this.handlers.get(event)
    if (handler) {
      return handler(...args)
    }
    console.warn(`[Flutter] ${event} 事件未被处理`)
  }

  // 清理所有事件处理器
  clear(): void {
    this.handlers.clear()
  }

  // 获取当前注册的事件列表（调试用）
  getRegisteredEvents(): ValueFlutterEvent[] {
    return Array.from(this.handlers.keys())
  }
}

// 导出单例实例和类型
export const webBridgeInstance = WebBridge.getInstance()
export const eventHub = FlutterEventHub.getInstance()
export { FLUTTER_EVENTS }
export type { ValueFlutterEvent }

/**
 * 使用示例：
 *
 * // 推荐方式：使用 useFlutterEvent hooks（更安全、更简洁）
 * // Vue 3 中推荐使用此方式，避免直接操作组件实例
 * import { useFlutterEvent } from '@/hooks'
 *
 * export default {
 *   setup() {
 *     const { on, FLUTTER_EVENTS } = useFlutterEvent()
 *
 *     // 注册事件监听器（自动清理）
 *     on(FLUTTER_EVENTS.getSinglePhoto, (data) => {
 *       console.log('处理照片:', data)
 *     })
 *
 *     return {}
 *   }
 * }
 *
 */
