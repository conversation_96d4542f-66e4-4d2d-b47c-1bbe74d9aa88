<script setup lang="ts">
import { getImageUrl } from '@/utils/img'
defineEmits<{
  (e: 'select'): void
}>()
</script>
<template>
  <div class="flex flex-col w-[100%] h-[444px] items-center bg-white rounded-[10px] justify-center">
    <img :src="getImageUrl('practice/img_empty_no_subject.png')" class="w-[110px] h-[104px]" />
    <span class="text-[18px] mt-[20px] text-[#3C4258] font-[600]">选择科目，获取最新考题</span>
    <span class="text-[16px] text-[#6B7186]">小寻陪你高效刷题提分！</span>
    <VaBaseButton
      type="primary"
      text="选择科目"
      @click="$emit('select')"
      class="mt-[40px] w-[247px] h-[40px] text-[16px]"
    />
  </div>
</template>
