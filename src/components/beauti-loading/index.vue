<script setup lang="ts">
defineOptions({
  name: '<PERSON><PERSON><PERSON><PERSON><PERSON>'
})
</script>
<template>
  <div class="bubble-dot">
    <i
      v-for="(item, index) in 3"
      :key="index"
      class="bubble-dot-item"
      :style="{ animationDelay: `${(index + 1) * 0.2}s` }"
    ></i>
  </div>
</template>
<style lang="scss" scoped>
.bubble-dot {
  display: inline-flex;
  padding: 0 4px;
  align-items: center;
  column-gap: 8px;
  &-item {
    width: 4px;
    height: 4px;
    background-color: #3687ef;
    border-radius: 100%;
    animation-duration: 2s;
    animation-iteration-count: infinite;
    animation-name: loadingMove;
    animation-timing-function: linear;
  }
}

@keyframes loadingMove {
  0% {
    transform: translateY(0);
  }
  10% {
    transform: translateY(4px);
  }
  20% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-4px);
  }
  40% {
    transform: translateY(0);
  }
}
</style>
