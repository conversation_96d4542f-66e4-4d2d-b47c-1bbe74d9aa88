/**
 * 设备检测工具 - 简化版
 */

// 设备类型枚举
export enum DeviceType {
  PC = 'pc',
  MOBILE = 'mobile',
  PAD = 'pad'
}

// 屏幕方向枚举
export enum ScreenOrientation {
  PORTRAIT = 'portrait', // 竖屏
  LANDSCAPE = 'landscape' // 横屏
}

/**
 * 判断设备屏幕方向
 * @returns {ScreenOrientation} 屏幕方向
 */
export function getScreenOrientation(): ScreenOrientation {
  // 优先使用 screen.orientation API
  if (screen.orientation) {
    return screen.orientation.angle === 0 || screen.orientation.angle === 180
      ? ScreenOrientation.PORTRAIT
      : ScreenOrientation.LANDSCAPE
  }

  // 降级使用窗口尺寸判断
  const { innerWidth, innerHeight } = window
  return innerWidth > innerHeight ? ScreenOrientation.LANDSCAPE : ScreenOrientation.PORTRAIT
}

/**
 * 检测是否为触摸设备
 * @returns {boolean} 是否为触摸设备
 */
export function isTouchDevice(): boolean {
  return 'ontouchstart' in window || navigator.maxTouchPoints > 0
}

/**
 * 获取设备类型（基于 User Agent，不依赖屏幕尺寸）
 * @returns {DeviceType} 设备类型
 */
export function getDeviceType(): DeviceType {
  const userAgent = navigator.userAgent

  // 1. 明确的移动设备检测（手机）
  if (/iPhone|iPod|Android.*Mobile|Windows Phone|BlackBerry|IEMobile/i.test(userAgent)) {
    return DeviceType.MOBILE
  }

  // 2. iPad 检测（传统和现代）
  if (/iPad/i.test(userAgent)) {
    return DeviceType.PAD
  }

  // 3. 现代 iPad 检测（iPadOS 13+ 伪装成桌面）
  if (/Macintosh/i.test(userAgent) && isTouchDevice()) {
    return DeviceType.PAD
  }

  // 4. Android 平板检测（更严格的条件）
  if (/Android/i.test(userAgent) && !/Mobile/i.test(userAgent)) {
    return DeviceType.PAD
  }

  // 5. 其他平板关键字
  if (/Tablet/i.test(userAgent)) {
    return DeviceType.PAD
  }

  // 6. 其他情况默认为 PC
  return DeviceType.PC
}

/**
 * 简化的移动端检测（仅基于 User Agent）
 * @returns {boolean} 是否为移动端
 */
export function isMobileDevice(): boolean {
  // 纯粹基于 User Agent，不考虑屏幕尺寸
  return /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
}

/**
 * 判断是否为横屏
 * @returns {boolean} 是否为横屏
 */
export function isLandscape(): boolean {
  return getScreenOrientation() === ScreenOrientation.LANDSCAPE
}

/**
 * 判断是否为竖屏
 * @returns {boolean} 是否为竖屏
 */
export function isPortrait(): boolean {
  return getScreenOrientation() === ScreenOrientation.PORTRAIT
}

/**
 * 判断是否为 PC 设备
 * @returns {boolean} 是否为 PC
 */
export function isPC(): boolean {
  return getDeviceType() === DeviceType.PC
}

/**
 * 判断是否为移动设备
 * @returns {boolean} 是否为移动设备
 */
export function isMobile(): boolean {
  return getDeviceType() === DeviceType.MOBILE
}

/**
 * 判断是否为平板设备
 * @returns {boolean} 是否为平板
 */
export function isPad(): boolean {
  return getDeviceType() === DeviceType.PAD
}

/**
 * 获取设备信息对象
 * @returns {object} 包含设备类型和屏幕方向的对象
 */
export function getDeviceInfo() {
  return {
    type: getDeviceType(),
    orientation: getScreenOrientation(),
    isLandscape: isLandscape(),
    isPortrait: isPortrait(),
    isPC: isPC(),
    isMobile: isMobile(),
    isPad: isPad(),
    isTouchDevice: isTouchDevice(),
    isMobileDevice: isMobileDevice(),
    screenWidth: window.innerWidth,
    screenHeight: window.innerHeight
  }
}

/**
 * 监听屏幕方向变化
 * @param {Function} callback 回调函数
 * @returns {Function} 取消监听的函数
 */
export function onOrientationChange(callback: (orientation: ScreenOrientation) => void) {
  const handleOrientationChange = () => {
    // 延迟获取，确保尺寸更新完成
    setTimeout(() => {
      callback(getScreenOrientation())
    }, 100)
  }

  // 监听多个事件以确保兼容性
  window.addEventListener('orientationchange', handleOrientationChange)
  window.addEventListener('resize', handleOrientationChange)

  // 返回取消监听的函数
  return () => {
    window.removeEventListener('orientationchange', handleOrientationChange)
    window.removeEventListener('resize', handleOrientationChange)
  }
}

/**
 * 监听设备类型变化
 * @param {Function} callback 回调函数
 * @returns {Function} 取消监听的函数
 */
export function onDeviceTypeChange(callback: (deviceType: DeviceType) => void) {
  let currentDeviceType = getDeviceType()

  const handleResize = () => {
    const newDeviceType = getDeviceType()
    if (newDeviceType !== currentDeviceType) {
      currentDeviceType = newDeviceType
      callback(newDeviceType)
    }
  }

  window.addEventListener('resize', handleResize)

  return () => {
    window.removeEventListener('resize', handleResize)
  }
}
