<script setup lang="ts">
import type { TVolQuizDataVO } from '@/services/practice/types'
import { getImageUrl } from '@/utils/img'
import MarkdowRender from '@/components/markdown-render/index.vue'
import CorrectingDesc from './CorrectingDesc.vue'
const props = withDefaults(
  defineProps<{
    flattenedQuizzes: TVolQuizDataVO[]
    no?: number
  }>(),
  {
    flattenedQuizzes: () => []
  }
)

const emit = defineEmits<{
  (e: 'goVideoAnalysis', item: TVolQuizDataVO): void
  (e: 'refresh-quiz'): void
}>()

const goVideoAnalysis = (item: TVolQuizDataVO) => {
  emit('goVideoAnalysis', item)
}

const refreshQuiz = () => {
  emit('refresh-quiz')
}
</script>

<template>
  <div class="my-answer-content">
    <div
      class="answer-item pb-[15px] mb-[15px]"
      :class="{ 'border-b border-va-line2': index !== props.flattenedQuizzes.length - 1 }"
      v-for="(item, index) in props.flattenedQuizzes"
      :key="item.uid"
    >
      <div
        class="answer-title text-va-grey-text text-[14px] mb-[20px] pl-[10px] flex items-center justify-between"
      >
        <div class="inline-flex items-center">
          <span
            class="seq mr-[15px] font-[600] text-[20px] align-middle"
            v-show="item.quizSeqNo || item.quizSeq || props.no"
            >{{ item.quizSeqNo || item.quizSeq || props.no }}.</span
          >
          <span class="label mr-[5px]">本题得分:</span>
          <CorrectingDesc
            v-if="[0, 9].includes(item.answerResult as number) || !item.answerResult"
            @refresh="refreshQuiz"
          />
          <span class="score" v-else>
            <span
              ><span
                class="font-[600]"
                :class="{
                  'text-[#EB5858]': item.answerResult === 1,
                  'text-[#0CA840;]': item.answerResult === 2
                }"
                >{{ item.answerScore }}</span
              >分/</span
            >
            <span class="text-va-grey-text-2">{{ item.score }}分</span>
          </span>
        </div>
        <div
          class="video-analysis text-va-primary-text inline-flex items-center user-select-none"
          v-show="item.avAnalysis?.length"
          @click="goVideoAnalysis(item)"
        >
          <span class="iconfont iconshipin mr-[4px]"></span>
          <span class="mr-[4px] text-[12px] font-[600]">视频讲题</span>
        </div>
      </div>
      <div class="my-answer flex items-center text-[14px] text-va-grey-text pl-[10px]">
        <span class="font-bold mr-[10px]">题目解析:</span>
      </div>
      <div class="mt-[10px] text-[14px] text-va-grey-text pl-[10px]" v-if="item.aiAnalysis">
        <MarkdowRender :content="item.aiAnalysis" />
      </div>
      <div
        class="mt-[10px] flex flex-col items-center justify-center"
        v-else-if="![2, 3].includes(item.answerState as number) && !item.aiAnalysis"
      >
        <img
          :src="getImageUrl('practice/va-analysis.png')"
          alt=""
          class="w-[79px] h-[70px] mb-[16px]"
        />
        <div class="desc text-va-grey-text-1 text-[14px] flex items-center">
          <span>唯小寻努力分析中...</span>
          <span
            class="iconfont iconshuaxin text-va-primary-btn text-[18px] ml-[5px]"
            @click="refreshQuiz"
          ></span>
        </div>
      </div>
      <div class="mt-[10px] text-[14px] text-va-grey-text pl-[10px]" v-else>暂无</div>
    </div>
  </div>
</template>

<style scoped></style>
