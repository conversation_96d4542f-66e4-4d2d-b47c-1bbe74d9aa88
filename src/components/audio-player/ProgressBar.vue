<script setup lang="ts">
import {
  ref,
  onBeforeMount,
  watch,
  computed
} from 'vue'

export interface ProgressBarProps {
  progress?: number
}
const props = withDefaults(defineProps<ProgressBarProps>(), {
  progress: 0
})
interface Emits {
  (e: 'progress-changing', progress: number): void
  (e: 'progress-changed', progress: number): void
}
const emit = defineEmits<Emits>()
const touch = ref<{
  x1?: number
  beginWidth?: number
}>({})
const processVM = ref<HTMLDivElement | null>(null)
const containerVM = ref<HTMLDivElement | null>(null)
const progressBtnWidth = 14
const offset = ref(0)

watch(
  () => props.progress,
  (newVal) => {
    setOffset(newVal)
  }
)

const progressStyle = computed(() => {
  return `width:${offset.value}px`
})
const btnStyle = computed(() => {
  return `transform:translate3d(${offset.value}px,0,0)`
})

onBeforeMount(() => {
  touch.value = {}
})

const onTouchStart = (e: TouchEvent) => {
  touch.value = {
    x1: e.touches[0].pageX,
    beginWidth: (processVM.value as unknown as HTMLDivElement)?.clientWidth
  }
}

const onTouchMove = (e: TouchEvent) => {
  const delta = e.touches[0].pageX - (touch.value?.x1 || 0)
  const tempWidth = (touch.value?.beginWidth || 0) + delta
  const barWidth = (containerVM.value as unknown as HTMLDivElement).clientWidth - progressBtnWidth
  const progress = Math.min(1, Math.max(tempWidth / barWidth, 0))
  offset.value = barWidth * progress
  emit('progress-changing', progress)
}
const onTouchEnd = (e: TouchEvent) => {
  const barWidth = (containerVM.value as unknown as HTMLDivElement).clientWidth - progressBtnWidth
  const progress = (processVM.value as unknown as HTMLDivElement)?.clientWidth / barWidth
  emit('progress-changed', progress)
}

const onClick = (e: MouseEvent) => {
  const rect = (containerVM.value as unknown as HTMLDivElement).getBoundingClientRect()
  const offsetWidth = e.pageX - rect.left
  const barWidth = (containerVM.value as unknown as HTMLDivElement).clientWidth - progressBtnWidth
  const progress = offsetWidth / barWidth
  emit('progress-changed', progress)
}

const setOffset = (progress: number) => {
  const barWidth = (containerVM.value as unknown as HTMLDivElement).clientWidth - progressBtnWidth
  offset.value = barWidth * progress
}
</script>
<template>
  <div class="progress-bar" @click="onClick" ref="containerVM">
    <div class="bar-inner">
      <div class="progress" ref="processVM" :style="progressStyle"></div>
      <div
        class="progress-btn"
        :style="btnStyle"
        @touchstart.prevent="onTouchStart"
        @touchmove.prevent="onTouchMove"
        @touchend.prevent="onTouchEnd"
      >
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.progress-bar {
  height: 30PX;
  .bar-inner {
    position: relative;
    top: 13PX;
    height: 4PX;
    background-color:#EEEEEE;
    .progress {
      position: absolute;
      height: 100%;
      background-color: #6481EE;
    }
    .progress-btn {
      position: absolute;
      left: 0;
      top: -6PX;
      height: 14PX;
      width: 14PX;
      box-sizing: border-box;
      background: #FFFFFF;
      box-shadow: 1px 1px 4px 0px rgba(126,133,152,0.3), -1px -1px 4px 0px rgba(126,133,152,0.3);
      border-radius: 50%;
    }
  }
}
</style>
