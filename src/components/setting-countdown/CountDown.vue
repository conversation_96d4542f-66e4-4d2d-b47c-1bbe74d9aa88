<script setup lang="ts">
import { useCountdown } from '@vueuse/core'
import { computed, watch } from 'vue'
import { showToast } from 'vant'

interface Props {
  time: number
}

const props = defineProps<Props>()
const emit = defineEmits(['end', 'count-down-click'])

const { remaining, start, stop, pause, resume } = useCountdown(props.time, {
  onComplete() {
    emit('end')
  },
  onTick() {
    if ([899, 900].includes(remaining.value)) {
      showToast('倒计时还有15分钟')
    } else if (remaining.value === 180) {
      showToast('倒计时还有3分钟')
    }
  }
})

watch(
  () => props.time,
  (newTime) => {
    if (newTime) {
      start(newTime)
    }
  },
  {
    immediate: true
  }
)

const restart = () => {
  resume()
}

defineExpose({
  stop,
  restart,
  pause
})

const timeText = computed(() => {
  if (!remaining.value) return '00:00:00'

  const hours = Math.floor(remaining.value / 3600)
  const minutes = Math.floor((remaining.value % 3600) / 60)
  const seconds = remaining.value % 60

  return [
    hours.toString().padStart(2, '0'),
    minutes.toString().padStart(2, '0'),
    seconds.toString().padStart(2, '0')
  ].join(':')
})

const timeColor = computed(() => {
  const totalMinutes = Math.floor(remaining.value / 60)
  if (totalMinutes < 3) return 'red'
  if (totalMinutes < 15) return 'orange'
  return ''
})

const handleTimeClick = () => {
  emit('count-down-click')
}
</script>

<template>
  <div :class="timeColor" class="text-[12px] font-bold text" @click="handleTimeClick">
    {{ timeText }}
  </div>
</template>

<style lang="scss" scoped>
.text {
  color: #3d3d3d;
  &.orange {
    color: #ffa500;
  }
  &.red {
    color: #ff0000;
  }
}
</style>
