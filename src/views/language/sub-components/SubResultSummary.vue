<script setup lang="ts">
import { formatTime } from '@/utils/time'
import { onBeforeMount, ref, computed } from 'vue'
interface summaryInfo {
  courseName?: string
  time?: number
  rightQty?: number
  totalQty?: number
  currentRate?: number
}
interface SummaryProps {
  info: summaryInfo
}
const props = defineProps<SummaryProps>()
const gradientColor = {
  '0%': '#9B69FF',
  '50%': '#4B67FC',
  '100%': '#9D69FF'
}

const text = computed(() => props.info.currentRate + '%')
</script>
<template>
  <div class="result-wrap flex items-center">
    <div class="text-text-grey text-[14px]">
      <div class="mb-[12px] flex items-center">
        <span class="label mr-[4px]">答对题数:</span>
        <span class="flex items-center">
          <span class="text-va-blue font-semibold text-[20px] mr-[4px]">{{
            props.info?.rightQty || 0
          }}</span>
          <span> / {{ props.info?.totalQty || 0 }}</span>
        </span>
      </div>
      <div class="mb-[12px]">
        <span class="label mr-[4px]">使用时长:</span
        ><span>{{ props.info?.time ? formatTime(props.info.time) : '--' }}</span>
      </div>
      <div>
        <span class="label mr-[4px]">课程:</span><span>{{ props.info?.courseName || '--' }}</span>
      </div>
    </div>

    <div class="circle flex-1 flex justify-end">
      <van-circle
        :current-rate="props.info.currentRate"
        :text="text"
        layer-color="rgba(0,112,255,0.18)"
        :color="gradientColor"
        :stroke-width="60"
        :size="106"
      >
        <div class="circle-inner flex flex-col justify-center items-center h-[100%]">
          <div class="text-[19px] text-va-blue font-semibold">{{ text }}</div>
          <div class="text-[#898E96] text-[12px]">总正确率</div>
        </div>
      </van-circle>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.result-wrap {
  background: url('@/assets/images/language/result-bg.png') no-repeat;
  background-size: 100% 100%;
  padding: 19px 20px 16px 15px;
}
</style>
