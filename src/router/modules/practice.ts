import type { RouteRecordRaw } from 'vue-router'

export enum SmartPracticePageNames {
  PracticeHome = 'PracticeHome', // 做题首页
  PracticeQuizList = 'PracticeQuizList',
  PracticeHistory = 'PracticeHistory',
  PracticeDopractice = 'PracticeDopractice',
  PracticeDetail = 'PracticeDetail',
  PracticeWrongbk = 'PracticeWrongbk',
  PracticeDowrongquiz = 'PracticeDowrongquiz',
  PracticeResult = 'PracticeResult',
  PracticeChooseSubject = 'PracticeChooseSubject',
  PracticeAiChat = 'PracticeAiChat'
}

export default [
  {
    path: '/practice/demo',
    name: 'PracticeDemo',
    meta: {
      title: '刷题看板demo'
    },
    component: () => import('@/components/draw-board/DrawBoardDemo.vue')
  },
  {
    path: '/practice/home',
    name: 'PracticeHome',
    meta: {
      title: '智能刷题'
    },
    component: () => import('@/views/smart-practice/PracticeHome.vue')
  },
  {
    path: '/practice/quiz-list',
    name: 'PracticeQuizList',
    meta: {
      title: '真题列表'
    },
    component: () => import('@/views/smart-practice/PracticeQuizList.vue'),
    props: true
  },
  {
    path: '/practice/history',
    name: 'PracticeHistory',
    meta: {
      title: '刷题记录'
    },
    component: () => import('@/views/smart-practice/PracticeHistory.vue')
  },
  {
    path: '/practice/dopractice',
    name: 'PracticeDopractice',
    meta: {
      title: '做题'
    },
    component: () => import('@/views/smart-practice/PracticeDopractice.vue')
  },
  {
    path: '/practice/detail',
    name: 'PracticeDetail',
    meta: {
      title: '题目详情'
    },
    component: () => import('@/views/smart-practice/PracticeDetail.vue')
  },
  {
    path: '/practice/result',
    name: 'PracticeResult',
    meta: {
      title: '考试报告'
    },
    component: () => import('@/views/smart-practice/PracticeResult.vue')
  },
  {
    path: '/practice/wrongbk',
    name: 'PracticeWrongbk',
    meta: {
      title: '错题本'
    },
    component: () => import('@/views/smart-practice/PracticeWrongbk.vue')
  },
  {
    path: '/practice/dowrongquiz',
    name: 'PracticeDowrongquiz',
    meta: {
      title: '错题练习'
    },
    component: () => import('@/views/smart-practice/PracticeDowrongquiz.vue')
  },
  // 选择科目
  {
    path: '/practice/choose-subject',
    name: 'PracticeChooseSubject',
    meta: {
      title: '选择科目'
    },
    component: () => import('@/views/smart-practice/PracticeChooseSubject.vue')
  },
  {
    path: '/practice/aichat',
    name: 'PracticeAiChat',
    meta: {
      title: 'AI 问答'
    },
    component: () => import('@/views/smart-practice/AiChat.vue')
  }
] as RouteRecordRaw[]
