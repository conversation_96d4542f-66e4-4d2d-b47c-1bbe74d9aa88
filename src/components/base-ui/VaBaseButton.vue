<script setup lang="ts">
import { computed, type StyleValue } from 'vue'

defineOptions({
  name: 'VaBaseButton'
})
interface Props {
  type?: 'primary' | 'text' | 'plain'
  disabled?: boolean
  loading?: boolean
  icon?: string
  customClass?: string
  labelClass?: string
  text?: string
  labelStyle?: StyleValue
}
const props = withDefaults(defineProps<Props>(), {
  type: 'primary',
  disabled: false,
  loading: false,
  icon: '',
  text: '',
  labelClass: '',
  labelStyle: () => ({})
})
const emit = defineEmits<{
  (e: 'click', event: MouseEvent): void
}>()

const buttonClasses = computed(() => [
  'text-[12px] px-[24px] py-[6px]',
  'va-button',
  `va-button--${props.type}`,
  { 'is-disabled': props.disabled || props.loading },
  props.customClass
])

const handleClick = (event: MouseEvent) => {
  if (props.disabled || props.loading) {
    return
  }
  navigator?.vibrate?.(50)
  emit('click', event)
}
//
</script>

<template>
  <div :class="[...buttonClasses]" @click="handleClick($event)">
    <slot>
      <i v-if="props.icon" :class="props.icon"></i>
      <span :class="['font-[600]', labelClass]" :style="props.labelStyle">{{ props.text }}</span>
    </slot>
  </div>
</template>
<style lang="scss" scoped>
.va-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50px;
  background-color: #fff;
  border: 1px solid #e9eaee;
  color: #3c4258;
  box-shadow: 0px 2px 0px 0px #e9ebf1;
  user-select: none;
  &--primary {
    color: #fff;
    background: #326eff;
    border: none;
    box-shadow:
      0px 3px 0px 0px #1358fd,
      0px 4px 10px 0px rgba(25, 93, 255, 0.4);
    &:active {
      box-shadow: none;
      transform: translateY(3px);
    }
  }
  &.is-disabled {
    color: #999;
    background: #f5f5f5;
    border: 1px solid #e9eaee;
    box-shadow: none;
    cursor: not-allowed;
    opacity: 0.8;
    &:active {
      transform: none;
    }
  }
  &:active {
    box-shadow: none;
    transform: translateY(2px);
  }
}
</style>
