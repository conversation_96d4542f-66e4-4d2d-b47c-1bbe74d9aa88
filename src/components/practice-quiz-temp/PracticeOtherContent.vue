<script setup lang="ts">
import type { TVolQuizOutData } from '@/services/practice/types'

export interface Props {
  currentQuizInfo: TVolQuizOutData
  parentIndex?: string
}

const props = withDefaults(defineProps<Props>(), { currentQuizInfo: () => ({}), parentIndex: '' })
</script>

<template>
  <div class="quiz-content flex">
    <div class="quiz-title flex-shrink-0">
      <span>{{ props.currentQuizInfo?.quizSeq || props.currentQuizInfo?.quizSeqNo }}</span>
    </div>
    <div class="quiz-body flex-1 ml-[6px] align-top" v-preview-image>
      <van-image
        v-if="props.currentQuizInfo.imgContent"
        :src="props.currentQuizInfo.imgContent"
        fit="cover"
      />
      <div
        v-else
        class="qzitem-quiz break-word-legacy"
        v-html="props.currentQuizInfo.content || props.currentQuizInfo.htmlContent"
      ></div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.quiz-content {
  .quiz-title {
    font-size: 14px;
    font-weight: 600;
    color: #333;
  }
}
</style>
