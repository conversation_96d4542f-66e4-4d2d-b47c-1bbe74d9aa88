import { fileURLToPath, URL } from 'node:url'

import { type ConfigEnv, defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { VantResolver } from '@vant/auto-import-resolver' // 新增

// https://vitejs.dev/config/
export default defineConfig(({ mode }: ConfigEnv) => {
  const env = loadEnv(mode, process.cwd())

  return {
    base: env?.VITE_PUBLIC_PATH || '/',
    plugins: [
      vue(),
      vueJsx(),
      AutoImport({
        resolvers: [VantResolver()]
      }),
      Components({
        resolvers: [VantResolver()]
      })
    ],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url))
      }
    }
    // server: {
    //   cors: true,
    //   proxy: {
    //     '/dev': {
    //       target: 'http://10.8.247.107:9080',
    //       changeOrigin: true,
    //       rewrite: (path) => path.replace(/^\/dev/, '')
    //     }
    //   }
    // }
  }
})
