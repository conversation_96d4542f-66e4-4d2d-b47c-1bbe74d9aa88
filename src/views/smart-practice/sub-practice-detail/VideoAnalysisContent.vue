<script setup lang="ts">
import { ref, watch, computed, useTemplateRef, nextTick } from 'vue'
import XgPlayer from '@/components/xg-player/index.vue'

interface Props {
  currentVideoList: string[]
  showVideoAnalysis?: boolean
}

const props = defineProps<Props>()
const emit = defineEmits(['close'])

const videoUrl = ref('')
const activeTab = ref(0)

watch(
  () => props.currentVideoList,
  (newList) => {
    if (newList.length > 0) {
      videoUrl.value = newList[0]
    }
  },
  { immediate: true }
)

const handleClose = () => {
  emit('close')
}

const tabs = computed(() =>
  props.currentVideoList.map((_, index) => ({
    label: `视频讲题${index + 1}`,
    value: props.currentVideoList[index]
  }))
)

const tabsRef = useTemplateRef<HTMLElement[]>('tab-item')

const handleTabChange = (index: number) => {
  activeTab.value = index
  videoUrl.value = tabs.value[index].value
  nextTick(() => {
    if (tabsRef.value?.[index]) {
      tabsRef.value[index].scrollIntoView({
        behavior: 'smooth',
        block: 'nearest',
        inline: 'center'
      })
    }
  })
}
</script>

<template>
  <div class="video-analysis-content flex flex-col h-full">
    <!-- 返回按钮 -->
    <div class="flex justify-center items-center my-[22px] mx-[25px] cursor-pointer relative">
      <i
        @click="handleClose"
        class="iconfont icondanchuangfanhui text-[20px] w-[22px] font-bold text-va-primary-text absolute left-0 top-[50%] translate-y-[-50%]"
      ></i>
      <span class="text-[18px] font-bold text-va-grey-text">视频讲解</span>
    </div>

    <!-- Tab切换 -->
    <div class="tabs-container relative">
      <div class="tabs flex overflow-x-auto">
        <div
          v-for="(tab, index) in tabs"
          :key="index"
          ref="tab-item"
          class="tab-item flex flex-col items-center text-[14px] cursor-pointer whitespace-nowrap flex-shrink-0"
          :class="{
            'text-va-primary-text font-semibold': activeTab === index,
            'text-va-grey-text-2': activeTab !== index,
            'mr-[36px]': index !== tabs.length - 1
          }"
          @click="handleTabChange(index)"
        >
          <span>{{ tab.label }}</span>
          <span class="iconfont iconshipinjiangjie-xuanzhong" v-show="activeTab === index"></span>
        </div>
      </div>
    </div>

    <!-- 视频播放器区域 -->
    <div class="video-container w-full aspect-video overflow-hidden mt-[8px] px-[25px] box-border">
      <xg-player v-if="videoUrl && showVideoAnalysis" class="rounded-[10px]" :src="videoUrl" />
      <div v-else class="lex items-center justify-center text-[14px]">暂无视频讲解</div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.video-analysis-content {
  height: 100%;
}

.video-list {
  height: calc(100% - 32px);
}
.tabs-container {
  padding-left: 25px;
  padding-right: 10px;
  &::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 24px;
    height: 20px;
    background: linear-gradient(271deg, #f4f6fa 35%, rgba(244, 246, 250, 0) 99%);
  }
  .tabs {
    scrollbar-width: none;
    -ms-overflow-style: none;
    &::-webkit-scrollbar {
      display: none;
    }
  }
}
</style>
