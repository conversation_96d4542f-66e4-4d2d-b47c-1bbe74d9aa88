import { ref } from 'vue'
import { defineStore } from 'pinia'

type QuizInfo = {
  serial_number: string
  quizs: any[]
  originalList?: any[]
  article?: string
  sec: number
  audioUrl: string
}

export const useLanguageQuizStore = defineStore('language', () => {
  const serial_number = ref('')
  const quizs = ref<any[]>([])
  const originalList = ref<any[]>([])
  const article = ref('')
  const audioUrl = ref('')
  const sec = ref(0)

  const updateLatestQuiz = (info: QuizInfo) => {
    serial_number.value = info.serial_number
    quizs.value = info.quizs || []
    originalList.value = info?.originalList || []
    article.value = info?.article || ''
    sec.value = info.sec
    audioUrl.value = info.audioUrl || ''
  }

  return { audioUrl, sec, serial_number, article, quizs, originalList, updateLatestQuiz }
})
