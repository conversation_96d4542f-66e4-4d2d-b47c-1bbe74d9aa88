import { onBeforeUnmount } from 'vue'
import { eventHub, FLUTTER_EVENTS, type ValueFlutterEvent } from '@/utils/webview_bridge'
import type { EventHandler } from '@/types/webview_bridge'

/**
 * Flutter 事件处理 Hook
 * 自动处理事件注册和清理，简化组件中的使用
 * 用于监听 Flutter app 调用 window 上的方法
 */
export function useFlutterEvent() {
  /**
   * 注册 Flutter 事件监听器
   * @param event 事件类型
   * @param handler 事件处理函数
   * @param autoCleanup 是否自动清理（默认 true）
   */
  const on = (event: ValueFlutterEvent, handler: EventHandler, autoCleanup: boolean = true) => {
    // 使用当前组件实例注册事件
    eventHub.on(event, handler)

    // 自动清理
    if (autoCleanup) {
      onBeforeUnmount(() => {
        eventHub.off(event)
      })
    }
  }

  /**
   * 手动移除事件监听器
   * @param event 事件类型
   */
  const off = (event: ValueFlutterEvent) => {
    eventHub.off(event)
  }

  /**
   * 批量注册多个事件
   * @param events 事件配置数组
   * @param autoCleanup 是否自动清理（默认 true）
   */
  const onMultiple = (
    events: Array<{
      event: ValueFlutterEvent
      handler: EventHandler
    }>,
    autoCleanup: boolean = true
  ) => {
    events.forEach(({ event, handler }) => {
      on(event, handler, false) // 先不自动清理
    })

    // 统一清理
    if (autoCleanup) {
      onBeforeUnmount(() => {
        events.forEach(({ event }) => {
          eventHub.off(event)
        })
      })
    }
  }

  /**
   * 获取当前注册的事件列表（调试用）
   */
  const getRegisteredEvents = () => {
    return eventHub.getRegisteredEvents()
  }

  return {
    on,
    off,
    onMultiple,
    getRegisteredEvents,
    // 导出事件常量，方便使用
    FLUTTER_EVENTS
  }
}

/**
 * 使用示例：
 *
 * // 在组件中使用
 * <script setup>
 * import { useFlutterEvent } from '@/hooks/useFlutterEvent'
 *
 * const { on, FLUTTER_EVENTS } = useFlutterEvent()
 *
 * // 注册单个事件（自动清理）
 * on(FLUTTER_EVENTS.getSinglePhoto, (data) => {
 *   console.log('收到照片:', data)
 * })
 *
 * // 注册多个事件
 * onMultiple([
 *   {
 *     event: FLUTTER_EVENTS.getSinglePhoto,
 *     handler: (data) => console.log('照片:', data)
 *   },
 *   {
 *     event: FLUTTER_EVENTS.getMultiPhoto,
 *     handler: (data) => console.log('多张照片:', data)
 *   }
 * ])
 * </script>
 */
