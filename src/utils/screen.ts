import 'amfe-flexible'

/**
 * 判断是否为大屏幕
 * @param width 屏幕宽度阈值
 * @returns boolean
 */
export const isLargeScreen = (width: number = 768): boolean => {
  return window.innerWidth >= width
}

/**
 * 限制屏幕适配和字体大小限制
 * @param threshold 屏幕宽度阈值
 * @param maxFontSize 最大字体大小（单位：px）
 */
export const limitScreenFontSize = (threshold: number = 768, maxFontSize: number = 50) => {
  const htmlDom = document.documentElement
  const setValue = () => {
    if (isLargeScreen(threshold)) {
      htmlDom.style.fontSize = `${maxFontSize}px`
    }
  }

  setValue()
  window.addEventListener('resize', setValue)
}
