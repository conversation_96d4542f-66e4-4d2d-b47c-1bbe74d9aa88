import { UAParser } from 'ua-parser-js'
export const checkIsIOS = () => {
  const { os } = UAParser()
  return ['iOS', 'Mac OS'].includes(os.name as string)
}

export enum TransferType {
  jump = 'jump',
  jumpReplace = 'jumpReplace', // replace router
  share = 'share',
  getToken = 'getToken',
  getDate = 'getDate',
  getPhotos = 'getPhotos', // 多图片上传
  getSinglePhoto = 'getSinglePhoto', //  单个图片上传
  getSinglePhotoCrop = 'getSinglePhotoCrop', // 单个图片上传并裁剪
  getPhotoListCrop = 'getPhotoListCrop', // 多图片上传并裁剪
  shareToWeChat = 'shareToWeChat',
  saveToLocal = 'saveToLocal',
  logOut = 'logOut',
  back = 'back',
  handlePopupBack = 'handlePopupBack' // 处理弹窗返回键
}
interface TransferParam {
  title: string
  url: string
  [key: string]: any
}
export interface ConnectParam {
  type: TransferType
  data?: TransferParam
}

// vue调用flutter方法
export const TransferToFlutter = (param: ConnectParam) => {
  const { type, data } = param
  const url = JSON.stringify({
    type,
    data
  })
  try {
    if (checkIsIOS()) {
      return (window?.webkit as any)?.messageHandlers.WebViewBridge.postMessage(url)
    }
    return window?.WebViewBridge?.postMessage(url)
  } catch (error) {
    // eslint-disable-next-line no-console
    console.warn(error)
  }
}

export const transferAppMsg = (
  title: string,
  type: TransferType = TransferType['jump'],
  url?: string
) => {
  const param: ConnectParam = {
    type,
    data: {
      title,
      url: url || ''
    }
  }
  TransferToFlutter(param)
}

export default TransferToFlutter
