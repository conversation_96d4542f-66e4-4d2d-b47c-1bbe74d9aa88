<script setup lang="ts">
import VaBaseButton from '@/components/base-ui/VaBaseButton.vue'
import { useLoading } from '@/hooks'
import { useGlobal } from '@/hooks/useGlobal'
import { SmartPracticePageNames } from '@/router/modules/practice'
import type {
  PracticeListQueryReqDTO,
  PracticeSubmitReqDTO,
  VolListData
} from '@/services/practice'
import { usePracticeStore, useDeviceStore } from '@/stores'
import { getImageUrl } from '@/utils/img'
import { TransferToFlutter, TransferType } from '@/utils/msg'
import { computed, nextTick, onMounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import FilterSelector from './components/FilterSelector.vue'
import SelectionPopup from './components/SelectionPopup.vue'
import { buriedPointsMap } from '@/model/buried-point'

// 定义筛选器接口
interface FilterItem {
  label: string
  value: string
  active: boolean
  exam?: number
  examBoard?: number
  subject?: number
  subjectTitle?: string
  examTitle?: string
  examBoardTitle?: string
}

const router = useRouter()
const route = useRoute()
const { globalProperties } = useGlobal()
const { startLoading, endLoading, loading } = useLoading()
const practiceStore = usePracticeStore()
const deviceStore = useDeviceStore()

// 科目筛选
const subjectFilters = ref<FilterItem[]>([])

// 科目选择弹窗
const showSubjectPopup = ref(false)
const subjectPopupTitle = ref('选择科目')
const selectedSubjectValue = ref<string>('all') // 当前选中的科目值
// 已刷题目总数
const totalQuizzes = ref(0)

// 试题分组数据
const quizGroups = ref<VolListData[]>([])

// 分页相关
const pageSize = ref(10)
const currentPage = ref(1)
const finished = ref(false)

// 下拉刷新状态
const refreshing = ref(false)

// 处理下拉刷新
const onRefresh = async () => {
  try {
    // 重置分页参数
    currentPage.value = 1
    finished.value = false
    // 刷新时直接清空数据
    quizGroups.value = []
    // 重新获取第一页数据
    await getQuizList()
  } finally {
    refreshing.value = false
  }
}

// 加载更多数据
const onLoad = () => {
  if (!refreshing.value && !finished.value) {
    getQuizList(true)
  }
}

// 通用滚动函数
const scrollToActiveItem = (
  filterRef: InstanceType<typeof FilterSelector> | null,
  activeItemId: string,
  isAllOption: boolean,
  itemsCount: number
) => {
  if (filterRef === null) {
    // 如果没有传入filterRef，尝试使用subjectFilterRef
    filterRef = subjectFilterRef.value
  }

  if (!filterRef) return

  const scrollContainer = filterRef.scrollContainer

  // 如果元素数量不超过5个，不需要滚动
  if (itemsCount <= 5) return

  if (isAllOption) {
    // 如果是"不限"选项，滚动到最左侧
    if (scrollContainer) scrollContainer.scrollLeft = 0
    return
  }

  // 使用ID选择器找到活跃元素
  setTimeout(() => {
    const activeElement = document.getElementById(activeItemId)
    if (activeElement && scrollContainer) {
      // 获取活跃元素在容器中的位置
      const containerRect = scrollContainer.getBoundingClientRect()
      const elementRect = activeElement.getBoundingClientRect()

      // 计算需要滚动的距离，确保元素显示在最左侧
      const scrollOffset = elementRect.left - containerRect.left + scrollContainer.scrollLeft

      // 设置滚动位置
      scrollContainer.scrollLeft = scrollOffset
    }
  }, 0)
}

// 筛选器引用
const subjectFilterRef = ref<InstanceType<typeof FilterSelector> | null>(null)

// 获取当前选中的科目
const currentSubject = computed(() => {
  return subjectFilters.value.find((subject) => subject.active)
})

// 监听当前选中的科目变化，保存到 store
watch(
  () => currentSubject.value,
  (newSubject) => {
    if (newSubject) {
      practiceStore.setHistorySelectedSubject({
        exam: newSubject.exam,
        examBoard: newSubject.examBoard,
        subject: newSubject.subject,
        examTitle: newSubject.examTitle,
        examBoardTitle: newSubject.examBoardTitle,
        subjectTitle: newSubject.subjectTitle
      })
    }
  },
  { deep: true }
)

// 根据科目ID和value激活对应科目
const activateSubjectByValue = (value: string): boolean => {
  const subjectToActivate = subjectFilters.value.find((item) => item.value === value)

  if (subjectToActivate) {
    // 将所有科目设为非激活
    subjectFilters.value.forEach((item) => (item.active = false))
    // 将匹配的科目设为激活
    subjectToActivate.active = true
    return true
  }

  return false
}

const activateSubjectByIds = (exam: number, examBoard: number, subject: number): boolean => {
  const matchingSubject = subjectFilters.value.find(
    (item) => item.exam === exam && item.examBoard === examBoard && item.subject === subject
  )

  if (matchingSubject) {
    // 将所有科目设为非激活
    subjectFilters.value.forEach((item) => (item.active = false))
    // 将匹配的科目设为激活
    matchingSubject.active = true
    return true
  }

  return false
}

// 打开科目选择弹窗
const openSubjectPopup = () => {
  // 设置当前选中的科目值
  const activeSubject = subjectFilters.value.find((subject) => subject.active)
  if (activeSubject) {
    selectedSubjectValue.value = activeSubject.value
  }

  // 显示弹窗
  subjectPopupTitle.value = '选择科目'
  showSubjectPopup.value = true
}

// 处理选择弹窗选择
const handleSelectionSelect = (value: string | number) => {
  const selectedValue = String(value)

  // 更新科目筛选器的激活状态
  activateSubjectByValue(selectedValue)

  // 将选中的科目滚动到最前面
  nextTick(() => {
    scrollToActiveItem(
      subjectFilterRef.value,
      'active-subject-item',
      selectedValue === 'all',
      subjectFilters.value.length
    )
  })

  // 重置分页状态
  currentPage.value = 1
  finished.value = false
  quizGroups.value = []

  // 更新筛选条件并重新获取数据
  getQuizList()

  // 保存当前选中的科目
  const selectedSubject = currentSubject.value
  if (selectedSubject) {
    practiceStore.setHistorySelectedSubject({
      exam: selectedSubject.exam,
      examBoard: selectedSubject.examBoard,
      subject: selectedSubject.subject,
      examTitle: selectedSubject.examTitle,
      examBoardTitle: selectedSubject.examBoardTitle,
      subjectTitle: selectedSubject.subjectTitle
    })
  }
}

// 切换科目筛选器
const toggleSubjectFilter = (index: number) => {
  // 更新所有过滤器的活跃状态
  subjectFilters.value.forEach((filter, i) => {
    filter.active = i === index
  })

  // 滚动到活跃项
  nextTick(() => {
    scrollToActiveItem(
      subjectFilterRef.value,
      `active-subject-item`,
      index === 0,
      subjectFilters.value.length
    )
  })

  // 重置分页状态
  currentPage.value = 1
  finished.value = false
  quizGroups.value = []

  // 重新获取数据
  getQuizList()

  // 保存当前选中的科目
  const selectedSubject = currentSubject.value
  if (selectedSubject) {
    practiceStore.setHistorySelectedSubject({
      exam: selectedSubject.exam,
      examBoard: selectedSubject.examBoard,
      subject: selectedSubject.subject,
      examTitle: selectedSubject.examTitle,
      examBoardTitle: selectedSubject.examBoardTitle,
      subjectTitle: selectedSubject.subjectTitle
    })
  }
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 检查是否需要打开Flutter界面
const checkAndOpenFlutterInterface = async (quiz: VolListData, hasRecordUid = false) => {
  if (deviceStore.deviceType === 'pad') {
    try {
      // 构建URL参数
      let url = `PracticeDopractice?paperUid=${quiz.volUid}&paperVersion=${quiz.volVersion}`

      // 如果有practiceRecordUid，添加到URL中
      if (hasRecordUid && quiz.practiceRecordUid) {
        url += `&practiceRecordUid=${quiz.practiceRecordUid}&practiceState=${quiz.practiceState}`
      }

      TransferToFlutter({
        type: TransferType.jump,
        data: {
          title: '',
          url: url
        }
      })
      return true // 已经打开Flutter界面
    } catch (error) {
      console.error('发送消息给Flutter失败:', error)
      // 如果发送失败，继续使用当前逻辑
      return false
    }
  }
  return false // 不是pad设备，使用当前逻辑
}

// 检查是否需要打开Flutter界面 - 订正错题
const checkAndOpenFlutterInterfaceForRectify = async (quiz: VolListData) => {
  if (deviceStore.deviceType === 'pad') {
    try {
      // 构建订正错题的URL参数
      const url = `PracticeDowrongquiz?paperUid=${quiz.volUid}&practiceRecordUid=${quiz.practiceRecordUid}&paperVersion=${quiz.volVersion}&modifyFirst=1`

      TransferToFlutter({
        type: TransferType.jump,
        data: {
          title: '',
          url: url
        }
      })
      return true // 已经打开Flutter界面
    } catch (error) {
      console.error('发送消息给Flutter失败:', error)
      // 如果发送失败，继续使用当前逻辑
      return false
    }
  }
  return false // 不是pad设备，使用当前逻辑
}

// 获取刷题记录列表
const getQuizList = async (isLoadMore = false) => {
  // 如果已经结束或者正在加载中，则不处理
  if (finished.value) {
    return
  }

  try {
    const params: PracticeListQueryReqDTO = {
      // 分页参数
      pageNumber: currentPage.value,
      pageSize: pageSize.value
    }

    // 只有在参数存在时才添加到请求参数中
    if (route.query.exam) {
      params.exam = Number(route.query.exam)
    }
    if (route.query.examBoard) {
      params.examBoard = Number(route.query.examBoard)
    }
    if (route.query.subject) {
      params.subject = Number(route.query.subject)
    }

    // 获取科目筛选器中的活跃项
    const activeSubject = subjectFilters.value.find((filter) => filter.active)
    if (activeSubject && activeSubject.value !== 'all') {
      const [exam, examBoard, subject] = activeSubject.value.split('-')
      params.exam = Number(exam)
      params.examBoard = Number(examBoard)
      params.subject = Number(subject)
    }

    const response =
      await globalProperties.$http.Practice.VolQuizManagerController.getPracticeList(params)

    if (response.data) {
      // 追加数据
      if (isLoadMore) {
        // 加载更多时，将新数据添加到现有数据后面
        quizGroups.value = [...quizGroups.value, ...(response.data.dataList || [])]
      } else {
        // 首次加载或刷新时，直接替换数据
        quizGroups.value = response.data.dataList || []
      }

      // 更新总数
      totalQuizzes.value = response.data.practicePaperCount || 0

      // 判断是否加载完成
      if (
        !response.data.dataList ||
        response.data.dataList.length === 0 ||
        quizGroups.value.length >= totalQuizzes.value
      ) {
        finished.value = true
      } else {
        // 更新页码，准备加载下一页
        currentPage.value++
      }
    } else {
      finished.value = true
    }
  } catch (error) {
    console.error('获取刷题记录失败:', error)
    finished.value = true
  } finally {
    if (!isLoadMore) {
      endLoading()
    }
  }
}

// 提交试卷
const submitTestPaper = async (quiz: VolListData) => {
  const params: PracticeSubmitReqDTO = {
    practiceRecordUid: quiz.practiceRecordUid
  }
  const res =
    await globalProperties.$http.Practice.QuizAnswerProcessController.submitPractice(params)
  if (res.success) {
    showNotify({ type: 'success', message: '练习已提交, 请稍等片刻' })
    router.push({
      name: SmartPracticePageNames.PracticeResult,
      query: {
        practiceRecordUid: quiz.practiceRecordUid
      }
    })
  }
}

const getSubscribedSubjects = async () => {
  try {
    const res = await globalProperties.$http.Practice.UserManagerController.getUserSubjectTagList()
    if (res.data?.dataList?.length) {
      // 将接口返回的科目数据转换为筛选器格式
      subjectFilters.value = res.data.dataList.map(
        (subject): FilterItem => ({
          ...subject,
          // 添加筛选器需要的字段
          label: subject.subjectTitle || '',
          // 使用组合值 exam-examBoard-subject 作为唯一标识
          value: `${subject.exam}-${subject.examBoard}-${subject.subject}` || '0-0-0',
          active: false
        })
      )

      // 从 store 中获取之前选中的科目
      const storedSubject = practiceStore.historySelectedSubject

      let foundMatch = false

      // 如果有存储的科目数据，优先使用
      if (storedSubject && storedSubject.exam && storedSubject.examBoard && storedSubject.subject) {
        foundMatch = activateSubjectByIds(
          storedSubject.exam,
          storedSubject.examBoard,
          storedSubject.subject
        )
      }

      // 如果没有匹配到存储的科目，尝试使用URL参数
      if (!foundMatch) {
        const queryExam = route.query.exam ? Number(route.query.exam) : undefined
        const queryExamBoard = route.query.examBoard ? Number(route.query.examBoard) : undefined
        const querySubject = route.query.subject ? Number(route.query.subject) : undefined

        // 如果有传入参数，尝试匹配对应的科目
        if (queryExam !== undefined && queryExamBoard !== undefined && querySubject !== undefined) {
          foundMatch = activateSubjectByIds(queryExam, queryExamBoard, querySubject)
        }
      }

      // 如果没有找到匹配的科目，默认选中第一个
      if (!foundMatch && subjectFilters.value.length > 0) {
        subjectFilters.value[0].active = true
      }

      // 获取科目数据后再获取列表数据
      await getQuizList()

      // 保存当前选中的科目
      const currentActiveSubject = currentSubject.value
      if (currentActiveSubject) {
        practiceStore.setHistorySelectedSubject({
          exam: currentActiveSubject.exam,
          examBoard: currentActiveSubject.examBoard,
          subject: currentActiveSubject.subject,
          examTitle: currentActiveSubject.examTitle,
          examBoardTitle: currentActiveSubject.examBoardTitle,
          subjectTitle: currentActiveSubject.subjectTitle
        })
      }

      // 滚动到当前选中的科目
      nextTick(() => {
        scrollToActiveItem(
          subjectFilterRef.value,
          'active-subject-item',
          currentActiveSubject?.value === 'all',
          subjectFilters.value.length
        )
      })
    }
  } catch (error) {
    console.error('获取订阅的科目失败:', error)
    return []
  }
}

// 显示科目选择弹窗
const goToChooseSubject = () => {
  // 跳转到科目选择页面
  router.push({
    name: SmartPracticePageNames.PracticeChooseSubject
  })
}

onMounted(async () => {
  globalProperties.$aplusPush.pushEvent({ eventCode: buriedPointsMap.practice_history })
  try {
    startLoading()
    await getSubscribedSubjects()
  } finally {
    endLoading()
  }
})

const paddingTop = computed(() => {
  const appInfo = (window as any)?.appInfo
  const safeAreaTop = appInfo?.safeAreaTop || 10

  // 检测是否为iPad设备或大屏设备
  const isLargeDevice = window.innerWidth >= 768 // iPad通常宽度至少为768px

  // 对于iPad或大屏设备使用更大的padding值
  const additionalPadding = isLargeDevice ? 52 : 40

  return `${Number(safeAreaTop) + additionalPadding}px`
})

const containerStyle = computed(() => {
  return {
    paddingTop: paddingTop.value
  }
})

// 查看结果
const viewResult = (quiz: VolListData) => {
  router.push({
    name: SmartPracticePageNames.PracticeResult,
    query: {
      practiceRecordUid: quiz.practiceRecordUid
    }
  })
}
// 继续刷题
const continuePractice = async (quiz: VolListData) => {
  // 检查是否需要打开Flutter界面
  const openedFlutter = await checkAndOpenFlutterInterface(quiz, true)
  if (openedFlutter) {
    return // 已经打开Flutter界面，不需要继续执行
  }

  // 如果版本号不一致提示用户 practiceVolVersion
  if (quiz.practiceVolVersion && quiz.volVersion !== quiz.practiceVolVersion) {
    showConfirmDialog({
      title: '提示',
      message: `试卷版本已更新，请重新刷题`,
      confirmButtonText: '提交试卷',
      cancelButtonText: '继续作答',
      confirmButtonColor: '#326eff'
    })
      .then(() => {
        submitTestPaper(quiz)
      })
      .catch(() => {
        router.push({
          name: SmartPracticePageNames.PracticeDopractice,
          query: {
            paperUid: quiz.volUid,
            practiceRecordUid: quiz.practiceRecordUid,
            practiceState: quiz.practiceState,
            paperVersion: quiz.volVersion
          }
        })
      })
  } else {
    router.push({
      name: SmartPracticePageNames.PracticeDopractice,
      query: {
        paperUid: quiz.volUid,
        practiceRecordUid: quiz.practiceRecordUid,
        paperVersion: quiz.volVersion,
        practiceState: quiz.practiceState
      }
    })
  }
}

// 订正错题
const rectify = async (quiz: VolListData) => {
  // 检查是否需要打开Flutter界面
  const openedFlutter = await checkAndOpenFlutterInterfaceForRectify(quiz)
  if (openedFlutter) {
    return // 已经打开Flutter界面，不需要继续执行
  }

  router.push({
    name: SmartPracticePageNames.PracticeDowrongquiz,
    query: {
      paperUid: quiz.volUid,
      practiceRecordUid: quiz.practiceRecordUid,
      paperVersion: quiz.volVersion,
      modifyFirst: '1'
    }
  })
}

// 重新刷题
const rePractice = async (quiz: VolListData) => {
  // 检查是否需要打开Flutter界面
  const openedFlutter = await checkAndOpenFlutterInterface(quiz, false)
  if (openedFlutter) {
    return // 已经打开Flutter界面，不需要继续执行
  }

  router.push({
    name: SmartPracticePageNames.PracticeDopractice,
    query: {
      paperUid: quiz.volUid,
      paperVersion: quiz.volVersion
    }
  })
}

// 去做题
const goToDoPractice = () => {
  // 获取当前选中的科目
  const selectedSubject = subjectFilters.value.find((subject) => subject.active)

  if (selectedSubject) {
    router.replace({
      name: SmartPracticePageNames.PracticeQuizList,
      query: {
        // 使用当前选中科目的值
        exam: selectedSubject.exam,
        examTitle: selectedSubject.examTitle,
        examBoardTitle: selectedSubject.examBoardTitle,
        examBoard: selectedSubject.examBoard,
        subject: selectedSubject.subject,
        subjectTitle: selectedSubject.subjectTitle
      }
    })
  } else {
    // 如果没有选中的科目，直接跳转到科目列表页面
    router.replace({
      name: SmartPracticePageNames.PracticeQuizList
    })
  }
}
</script>
<template>
  <div class="practice-history" :style="containerStyle">
    <!-- 顶部导航栏 -->
    <common-nav title="刷题记录" :show-back="true" @back="goBack" :showBackground="true" />

    <!-- 已加载内容 -->
    <div>
      <!-- 筛选器 -->
      <div class="px-[16px] py-[10px] bg-white sticky" v-show="subjectFilters.length">
        <FilterSelector
          ref="subjectFilterRef"
          :filters="subjectFilters"
          type="subject"
          @toggle="toggleSubjectFilter"
          @open-popup="openSubjectPopup"
        />
      </div>

      <!-- 已刷题目总数 -->
      <div class="px-[16px] py-[10px] text-[14px] text-[#646B77]" v-show="subjectFilters.length">
        已刷
        <span class="artfont text-[#3C4258] font-[700] text-[16px]">{{ totalQuizzes }}</span> 套真题
      </div>

      <!-- 试题列表 -->
      <van-pull-refresh
        v-model="refreshing"
        @refresh="onRefresh"
        class="flex-1"
        v-show="subjectFilters.length"
      >
        <van-list
          v-model:loading="loading"
          :finished="finished"
          :finished-text="quizGroups && quizGroups.length ? '没有更多了' : ''"
          @load="onLoad"
          :immediate-check="true"
          :offset="300"
        >
          <div class="px-[16px]" v-if="quizGroups && quizGroups.length">
            <div v-for="(quiz, index) in quizGroups" :key="quiz.practiceRecordUid || index">
              <!-- 试题卡片 -->
              <div class="bg-white rounded-[10px] mb-[12px] px-[16px] py-[12px]">
                <div class="flex flex-col">
                  <!-- 标题行 -->
                  <div class="flex items-center mb-[12px]">
                    <img
                      src="@/assets/images/practice/ic_paper.png"
                      alt="试卷"
                      class="w-[20px] h-[20px] mr-[8px]"
                    />
                    <div class="text-[16px] font-medium flex-1 line-clamp-1 text-[#3C4258]">
                      {{ quiz.volName }}
                    </div>
                  </div>

                  <!-- 标签行和分数行在同一行 -->
                  <div class="flex items-center justify-between mb-[16px]">
                    <!-- 标签部分 -->
                    <div class="flex items-center">
                      <div class="tag-item mr-[8px]">{{ quiz.examTitle || 'Alevel' }}</div>
                      <div class="tag-item mr-[8px]">{{ quiz.examBoardTitle || 'CAIE' }}</div>
                      <div class="tag-item">已刷题{{ quiz.finishTimes || 0 }}次</div>
                    </div>

                    <!-- 分数部分 -->
                    <div class="flex items-center">
                      <div class="text-[14px] font-bold text-[#3C4258]">
                        {{ quiz.practiceUserScore || 0 }}
                      </div>
                      <div class="text-[11px] text-[#8F94A8]">
                        / {{ quiz.practiceTotalScore || 0 }}分
                      </div>
                    </div>
                  </div>

                  <!-- 按钮行 -->
                  <div class="flex justify-end mb-[8px]">
                    <!-- 提交试卷 -->
                    <VaBaseButton
                      label-style="font-weight: 500;font-size: 13px;"
                      style="padding: 8px 14px; margin-left: 8px"
                      v-show="quiz.practiceState === 1"
                      :text="'提交试卷'"
                      type="plain"
                      @click="submitTestPaper(quiz)"
                    />
                    <VaBaseButton
                      label-style="font-weight: 500;font-size: 13px;"
                      style="padding: 8px 16px; margin-left: 8px"
                      class="ml-[8px]"
                      v-show="quiz.practiceState === 1"
                      :text="'继续刷题'"
                      type="plain"
                      @click="continuePractice(quiz)"
                    />

                    <!-- 查看结果 -->
                    <VaCustomButton
                      v-if="quiz.practiceState === 2"
                      type="plain"
                      text="查看结果"
                      customClass="h-[32px] md:h-[28px] px-[14px] flex items-center justify-center"
                      @click="viewResult(quiz)"
                    >
                      <span class="text-[13px] md:text-[10px] font-[500] mt-[1px]">查看结果</span>
                    </VaCustomButton>
                    <!-- 重新刷题 -->

                    <VaCustomButton
                      v-if="quiz.practiceState === 2"
                      type="plain"
                      text="重新刷题"
                      customClass="h-[32px] md:h-[28px] px-[14px] flex items-center justify-center ml-[8px]"
                      @click="rePractice(quiz)"
                    >
                      <span class="text-[13px] md:text-[10px] font-[500] mt-[1px]">重新刷题</span>
                    </VaCustomButton>
                    <!-- 订正错题 -->

                    <VaCustomButton
                      v-if="quiz.practiceState === 2 && quiz.practiceUnRectifyCount"
                      type="primary"
                      text="订正错题"
                      customClass="h-[32px] md:h-[28px] px-[14px] flex items-center justify-center ml-[8px]"
                      @click="rectify(quiz)"
                    >
                      <span class="text-[13px] md:text-[10px] font-[500] mt-[2px]"
                        >订正错题 ({{
                          (quiz.practiceUnRectifyCount ?? 0) > 99
                            ? '99+'
                            : quiz.practiceUnRectifyCount ?? 0
                        }})</span
                      >
                    </VaCustomButton>
                  </div>
                  <!-- 日期显示 -->
                  <div class="flex justify-end mt-[10px]">
                    <div class="text-[11px] text-[#8F94A8]/70">
                      {{ quiz.practiceDate || '' }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div v-else class="flex flex-col items-center h-[350px] py-[10px]">
            <img
              :src="getImageUrl('practice/img_empty_no_subject.png')"
              class="w-[120px] h-[120px] mt-[64px]"
            />
            <div class="text-center text-[18px] text-[#3C4258] mb-[40px] mt-[20px] font-[600]">
              你还没有开始刷题哦～
            </div>
            <VaBaseButton
              :text="'去刷题'"
              @click="goToDoPractice"
              class="w-[247px] h-[40px] text-[16px]"
            />
          </div>
        </van-list>
      </van-pull-refresh>

      <!-- 空科目提示 -->
      <div class="px-[16px]" v-show="!subjectFilters?.length && !loading">
        <div class="flex flex-col items-center pt-[86px]">
          <img
            :src="getImageUrl('practice/img_empty_no_subject.png')"
            class="w-[110px] h-[104px]"
          />
          <div class="text-center mt-[16px]">
            <p class="text-[18px] font-medium text-[#3C4258] mb-[4px]">选择科目，开始刷题</p>
            <p class="text-[14px] text-[#6B7186]">小寻陪你高效刷题提分！</p>
          </div>
          <VaBaseButton
            text="选择科目"
            @click="goToChooseSubject"
            type="primary"
            class="w-[247px] h-[40px] mt-[24px] text-[16px]"
          />
        </div>
      </div>

      <!-- 科目选择弹窗 -->
      <SelectionPopup
        v-model:show="showSubjectPopup"
        :title="subjectPopupTitle"
        :options="subjectFilters"
        :model-value="selectedSubjectValue"
        @update:model-value="(value: string | number) => (selectedSubjectValue = String(value))"
        @select="handleSelectionSelect"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.practice-history {
  background-color: #f3f5f8;
  min-height: 100vh;
}

.tag-item {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 4px 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
  font-size: 12px;
  color: #646b77;
}

.history-button {
  height: 36px !important;
  border-radius: 6px !important;
  padding: 0 !important;
  box-shadow: none !important;
  font-size: 14px !important;

  &.history-button-plain {
    border: 1px solid #326eff !important;
    color: #326eff !important;
    background-color: transparent !important;
  }

  &.history-button-primary {
    background-color: #326eff !important;
    color: white !important;
  }
}
</style>
