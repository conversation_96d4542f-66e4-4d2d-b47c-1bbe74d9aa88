import { ref } from 'vue'
import { defineStore } from 'pinia'
import $config from '@/config'
import { useLocalStorage } from '@vueuse/core'
export interface UserInfo {
  isFirstDoPracticeGuide: boolean
}

export const fristGuideKey = 'isFristGuide' + $config.runEnv

export const useUserInfoStore = defineStore('userinfo', () => {
  const cacheIsFristGuide = useLocalStorage(fristGuideKey, true)
  const isFirstDoPracticeGuide = ref<boolean>(cacheIsFristGuide.value)

  const updateFirstDoPracticeGuide = (flag: boolean) => {
    cacheIsFristGuide.value = flag
    isFirstDoPracticeGuide.value = flag
  }

  return { updateFirstDoPracticeGuide, isFirstDoPracticeGuide }
})
