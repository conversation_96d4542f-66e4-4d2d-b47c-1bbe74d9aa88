<script setup lang="ts">
import type { TVolQuizDataVO } from '@/services/practice/types'

const props = withDefaults(
  defineProps<{
    flattenedQuizzes: TVolQuizDataVO[]
    no?: number
  }>(),
  {
    flattenedQuizzes: () => []
  }
)
</script>

<template>
  <div class="my-answer-content">
    <div
      class="answer-item pb-[15px] mb-[15px]"
      v-for="(item, index) in props.flattenedQuizzes"
      :key="item.uid"
      :class="{ 'border-b border-va-line2': index !== props.flattenedQuizzes.length - 1 }"
    >
      <div class="answer-title text-va-grey-text text-[14px] mb-[20px] pl-[10px] flex items-center">
        <span
          class="seq mr-[15px] font-[600] text-[20px] align-middle"
          v-show="item.quizSeqNo || item.quizSeq || props.no"
          >{{ item.quizSeqNo || item.quizSeq || props.no }}.</span
        >
        <span class="text-va-grey-text-2">({{ item.score }}分)</span>
      </div>
      <div class="my-answer flex text-[14px] text-va-grey-text pl-[10px] mb-[10px]">
        <span class="font-bold mr-[10px] flex-shrink-0 w-[62px] prefix">正确答案</span>
        <span v-show="[1, 6].includes(item.quizType as number)">
          <span
            v-for="(v, idx) in item.answers || []"
            :key="idx"
            class="mr-[4px] text-[14px]"
            v-html="v.content || v.htmlContent"
            v-preview-image
          ></span>
        </span>
      </div>
      <div class="mt-[10px] mb-[10px] pl-[10px]" v-preview-image>
        <template v-if="[5].includes(item.quizType as number)">
          <div
            class="mb-[10px] text-[14px] text-[#3d3d3d] leading-[24px] flex"
            v-for="(v, answerIdx) in item.answers || []"
            :key="answerIdx"
          >
            <div class="flex-shrink-0 mr-[10px] w-[26px]">空{{ answerIdx + 1 }}</div>
            <div class="flex-1">
              <div v-for="(subV, subAnswerIdx) in v.options" :key="subAnswerIdx">
                <span
                  class="inline-block h-[6PX] w-[6PX] rounded-[50%] bg-[#3d3d3d] align-text-middle"
                ></span>
                <div v-html="subV" class="ml-[4px] text-[14px] inline-flex"></div>
                <div
                  v-show="Array.isArray(v?.options) && subAnswerIdx < v.options.length - 1"
                  class="text-[#8F94A8]"
                >
                  或
                </div>
              </div>
            </div>
          </div>
        </template>
        <template v-if="![1, 5, 6].includes(item.quizType as number)">
          <span
            v-for="(v, idx) in item.answers || []"
            :key="idx"
            class="mb-[8px] text-[14px] block-img-container"
            v-html="v.content || v.htmlContent"
          ></span>
        </template>
      </div>
      <!-- <div class="my-answer flex text-[14px] text-va-grey-text pl-[10px] mb-[10px]">
        <div class="flex-shrink-0 w-[62px] mr-[10px]">
          <span class="font-bold prefix flex w-full">知识点</span>
        </div>
        <span class="flex flex-col">
          <span v-for="(v, idx) in item.knowledges || []" :key="idx" class="mb-[4px]"
            >{{ idx + 1 }}. {{ v.knowledgeName }}</span
          >
        </span>
      </div>
      <div class="my-answer flex text-[14px] text-va-grey-text pl-[10px]">
        <span class="font-bold mr-[10px] flex-shrink-0 prefix w-[62px]">难度</span>
        <span class="mr-[4px]">
          {{ item.difficultyName || '暂无' }}
        </span>
      </div> -->
    </div>
  </div>
</template>

<style lang="scss" scoped>
.block-img-container {
  :deep(img) {
    display: block;
    border-radius: 10px;
    border: 1px solid #3c4258;
    overflow: hidden;
  }
}
.prefix {
  position: relative;
  &::after {
    content: ':';
    position: absolute;
    right: 0;
    top: 50%;
    display: inline-flex;
    transform: translateY(-55%);
    color: #3c4258;
    font-size: 14px;
    font-weight: 600;
  }
}
</style>
