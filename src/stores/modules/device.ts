import { defineStore } from 'pinia'
import { ref, computed, onUnmounted } from 'vue'
import {
  getDeviceType,
  getScreenOrientation,
  getDeviceInfo,
  type DeviceType,
  type ScreenOrientation
} from '@/utils/device'

export const useDeviceStore = defineStore('device', () => {
  // 只根据 webview url 查询字符串初始化设备类型
  function getDeviceTypeFromUrl(): DeviceType | undefined {
    try {
      const url = window.location.href
      const match = url.match(/[?&]client=([^&]+)/i)
      if (match) {
        const clientValue = match[1].toLowerCase()
        if (clientValue === 'pad') return 'pad' as DeviceType
        if (clientValue === 'mobile') return 'mobile' as DeviceType
      }
    } catch (e) {
      console.warn('解析 client 参数失败', e)
    }
    return undefined
  }

  // 设备类型 - 只在初始化时设置一次
  const deviceType = ref<DeviceType>()

  // 屏幕相关状态（会随resize更新）
  const orientation = ref<ScreenOrientation>(getScreenOrientation())
  const screenWidth = ref(window.innerWidth)
  const screenHeight = ref(window.innerHeight)

  // 计算属性 不准 依赖ua
  const deviceInfo = computed(() => getDeviceInfo())

  // 更新屏幕相关数据
  const updateScreenData = () => {
    orientation.value = getScreenOrientation()
    screenWidth.value = window.innerWidth
    screenHeight.value = window.innerHeight
  }

  // resize监听器
  let resizeCleanup: (() => void) | null = null

  // 初始化resize监听
  const initResizeListener = () => {
    const handleResize = () => {
      // 延迟更新，确保尺寸变化完成
      setTimeout(() => {
        updateScreenData()
      }, 100)
    }

    window.addEventListener('resize', handleResize)
    window.addEventListener('orientationchange', handleResize)

    resizeCleanup = () => {
      window.removeEventListener('resize', handleResize)
      window.removeEventListener('orientationchange', handleResize)
    }
  }

  // 初始化设备类型 - 只根据URL参数，不考虑resize事件
  const initDeviceType = () => {
    // 优先从 URL 获取设备类型
    const urlDeviceType = getDeviceTypeFromUrl()
    deviceType.value = urlDeviceType
  }

  // 初始化resize监听器（只监听屏幕数据变化）
  const initListeners = () => {
    initResizeListener()
  }

  // 清理监听器
  const cleanup = () => {
    if (resizeCleanup) {
      resizeCleanup()
      resizeCleanup = null
    }
  }

  // 组件卸载时清理
  onUnmounted(() => {
    cleanup()
  })

  return {
    // 状态
    deviceType,
    orientation,
    screenWidth,
    screenHeight,

    // 计算属性
    deviceInfo,

    // 方法
    initDeviceType,
    initListeners,
    updateScreenData,
    cleanup
  }
})
