/** @type {import('tailwindcss').Config} */
const plugin = require('tailwindcss/plugin')

export default {
  content: ['./index.html', './src/**/*.{vue,js,ts,jsx,tsx}'],
  theme: {
    fs: {
      xs: '12px',
      sm: '14px',
      md: '16px',
      lg: '18px',
      xl: '20px'
    },
    extend: {
      colors: {
        'bg-grey': '#F2F3F5',
        'text-grey': '#3F454F',
        'va-text-grey': '#999',
        'va-blue': '#6481EE',
        'va-warn': '#FF5F5F',
        'va-line1': '#d4d6de',
        'va-line2': '#e9eaee',
        'va-bg': '#F3F5FB',
        'va-bg-cell1': '#F4F6FA',
        'va-bg-cell2': '#F8F9FC',
        'va-primary-text': '#0256FF',
        'va-primary-pic': '#2462f7',
        'va-primary-btn': '#326eFF',
        'va-help': '#e8eeff',
        'va-grey-text': '#3c4258',
        'va-grey-text-1': '#6b7186',
        'va-grey-text-2': '#8f94a8'
      }
    }
  },
  plugins: [
    plugin(function ({ addBase, theme }) {
      Object.keys(theme('fs')).forEach((key) => {
        addBase({
          [`.fs-${key}`]: {
            fontSize: theme(`fs.${key}`)
          }
        })
      })
    })
  ]
}
