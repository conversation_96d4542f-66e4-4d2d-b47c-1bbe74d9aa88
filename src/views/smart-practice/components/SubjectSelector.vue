<script setup lang="ts">
import { ref, computed, nextTick, watch } from 'vue'
import FilterSelector from './FilterSelector.vue'
import SelectionPopup from './SelectionPopup.vue'
import { usePracticeStore } from '@/stores/modules/practice'

const practiceStore = usePracticeStore()

const props = defineProps<{
  filters: Array<{
    label: string
    value: string
    active: boolean
    exam?: number
    examBoard?: number
    subject?: number
    [key: string]: any
  }>
  type?: string
  showPopupOnFirstVisit?: boolean // 是否在首次访问时显示弹窗
  storeKey?: string // 用于存储访问状态的键名，默认为 hasVisitedWrongbkPage
}>()

const emit = defineEmits<{
  (e: 'toggle', index: number): void
  (e: 'select', value: string | number): void
  (e: 'update:filters', filters: typeof props.filters): void
}>()

// SelectionPopup相关
const showSelectionPopup = ref(false)
const selectionPopupTitle = ref('选择科目')
const selectedValue = ref<string | number>('')

// 滚动容器引用
const filterRef = ref<InstanceType<typeof FilterSelector> | null>(null)

// 选项列表和唯一ID映射
const popupOptions = computed(() => {
  // 为弹窗选项添加更多元数据，便于准确匹配
  return props.filters.map((item, index) => ({
    label: item.label,
    value: String(item.value),
    exam: item.exam,
    examBoard: item.examBoard,
    subject: item.subject,
    uniqueId: `${item.exam || ''}-${item.examBoard || ''}-${item.subject || ''}-${index}`, // 添加唯一标识
    index // 保存在原数组中的索引
  }))
})

// 弹窗选项
const popupSelectOptions = computed(() => {
  return popupOptions.value.map((option) => ({
    label: option.label,
    value: option.uniqueId
  }))
})

// 通用滚动函数
const scrollToActiveItem = (activeItemId: string, isAllOption: boolean, itemsCount: number) => {
  if (!filterRef.value) return

  const scrollContainer = filterRef.value.scrollContainer

  // 如果元素数量不超过5个，不需要滚动
  if (itemsCount <= 5) return

  if (isAllOption) {
    // 如果是"不限"选项，滚动到最左侧
    if (scrollContainer) scrollContainer.scrollLeft = 0
    return
  }

  // 使用ID选择器找到活跃元素
  setTimeout(() => {
    const activeElement = document.getElementById(activeItemId)
    if (activeElement && scrollContainer) {
      // 获取活跃元素在容器中的位置
      const containerRect = scrollContainer.getBoundingClientRect()
      const elementRect = activeElement.getBoundingClientRect()

      // 计算需要滚动的距离，确保元素完全显示在左侧
      // 添加一个小的偏移量(1px)确保前一个元素完全不可见
      const scrollOffset = elementRect.left - containerRect.left + scrollContainer.scrollLeft - 1

      // 设置滚动位置
      scrollContainer.scrollLeft = scrollOffset
    }
  }, 0)
}

// 打开选择弹窗
const openSelectionPopup = () => {
  if (props.filters.length > 0) {
    // 设置当前选中的值
    const activeFilter = props.filters.find((filter) => filter.active)
    if (activeFilter) {
      // 找到弹窗选项中对应的项
      const activeOption = popupOptions.value.find(
        (option) =>
          option.exam === activeFilter.exam &&
          option.examBoard === activeFilter.examBoard &&
          option.subject === activeFilter.subject
      )

      if (activeOption) {
        // 设置该项的值作为选中值
        selectedValue.value = activeOption.uniqueId
      } else {
        // 如果没有找到，默认第一个
        selectedValue.value = popupOptions.value[0].uniqueId
      }
    } else if (props.filters.length > 0) {
      // 如果没有选中的项，默认选择第一个
      selectedValue.value = popupOptions.value[0].uniqueId
    }
  }

  // 显示弹窗
  showSelectionPopup.value = true
}

// 处理选择
const handleSelectionSelect = (value: string | number) => {
  // 在弹窗选项中找到用户选择的具体项
  const selectedOption = popupOptions.value.find(
    (option) => String(option.uniqueId) === String(value)
  )

  if (selectedOption) {
    // 直接使用保存的索引
    const index = selectedOption.index

    // 触发toggle事件，处理active状态
    emit('toggle', index)

    // 等待DOM更新后滚动
    nextTick(() => {
      const activeItemId = `active-${props.type || 'filter'}-item`
      scrollToActiveItem(activeItemId, selectedOption.value === '0', props.filters.length)
    })

    // 触发select事件
    emit('select', selectedOption.value)
  }
}

// 处理切换事件
const handleToggle = (index: number) => {
  emit('toggle', index)
}

// 监听filters变化，更新内部状态
watch(
  () => props.filters,
  (newFilters) => {
    if (newFilters.length > 0) {
      // 设置当前选中的值
      const activeFilter = newFilters.find((filter) => filter.active)
      if (activeFilter && popupOptions.value.length > 0) {
        // 找到弹窗选项中对应的项
        const activeOption = popupOptions.value.find(
          (option) =>
            option.exam === activeFilter.exam &&
            option.examBoard === activeFilter.examBoard &&
            option.subject === activeFilter.subject
        )

        if (activeOption) {
          // 设置该项的值作为选中值
          selectedValue.value = activeOption.uniqueId
        } else {
          // 如果没有找到，默认第一个
          selectedValue.value = popupOptions.value[0].uniqueId
        }
      } else if (newFilters.length > 0 && popupOptions.value.length > 0) {
        // 如果没有选中的项，默认选择第一个
        selectedValue.value = popupOptions.value[0].uniqueId
      }
    }
  },
  { immediate: true }
)

// 暴露方法给父组件
defineExpose({
  openSelectionPopup,
  scrollToActiveItem,
  filterRef
})
</script>

<template>
  <div>
    <!-- 筛选器 -->
    <FilterSelector
      ref="filterRef"
      :filters="filters"
      :type="type || 'filter'"
      @toggle="handleToggle"
      @open-popup="openSelectionPopup"
    />

    <!-- 选择弹窗 -->
    <SelectionPopup
      :show="showSelectionPopup"
      @update:show="showSelectionPopup = $event"
      :model-value="selectedValue"
      @update:model-value="selectedValue = $event"
      :title="selectionPopupTitle"
      :options="popupSelectOptions"
      @select="handleSelectionSelect"
    />
  </div>
</template>
