<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
export interface QuizProps {
  title: string
  tag?: string
  modelValue: {
    content: string
    answers?: any[]
  }
}
const emit = defineEmits(['update:modelValue'])
const props = withDefaults(defineProps<QuizProps>(), {
  tag: '填空题'
})

const changeVal = (val: string, index: number) => {
  const options = JSON.parse(JSON.stringify(props.modelValue.answers || []))
  options[index].studentAnswer = val
  emit('update:modelValue', { ...props.modelValue, answers: options })
}
</script>
<template>
  <div class="blank-filling-wrap bg-white px-[15px] pt-[15px] pb-[20px] box-border">
    <div class="title flex items-center mb-[15px]">
      <span class="label text-text-grey text-[16px] font-semibold mr-[14px]">{{
        props.title
      }}</span>
      <van-tag
        color="rgba(100, 129, 238, 0.22)"
        text-color="#6481EE"
        class="text-[12px] leading-[12px] rounded-[4px] py-[2px]"
        >{{ props.tag }}</van-tag
      >
    </div>
    <div
      v-if="props.modelValue.content"
      v-html="props.modelValue.content"
      class="text-[14px] mb-[15px]"
    ></div>

    <div v-for="(item, index) in props.modelValue.answers" :key="index" class="mb-[10px] last:mb-0">
      <div
        class="text-[14px] leading-[16px] mr-[6px] mb-[6px]"
        v-html="item.content.replace(/\[blank\]\[\/blank\]/g, '__')"
      ></div>
      <van-field
        @update:model-value="(val) => changeVal(val, index)"
        :modelValue="item.studentAnswer"
        placeholder="请输入答案"
        clearable
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
:deep(.van-field) {
  border: 1px solid #ddd;
  border-radius: 2px;
}
</style>
