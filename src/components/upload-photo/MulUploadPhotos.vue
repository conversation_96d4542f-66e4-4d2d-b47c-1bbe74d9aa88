<script setup lang="ts">
import { computed } from 'vue'

defineOptions({
  name: 'MulUploadPhotos'
})

interface Props {
  pics?: string | string[]
  max?: number
}

const emit = defineEmits(['select', 'remove'])
const props = withDefaults(defineProps<Props>(), {
  pics: () => [],
  max: 9
})

const picList = computed(() => {
  if (Array.isArray(props.pics)) {
    return props.pics
  } else if (typeof props.pics === 'string' && props.pics?.length) {
    return [props.pics]
  } else {
    return []
  }
})

const showAddIcon = computed(() => {
  return props.max ? picList.value.length < props.max : true
})

const handleRemove = (index: number) => {
  emit('remove', index)
}

const handleSelect = () => {
  emit('select')
}
</script>
<template>
  <div class="mul-upload-photos-wrap flex flex-wrap gap-2" v-preview-image>
    <div
      v-for="(pic, index) in picList"
      :key="index"
      class="photo-item relative w-[64px] h-[64px] rounded-[6px]"
    >
      <img :src="pic" class="block w-full h-full rounded-[6px]" />
      <div
        class="absolute top-[-8px] right-[-8px] w-[24px] h-[24px] p-[4px] box-border bg-white rounded-full flex items-center justify-center cursor-pointer"
        @click.stop="handleRemove(index)"
      >
        <span class="text-[#C9CDD8] text-[16px]"><van-icon name="cross" /></span>
      </div>
    </div>

    <div
      v-if="showAddIcon"
      class="w-[64px] h-[64px] rounded-[6px] bg-white box-border flex items-center justify-center cursor-pointer"
      @click="handleSelect"
    >
      <div class="text-[#8F94A8] text-[30px] h-[40px] w-[40px] flex justify-center items-center">
        <van-icon name="plus" />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
/* Add any additional styles if needed */
</style>
