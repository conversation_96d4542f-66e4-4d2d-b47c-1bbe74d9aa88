// 工具类型
export type ToolType =
  | 'brush'
  | 'eraser'
  | 'circle'
  | 'rectangle'
  | 'text'
  | 'background-line'
  | 'grid'
  | 'history-back'
  | 'history-forward'
  | 'clear'
  | 'export'

// 画笔大小选项
export interface BrushSize {
  value: number
  label: string
}

// 颜色选项
export interface ColorOption {
  value: string
  label: string
}

// 工具配置
export interface ToolConfig {
  brushSize: number
  color: string
}

// 工具状态
export interface ToolState {
  activeTool: ToolType
  showSubMenu: ToolType | null
  configs: Record<ToolType, ToolConfig>
}

// 二级菜单项配置
export interface SubMenuItem {
  type: 'color' | 'size'
  options: ColorOption[] | BrushSize[]
}

// 工具栏工具项
export interface ToolbarItem {
  type: ToolType
  icon: string
  label: string
  hasSubMenu: boolean
  subMenuItems?: SubMenuItem[]
}
