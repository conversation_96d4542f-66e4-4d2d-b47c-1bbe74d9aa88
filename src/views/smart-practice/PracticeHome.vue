<script setup lang="ts">
import { useGlobal, useLoading } from '@/hooks'
import { SmartPracticePageNames } from '@/router/modules/practice'
import type { UserSubjectTagInfoData } from '@/services/practice'
import { computed, onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { showConfirmDialog } from 'vant'
import EmptySubjectCard from './components/EmptySubjectCard.vue'
import MySubjectList from './components/MySubjectList.vue'
import StatisticsCard from './components/StatisticsCard.vue'
// 导入练习状态管理
import { usePracticeStore, useDeviceStore } from '@/stores'
import { buriedPointsMap } from '@/model/buried-point'
import { TransferToFlutter, TransferType } from '@/utils/msg'
const router = useRouter()
const { globalProperties } = useGlobal()
const { startLoading, endLoading, loading } = useLoading()
// 使用练习状态管理
const practiceStore = usePracticeStore()
const deviceStore = useDeviceStore()
// 刷题天数
const practiceDays = ref(0)

// 已刷题
const practiceVolCount = ref(0)

// 待订正
const unRectifyQuizCount = ref(0)

// 未提交的作业的uid
const latestUnsubmittedTestUid = ref('')

// 下拉刷新状态
const refreshing = ref(false)

// 处理下拉刷新
const onRefresh = async () => {
  try {
    refreshing.value = true
    await Promise.all([getSubjects(), getPracticeCountAndToCorrectCount()])
  } finally {
    refreshing.value = false
  }
}

// 处理统计项点击
const handleStatItemClick = (item: { label: string }) => {
  if (item.label.includes('待订正')) {
    // 跳转到错题本页面
    router.push({ name: SmartPracticePageNames.PracticeWrongbk })
  }

  if (item.label.includes('已刷题')) {
    // 跳转到刷题记录页面
    router.push({ name: SmartPracticePageNames.PracticeHistory })
  }
}

const statisticsItems = computed(() => [
  { value: practiceVolCount.value, label: '已刷题套' },
  { value: unRectifyQuizCount.value, label: '待订正道' }
])

// 科目列表
const subjects = ref<UserSubjectTagInfoData[]>([])

// 获取总的刷题套数 和 待订正道数
const getPracticeCountAndToCorrectCount = async () => {
  const { data } = await globalProperties.$http.Practice.UserManagerController.getUserHeaderCount()
  unRectifyQuizCount.value = data?.unRectifyQuizCount || 0
  practiceVolCount.value = data?.practiceVolCount || 0
  practiceDays.value = data?.practiceDays || 0
}

//获取当前用户所订阅的科目信息列表
const getSubjects = async () => {
  const { data } =
    await globalProperties.$http.Practice.UserManagerController.getUserSubjectInfoList()
  subjects.value = data?.dataList || []
}

const handleRecord = () => {
  router.push({ name: SmartPracticePageNames.PracticeHistory })
}

// 用户签到
const handleSignIn = async () => {
  await globalProperties.$http.Practice.UserManagerController.addUserSign()
  await getPracticeCountAndToCorrectCount()
}

const paddingTop = computed(() => {
  const appInfo = (window as any)?.appInfo
  const safeAreaTop = appInfo?.safeAreaTop || 10

  // 检测是否为iPad设备或大屏设备
  const isLargeDevice = window.innerWidth >= 768 // iPad通常宽度至少为768px

  // 对于iPad或大屏设备使用更大的padding值
  const additionalPadding = isLargeDevice ? 52 : 60

  return `${Number(safeAreaTop) + additionalPadding}px`
})

const containerStyle = computed(() => {
  return {
    paddingTop: paddingTop.value
  }
})

// 删除订阅科目
const handleDelete = async (subject: UserSubjectTagInfoData) => {
  // refresh data
  await getSubjects()
  await getPracticeCountAndToCorrectCount()
}

// 选择科目
const chooseSubject = () => {
  router.push({ name: SmartPracticePageNames.PracticeChooseSubject })
}

// 检查是否需要打开Flutter界面
const checkAndOpenFlutterInterface = async (practiceRecordUid: string) => {
  if (deviceStore.deviceType === 'pad') {
    try {
      const url = `PracticeDopractice?practiceRecordUid=${practiceRecordUid}&practiceState=1`

      TransferToFlutter({
        type: TransferType.jump,
        data: {
          title: '',
          url: url
        }
      })
      return true // 已经打开Flutter界面
    } catch (error) {
      console.error('发送消息给Flutter失败:', error)
      // 如果发送失败，继续使用当前逻辑
      return false
    }
  }
  return false // 不是pad设备，使用当前逻辑
}

onMounted(async () => {
  globalProperties.$aplusPush.pushEvent({ eventCode: buriedPointsMap.practice_home })
  try {
    startLoading()
    await Promise.all([getLatestUnsubmittedTest(), handleSignIn(), getSubjects()])
  } finally {
    endLoading()
  }
})

// 查询最近一次没有提交的作业
const getLatestUnsubmittedTest = async () => {
  // 判断是否已显示过弹窗
  if (practiceStore.hasShownPracticeDialog) {
    return
  }
  const { data } =
    await globalProperties.$http.Practice.UserManagerController.quickUserPracticeRecordUid()
  latestUnsubmittedTestUid.value = data || ''
  practiceStore.setHasShownPracticeDialog(true)
  // 弹窗提示
  if (latestUnsubmittedTestUid.value) {
    showConfirmDialog({
      title: '提示',
      message: `当前还有进行中的练习，是否继续答题？`,
      confirmButtonText: '继续作答',
      cancelButtonText: '暂不作答',
      confirmButtonColor: '#326eff'
    })
      .then(async () => {
        // 继续作答 - 检查是否需要打开Flutter界面
        const openedFlutter = await checkAndOpenFlutterInterface(latestUnsubmittedTestUid.value)
        if (!openedFlutter) {
          // 如果没有打开Flutter界面，继续使用H5逻辑
          router.push({
            name: SmartPracticePageNames.PracticeDopractice,
            query: { practiceRecordUid: latestUnsubmittedTestUid.value, practiceState: 1 }
          })
        }
      })
      .catch(() => {
        // 暂不作答
      })
  }
}
</script>

<template>
  <div
    class="min-h-screen bg-[#F3F5F8] bg-[url(@/assets/images/practice/top_bg.png)] md:bg-[url(@/assets/images/practice/top_bg_pad.png)] bg-no-repeat bg-[length:100%_auto] bg-top"
    :style="containerStyle"
    v-if="!loading"
  >
    <!-- 标题栏 -->
    <common-nav
      :leftArrow="true"
      :showBackground="false"
      :scrollOpacity="true"
      :maxScrollForOpacity="100"
      :enableTransition="true"
    >
      <template #right>
        <div class="flex items-center" @click="handleRecord">
          <van-icon name="clock-o" class="text-[#3C4258]" color="#3C4258" size="18" />
          <span class="text-[#3C4258] ml-[4px] font-[400]">刷题记录</span>
        </div>
      </template>
    </common-nav>

    <!-- 使用 van-pull-refresh 包裹内容 -->
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh" v-if="!loading">
      <!-- 刷题天数 -->
      <div class="mx-[16px] pt-[2px] md:pt-[17px] relative z-10">
        <span class="text-[14px] text-[#3C4258]"> 已坚持刷题 </span>
        <span class="artfont font-bold text-[20px] text-[#3C4258]">{{ practiceDays }}</span>
        <span class="text-[14px] text-[#3C4258]"> 天</span>
      </div>

      <!-- 统计卡片 -->
      <div class="mx-[16px] mt-[10px] relative z-10" v-if="!loading">
        <StatisticsCard :items="statisticsItems" @item-click="handleStatItemClick" />
      </div>

      <!-- 我的科目 -->
      <div class="flex justify-between items-center mx-[16px] mt-[24px] relative z-10">
        <div class="text-[18px] text-[#3C4258] font-[600]">我的科目</div>
        <div v-if="subjects.length > 0" class="flex items-center" @click="chooseSubject">
          <img
            src="@/assets/images/practice/manage_subject.png"
            alt="管理科目"
            class="w-[20px] h-[20px] mr-[4px]"
          />
          <span class="text-[14px] text-[#6B7186]">管理科目</span>
        </div>
      </div>

      <!-- 科目列表 -->
      <div class="mx-[16px] mt-[16px] relative z-10" v-if="!loading">
        <MySubjectList :subjects="subjects" v-if="subjects.length" @delete="handleDelete" />
        <div v-else>
          <EmptySubjectCard @select="chooseSubject" />
        </div>
      </div>
    </van-pull-refresh>
  </div>
</template>

<style lang="scss" scoped></style>
