<script setup lang="ts">
import PracticeMultiChoiceContent from './PracticeMultiChoiceContent.vue'
import PracticeSingleChoiceContent from './PracticeSingleChoiceContent.vue'
import PracticeOtherContent from './PracticeOtherContent.vue'
import PracticeComprehensiveQuestionsContent from './PracticeComprehensiveQuestionsContent.vue'
import type { ListVolQuizzesReqVO, TVolQuizOutData } from '@/services/practice/types'

export interface Props {
  currentIndex: number
  currentQuizInfo: TVolQuizOutData
  parentNumber?: string
}

const props = withDefaults(defineProps<Props>(), {
  currentQuizInfo: () => ({}),
  currentIndex: 0
})

const getQuizComponent = (type: number) => {
  switch (type) {
    case 1:
      return PracticeSingleChoiceContent
    case 6:
      return PracticeMultiChoiceContent
    case 3:
      return PracticeComprehensiveQuestionsContent
    default:
      return PracticeOtherContent
  }
}
</script>

<template>
  <div class="quiz-content p-[16px]">
    <div class="quiz-title font-bold">第{{ currentIndex + 1 }}题</div>
    <div class="quiz-body">
      <component
        :is="getQuizComponent(currentQuizInfo.quizType || 1)"
        :parentIndex="`${currentIndex}`"
        :currentQuizInfo="currentQuizInfo"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.quiz-content {
  font-size: 14px;
  .quiz-title {
    font-size: 14px;
    color: #3d3d3d;
    margin-bottom: 10px;
  }
}
</style>
