import { defineStore } from 'pinia'
import { ref } from 'vue'

export interface SelectedSubject {
  exam?: number
  examBoard?: number
  subject?: number
  examTitle?: string
  examBoardTitle?: string
  subjectTitle?: string
}

// 每个科目的筛选选项
export interface SubjectFilterOptions {
  yearValue: string
  paperValue: string
  monthValue: string
  excludePracticed: boolean
}

/**
 * 练习模块的状态管理
 */
export const usePracticeStore = defineStore(
  'practice',
  () => {
    // 是否已显示过练习首页的进行中练习弹窗
    const hasShownPracticeDialog = ref(false)

    // 错题本页面选中的科目
    const wrongbkSelectedSubject = ref<SelectedSubject | null>(null)

    // 历史记录页面选中的科目
    const historySelectedSubject = ref<SelectedSubject | null>(null)

    // 每个科目的试题列表筛选选项
    // 使用Map存储，键为 'exam-examBoard-subject' 格式
    const quizListFilterOptions = ref<Map<string, SubjectFilterOptions>>(new Map())

    /**
     * 设置是否已显示过练习首页的进行中练习弹窗
     * @param value 是否已显示过
     */
    function setHasShownPracticeDialog(value: boolean) {
      hasShownPracticeDialog.value = value
    }

    /**
     * 设置错题本页面选中的科目
     * @param subject 选中的科目信息
     */
    function setWrongbkSelectedSubject(subject: SelectedSubject | null) {
      wrongbkSelectedSubject.value = subject
    }

    /**
     * 设置历史记录页面选中的科目
     * @param subject 选中的科目信息
     */
    function setHistorySelectedSubject(subject: SelectedSubject | null) {
      historySelectedSubject.value = subject
    }

    /**
     * 获取科目对应的筛选选项
     * @param exam 考试类型
     * @param examBoard 考试局
     * @param subject 科目
     * @returns 科目对应的筛选选项，如果不存在则返回默认值
     */
    function getQuizListFilterOptions(
      exam: number,
      examBoard: number,
      subject: number
    ): SubjectFilterOptions {
      const key = `${exam}-${examBoard}-${subject}`
      const options = quizListFilterOptions.value.get(key)

      if (options) {
        return options
      }

      // 返回默认值
      return {
        yearValue: '0',
        paperValue: '0',
        monthValue: '0',
        excludePracticed: false
      }
    }

    /**
     * 保存科目对应的筛选选项
     * @param exam 考试类型
     * @param examBoard 考试局
     * @param subject 科目
     * @param options 筛选选项
     */
    function saveQuizListFilterOptions(
      exam: number,
      examBoard: number,
      subject: number,
      options: SubjectFilterOptions
    ) {
      const key = `${exam}-${examBoard}-${subject}`
      quizListFilterOptions.value.set(key, options)
    }

    /**
     * 重置所有状态
     */
    function resetState() {
      hasShownPracticeDialog.value = false
      wrongbkSelectedSubject.value = null
      historySelectedSubject.value = null
      quizListFilterOptions.value.clear()
    }

    return {
      hasShownPracticeDialog,
      wrongbkSelectedSubject,
      historySelectedSubject,
      setHasShownPracticeDialog,
      setWrongbkSelectedSubject,
      setHistorySelectedSubject,
      getQuizListFilterOptions,
      saveQuizListFilterOptions,
      resetState
    }
  },
  {
    persist: true // 启用状态持久化
  }
)
