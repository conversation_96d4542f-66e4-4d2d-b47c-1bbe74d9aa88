<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
export interface QuizProps {
  title: string
  tag?: string
  info: {
    content: string
    answers: any[]
    studentAnswerList: any[]
  }
}
const props = withDefaults(defineProps<QuizProps>(), {
  tag: '填空题'
})

const checkRight = (options: string[], answerItem: string) => {
  if (!answerItem) {
    return false
  }
  //匹配HTML标记的正则
  const reg = /<(\S*?)[^>]*>.*?|<.*? \/>/g
  //不是中文、英文和数字
  const regex = /[^\u4e00-\u9fa5a-zA-Z0-9]+/g
  const res = options.some((item) => {
    const studentAnswer = answerItem?.toLowerCase()?.trim()?.replace(reg, '')?.replace(regex, '')
    const answer = item?.toLowerCase()?.trim()?.replace(reg, '')?.replace(regex, '')
    return answer === studentAnswer
  })
  return res
}
</script>
<template>
  <div class="blank-filling-wrap bg-white px-[15px] pt-[15px] pb-[20px] box-border">
    <div class="title flex items-center mb-[15px]">
      <span class="label text-text-grey text-[16px] font-semibold mr-[14px]">{{
        props.title
      }}</span>
      <van-tag
        color="rgba(100, 129, 238, 0.22)"
        text-color="#6481EE"
        class="text-[12px] leading-[12px] rounded-[4px] py-[2px]"
        >{{ props.tag }}</van-tag
      >
    </div>
    <div
      v-if="props.info?.content"
      v-html="props.info?.content"
      class="text-[14px] mb-[15px]"
    ></div>
    <div v-for="(item, index) in props.info.answers" :key="index" class="mb-[10px] last:mb-0">
      <div class="relative">
        <div
          v-html="props.info?.studentAnswerList?.[index] || '未作答'"
          class="text-[14px] min-h-[37px] px-[10px] py-[7px] border rounded-[2px]"
          :class="[
            !checkRight(item.options || [], props.info?.studentAnswerList?.[index])
              ? 'text-[#EF6A6D] border-[#EF6A6D]'
              : 'text-[#41B981] border-[#41B981]'
          ]"
        ></div>
        <span
          class="iconfont text-[16px] absolute right-[2px] top-[0]"
          :class="
            checkRight(item.options, props.info?.studentAnswerList?.[index])
              ? 'text-[#41B981] iconduigou'
              : 'iconcuowu text-[#EF6A6D]'
          "
        ></span>
      </div>
      <div
        v-show="!checkRight(item.options || [], props.info?.studentAnswerList?.[index])"
        v-html="item.options?.join() || ''"
        class="text-[14px] min-h-[37px] px-[10px] py-[7px] border rounded-[2px] mt-[6px]"
        :class="['text-[#41B981] border-[#41B981]']"
      ></div>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>
