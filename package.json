{"name": "global-h5-v3", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --mode dev --host", "build:uat": "run-p type-check && vite build --mode uat", "build:test": "run-p type-check && vite build --mode test", "build": "run-p type-check && vite build --mode pro", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build --force", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "lint-staged": "lint-staged", "prepare": "husky", "commit": "git-cz"}, "engines": {"node": "^18.0.0 || ^20.9.0 || >=21.1.0"}, "dependencies": {"@microsoft/fetch-event-source": "^2.0.1", "@types/html2canvas": "^0.5.35", "@vscode/markdown-it-katex": "^1.1.1", "@vueuse/core": "^13.0.0", "amfe-flexible": "^2.2.1", "axios": "^1.8.3", "dayjs": "^1.11.11", "dompurify": "^3.2.4", "driver.js": "^1.3.5", "eruda": "^3.4.1", "highlight.js": "^11.11.1", "html2canvas": "^1.4.1", "katex": "^0.16.21", "konva": "^9.3.20", "lodash-es": "^4.17.21", "lottie-web": "^5.12.2", "markdown-it": "^14.1.0", "markdown-it-link-attributes": "^4.0.1", "mitt": "^3.0.1", "nprogress": "^0.2.0", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.1", "savml": "^1.0.89", "ua-parser-js": "^1.0.38", "va.basic.service.auth": "^0.1.11", "va.basic.service.user": "^0.1.477", "va.biz.service.collegePlan": "^0.1.300", "va.study.service.homework": "^0.6.408", "va.study.service.learntdetail": "^0.2.98", "va.study.service.volquiz": "^0.4.43", "vant": "^4.9.9", "vue": "3.5", "vue-konva": "^3.2.1", "vue-router": "^4.3.0", "vue3-lottie": "^3.3.1", "xgplayer": "^3.0.21"}, "devDependencies": {"@commitlint/cli": "^19.3.0", "@commitlint/config-conventional": "^19.2.2", "@rushstack/eslint-patch": "^1.8.0", "@tsconfig/node20": "^20.1.4", "@types/katex": "^0.16.7", "@types/lodash-es": "^4.17.12", "@types/markdown-it": "^14.1.2", "@types/markdown-it-link-attributes": "^3.0.5", "@types/node": "^20.12.5", "@types/nprogress": "^0.2.3", "@types/ua-parser-js": "^0.7.39", "@typescript-eslint/eslint-plugin": "^7.11.0", "@typescript-eslint/parser": "^7.11.0", "@vant/auto-import-resolver": "^1.2.1", "@vitejs/plugin-vue": "^5.0.4", "@vitejs/plugin-vue-jsx": "^3.1.0", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^13.0.0", "@vue/tsconfig": "^0.5.1", "autoprefixer": "^10.4.19", "commitizen": "^4.3.0", "cz-git": "^1.9.2", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.23.0", "husky": "^9.0.11", "lint-staged": "^15.2.5", "npm-run-all2": "^6.1.2", "postcss": "^8.4.38", "postcss-pxtorem": "^6.1.0", "prettier": "^3.2.5", "sass": "^1.77.2", "tailwindcss": "^3.4.3", "typescript": "~5.4.0", "unplugin-auto-import": "^0.17.6", "unplugin-vue-components": "^0.27.0", "vite": "^5.2.8", "vue-eslint-parser": "^9.4.2", "vue-tsc": "^2.0.11"}, "config": {"commitizen": {"path": "node_modules/cz-git"}}, "lint-staged": {"*.{js,ts,tsx,vue,html}": ["eslint --fix", "prettier --write"], "*.{cjs,json}": ["prettier --write"], "*.{scss,css}": ["prettier --write"], "*.md": ["prettier --write"]}}