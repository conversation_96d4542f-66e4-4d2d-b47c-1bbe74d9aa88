<script setup lang="ts">
import { onBeforeMount, ref, watch } from 'vue'
import { useGetComp } from '@/hooks'
import type { TQuizOutData } from 'va.study.service.volquiz'

const props = withDefaults(
  defineProps<{
    initQuizs?: any[]
  }>(),
  { initQuizs: () => [] }
)
type QuizItem = TQuizOutData & { studentAnswer?: string[] }
const quizs = ref<QuizItem[]>([])

watch(
  () => props.initQuizs,
  (newVal: any[]) => {
    quizs.value = JSON.parse(JSON.stringify(newVal || [])) as TQuizOutData[]
  },
  { deep: true, immediate: true }
)

defineExpose({
  quizs
})
</script>
<template>
  <div class="result-detail pb-[160px] bg-bg-grey">
    <div class="quizs-list">
      <div class="quiz-item mb-[12px]" v-for="(item, index) in quizs" :key="index">
        <component
          :is="useGetComp(item.quizType, true, false)"
          :title="'Question ' + (index + 1)"
          :faIndex="index"
          v-model="quizs[index]"
          v-model:studentAnswer="quizs[index].studentAnswer"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>
