<script setup lang="ts">
defineOptions({
  name: '<PERSON>I<PERSON>'
})
interface Props {
  text: number
  finish?: boolean
  isHalf?: boolean
}
const props = withDefaults(defineProps<Props>(), {
  text: 0,
  finish: false,
  isHalf: false
})
</script>
<template>
  <div
    class="quiz-item text-va-grey-text box-border text-center rounded-[10px] flex items-center justify-center"
    :class="[{ finish: props.finish, half: props.isHalf }]"
  >
    <span class="artfont text-[18px] font-[600]">{{ props.text }}</span>
  </div>
</template>

<style lang="scss" scoped>
.quiz-item {
  width: 52px;
  height: 40px;
  line-height: 40px;
  border: 1.5px solid #e9eaee;
  &.finish {
    color: #0256ff;
    background-color: #e2e9ff;
    border: none;
  }
  &.half {
    border: 2px solid #e2e9ff;
    color: #0256ff;
    background: linear-gradient(to right, #e2e9ff 50%, transparent 50%);
  }
}
</style>
