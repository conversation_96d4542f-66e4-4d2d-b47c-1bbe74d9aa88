import { useLocalStorage } from '@vueuse/core'
import $config from '@/config'
const cacheInfo = {
  [$config.jwtTokenKey]: ''
}

type LocalCacheValueType = typeof cacheInfo
type Keys = keyof LocalCacheValueType

export function useLocalCache() {
  function getCache<T extends Keys>(key: T): LocalCacheValueType[T] {
    return useLocalStorage(key as string, cacheInfo[key]).value
  }

  function setCache<T extends Keys>(key: T, value: LocalCacheValueType[T]) {
    useLocalStorage(key as string, cacheInfo[key]).value = value
  }

  function removeCache(key: Keys) {
    useLocalStorage(key as string, cacheInfo[key]).value = null
  }

  function clearCache() {
    localStorage.clear()
  }
  return { getCache, setCache, removeCache, clearCache }
}
