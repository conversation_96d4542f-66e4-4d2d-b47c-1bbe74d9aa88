<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, nextTick, triggerRef } from 'vue'
import { showLoadingToast, closeToast, showSuccessToast, showFailToast } from 'vant'
// @ts-ignore
import DrawToolBar from './DrawToolBar.vue'
// @ts-ignore
import DrawBoardContent from './DrawBoardContent.vue'
import type { ToolType } from './types'
import { getDefaultToolConfigs } from './config'

// 全局触摸模式状态
const globalTouchMode = ref<'auto' | 'none'>('auto')

// 切换触摸模式
const toggleTouchMode = () => {
  globalTouchMode.value = globalTouchMode.value === 'auto' ? 'none' : 'auto'
}

// 处理来自子组件的touchMode变化
const handleTouchModeChange = (newMode: 'auto' | 'none') => {
  globalTouchMode.value = newMode
}

defineOptions({
  name: 'DrawBoardDemo'
})

// 题目数据
const questions = ref([
  {
    id: 1,
    title: '计算下列表达式的值',
    content: '已知 a = 3, b = 4, c = 5，求：',
    items: ['(1) a² + b² = ?', '(2) √(a² + b²) = ?', '(3) c² - (a² + b²) = ?']
  },
  {
    id: 2,
    title: '几何图形题',
    content: '如图所示，在三角形ABC中：',
    items: ['(1) 画出三角形ABC的高', '(2) 计算三角形的面积', '(3) 标注各边长度']
  },
  {
    id: 3,
    title: '函数图像题',
    content: '已知函数 f(x) = x² - 2x + 1：',
    items: ['(1) 画出函数图像', '(2) 标注顶点坐标', '(3) 求函数的最小值']
  }
])

// 公共工具栏状态（基础绘图工具）
const commonCurrentTool = ref<ToolType>('brush')
const commonToolConfigs = ref(getDefaultToolConfigs())
const commonTools: ToolType[] = [
  'brush',
  'eraser',
  'circle',
  'rectangle',
  'background-line',
  'grid',
  'text'
]

// 题目工具栏配置（操作类工具）
const questionTools: ToolType[] = ['history-back', 'history-forward', 'clear', 'export']

// 为每个题目创建独立的工具栏状态
const questionToolbars = ref<Record<number, any>>({})
const questionHeights = ref<Record<number, number>>({})
const drawBoardRefs = ref<Record<number, any>>({})
const questionRefs = ref<Record<number, HTMLElement>>({})

// Loading状态管理
const exportingQuestions = ref<Set<number>>(new Set())
// 处理公共工具栏动作
const handleCommonToolbarAction = (action: string) => {
  // 将公共工具栏的动作应用到所有画板
  Object.values(drawBoardRefs.value).forEach((drawBoardRef) => {
    if (drawBoardRef && drawBoardRef.handleToolbarAction) {
      drawBoardRef.handleToolbarAction(action)
    }
  })
}

// 处理特定题目的工具栏动作
const handleToolbarAction = async (questionId: number, action: string) => {
  const drawBoardRef = drawBoardRefs.value[questionId]
  if (!drawBoardRef) return

  // 如果是导出操作，显示loading
  if (action === 'export') {
    exportingQuestions.value.add(questionId)

    const loadingToast = showLoadingToast({
      message: `正在导出第${questionId}题...`,
      forbidClick: true,
      duration: 0
    })

    try {
      // 执行导出操作
      await drawBoardRef.handleToolbarAction(action)

      // 导出成功
      closeToast()

      // 显示美化的成功提示
      showSuccessToast({
        message: `🎉 第${questionId}题导出成功！`,
        duration: 2000
      })
    } catch (error) {
      console.error('导出失败:', error)
      closeToast()

      // 显示失败提示
      showFailToast({
        message: '💥 导出失败，请重试',
        duration: 2000
      })
    } finally {
      // 清除loading状态
      exportingQuestions.value.delete(questionId)
    }
  } else {
    // 其他操作直接执行
    drawBoardRef.handleToolbarAction(action)
  }
}

// 检查题目是否正在导出
const isQuestionExporting = (questionId: number) => {
  return exportingQuestions.value.has(questionId)
}

// 计算指定题目的高度
const calculateQuestionHeight = (questionId: number) => {
  const questionRef = questionRefs.value[questionId]
  if (questionRef) {
    const rect = questionRef.getBoundingClientRect()
    questionHeights.value[questionId] = rect.height

    // 通知对应的画板组件重新计算
    const drawBoardRef = drawBoardRefs.value[questionId]
    if (drawBoardRef) {
      drawBoardRef.calculateQuestionHeight()
    }
  }
}

// 计算所有题目高度
const calculateAllQuestionHeights = () => {
  questions.value.forEach((question) => {
    calculateQuestionHeight(question.id)
  })
}

// 设置题目引用
const setQuestionRef = (questionId: number, el: any) => {
  if (el) {
    questionRefs.value[questionId] = el
  }
}

// 设置画板引用
const setDrawBoardRef = (questionId: number, el: any) => {
  if (el) {
    drawBoardRefs.value[questionId] = el
  }
}

// 获取题目包装元素引用
const getQuestionWrapRef = (questionId: number) => {
  return questionRefs.value[questionId] || null
}

// 设备旋转时重新计算尺寸
const handleOrientationChange = () => {
  setTimeout(() => {
    calculateAllQuestionHeights()
    // 通知所有画板重新计算宽度
    Object.values(drawBoardRefs.value).forEach((drawBoardRef) => {
      if (drawBoardRef && drawBoardRef.handleResize) {
        drawBoardRef.handleResize()
      }
    })
  }, 100)
}

// 为每个题目创建计算属性来确保响应式更新
const getQuestionCurrentTool = (questionId: number) => {
  return computed(() => {
    const toolbar = questionToolbars.value[questionId]
    return toolbar?.currentTool.value || 'brush'
  })
}

const getQuestionConfigs = (questionId: number) => {
  return computed(() => {
    const toolbar = questionToolbars.value[questionId]
    return toolbar?.toolConfigs.value || {}
  })
}

const initializeToolbars = () => {
  questions.value.forEach((question) => {
    questionToolbars.value[question.id] = {
      currentTool: 'brush',
      toolConfigs: getDefaultToolConfigs()
    }
  })
}

onMounted(() => {
  initializeToolbars()

  setTimeout(() => {
    calculateAllQuestionHeights()
  }, 100)

  // 监听设备旋转
  window.addEventListener('orientationchange', handleOrientationChange)
  window.addEventListener('resize', handleOrientationChange)
})

// 清理事件监听
onUnmounted(() => {
  window.removeEventListener('orientationchange', handleOrientationChange)
  window.removeEventListener('resize', handleOrientationChange)
})
</script>

<template>
  <div class="draw-board-demo">
    <!-- 页面标题 -->
    <div class="demo-header">
      <h1>📝 智能画板演示 - 多题目测试</h1>
    </div>

    <!-- 公共工具栏 -->
    <div class="common-toolbar-container">
      <DrawToolBar
        v-model="commonCurrentTool"
        :configs="commonToolConfigs"
        :tools="commonTools"
        @update:configs="commonToolConfigs = $event"
        @action="handleCommonToolbarAction"
      />

      <!-- 触摸模式切换按钮 -->
      <div class="touch-mode-toggle">
        <button
          class="touch-mode-button"
          :class="{ active: globalTouchMode === 'none' }"
          @click="toggleTouchMode"
        >
          <!-- 触摸模式图标 -->
          <div class="mode-icon">
            <svg
              v-if="globalTouchMode === 'auto'"
              width="18"
              height="18"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
            >
              <path d="M12 2v6m0 8v6m-6-6h6m2 0h6" />
              <circle cx="12" cy="12" r="3" />
            </svg>
            <svg
              v-else
              width="18"
              height="18"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
            >
              <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7z" />
              <circle cx="12" cy="9" r="2.5" />
            </svg>
          </div>

          <!-- 状态文本 -->
          <span class="mode-text">
            {{ globalTouchMode === 'auto' ? '滚动' : '绘图' }}
          </span>

          <!-- 状态指示器 -->
          <div class="mode-indicator">
            <div class="indicator-dot" :class="{ active: globalTouchMode === 'none' }"></div>
          </div>
        </button>
      </div>
    </div>

    <!-- 题目列表 -->
    <div class="questions-container">
      <div v-for="question in questions" :key="question.id" class="question-wrapper">
        <!-- 每个题目的独立工具栏 -->
        <div
          class="question-toolbar"
          :data-question-id="question.id"
          :class="{ exporting: isQuestionExporting(question.id) }"
        >
          <!-- 紧凑型工具栏布局 -->
          <div class="toolbar-compact">
            <!-- 左侧题目标识 -->
            <div class="question-indicator">
              <span class="question-number">{{ question.id }}</span>
              <span class="question-title">{{ question.title }}</span>
              <!-- 导出状态指示 -->
              <span v-if="isQuestionExporting(question.id)" class="export-status">导出中...</span>
            </div>

            <!-- 右侧工具栏 -->
            <div class="toolbar-content">
              <DrawToolBar
                v-if="questionToolbars[question.id]"
                :key="`toolbar-${question.id}`"
                v-model="questionToolbars[question.id].currentTool"
                v-model:configs="questionToolbars[question.id].toolConfigs"
                :tools="questionTools"
                :disabled="isQuestionExporting(question.id)"
                @action="(action) => handleToolbarAction(question.id, action)"
              />
            </div>
          </div>
        </div>

        <!-- 画板容器：题目和画板平级 -->
        <div class="board-container">
          <!-- 题目内容外层包装 -->
          <div class="question-content-wrap" :ref="(el) => setQuestionRef(question.id, el)">
            <!-- 题目内容 -->
            <div class="question-content">
              <h3>{{ question.title }}</h3>
              <p>{{ question.content }}</p>
              <div class="question-body">
                <div v-for="item in question.items" :key="item" class="question-item">
                  {{ item }}
                </div>
              </div>
            </div>
          </div>

          <!-- 画板组件：绝对定位覆盖题目 -->
          <DrawBoardContent
            v-if="questionToolbars[question.id]"
            :ref="(el) => setDrawBoardRef(question.id, el)"
            :question-height="questionHeights[question.id] || 0"
            :question-wrap-ref="getQuestionWrapRef(question.id)"
            :initial-answer-height="200"
            :min-answer-height="100"
            :grid-spacing="40"
            :current-tool="commonCurrentTool"
            :tool-configs="commonToolConfigs"
            :global-touch-mode="globalTouchMode"
            @touch-mode-change="handleTouchModeChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.draw-board-demo {
  min-height: 100vh;
  background: #fff;
  padding: 15px;
  padding-top: 80px; // 为fixed工具栏留出空间

  .demo-header {
    text-align: center;
    color: #3687ef;
    margin-bottom: 20px;

    h1 {
      font-size: 1rem;
      margin-bottom: 8px;
      // text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    p {
      font-size: 0.6rem;
      opacity: 0.9;
    }
  }

  .common-toolbar-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 60px;
    z-index: 1000;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    padding: 0 20px;
    flex-wrap: nowrap;

    // 确保公共工具栏在最上层，不被题目内容遮盖
    :deep(.draw-toolbar-pad) {
      z-index: 120;
      flex-shrink: 0; // 防止工具栏被压缩
    }
  }

  .touch-mode-toggle {
    display: flex;
    align-items: center;
    flex-shrink: 0; // 防止按钮被压缩
    position: absolute;
    left: 20px;
    top: 50%;
    transform: translateY(-50%);

    .touch-mode-button {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 10px 16px;
      background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
      border: 2px solid #e2e8f0;
      border-radius: 16px;
      color: #64748b;
      font-size: 13px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow:
        0 4px 12px rgba(0, 0, 0, 0.08),
        0 2px 6px rgba(0, 0, 0, 0.04);
      user-select: none;
      position: relative;
      overflow: hidden;
      white-space: nowrap; // 防止文字换行
      min-height: 46px; // 与工具栏保持相同高度

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
        transition: left 0.5s ease;
      }

      &:hover {
        transform: translateY(-2px);
        box-shadow:
          0 6px 18px rgba(0, 0, 0, 0.12),
          0 3px 9px rgba(0, 0, 0, 0.06);
        border-color: #cbd5e1;

        &::before {
          left: 100%;
        }
      }

      &.active {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        border-color: #3b82f6;
        color: white;
        box-shadow:
          0 6px 18px rgba(59, 130, 246, 0.4),
          0 3px 9px rgba(59, 130, 246, 0.2);

        &:hover {
          box-shadow:
            0 8px 24px rgba(59, 130, 246, 0.5),
            0 4px 12px rgba(59, 130, 246, 0.3);
        }
      }

      .mode-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 18px;
        height: 18px;
        flex-shrink: 0;
        transition: transform 0.3s ease;

        svg {
          width: 100%;
          height: 100%;
          transition: all 0.3s ease;
        }
      }

      .mode-text {
        font-weight: 600;
        white-space: nowrap;
        transition: all 0.3s ease;
      }

      .mode-indicator {
        display: flex;
        align-items: center;
        position: relative;

        .indicator-dot {
          width: 6px;
          height: 6px;
          border-radius: 50%;
          background: #94a3b8;
          transition: all 0.3s ease;
          position: relative;

          &::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: currentColor;
            opacity: 0;
            transition: opacity 0.3s ease;
          }

          &.active {
            background: #10b981;
            box-shadow: 0 0 8px rgba(16, 185, 129, 0.4);

            &::before {
              opacity: 0.2;
            }
          }
        }
      }

      &:active {
        transform: translateY(0);

        .mode-icon {
          transform: scale(0.9);
        }
      }
    }
  }

  .questions-container {
    max-width: 1000px;
    margin: 0 auto;
  }

  .question-wrapper {
    margin-bottom: 30px;
    border-radius: 12px;
    overflow: hidden;
    // box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);

    &:last-child {
      margin-bottom: 0;
    }
  }

  .question-toolbar {
    position: relative;
    background: white;
    padding: 8px 12px;
    // border-bottom: 1px solid #e0e0e0;
    z-index: 110;
    // box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);

    // 为每道题添加不同的主题色
    &[data-question-id='1'] {
      border-left: 3px solid #3b82f6;

      .question-number {
        background: #3b82f6;
        color: white;
      }
    }

    &[data-question-id='2'] {
      border-left: 3px solid #10b981;

      .question-number {
        background: #10b981;
        color: white;
      }
    }

    &[data-question-id='3'] {
      border-left: 3px solid #f59e0b;

      .question-number {
        background: #f59e0b;
        color: white;
      }
    }

    // 导出状态样式
    &.exporting {
      opacity: 0.7;
      pointer-events: none;

      .question-indicator {
        .export-status {
          color: #f59e0b;
          font-size: 11px;
          font-weight: 600;
          animation: pulse 1.5s ease-in-out infinite;
        }
      }

      .toolbar-content {
        opacity: 0.5;
      }
    }

    .toolbar-compact {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 12px;
    }

    .question-indicator {
      display: flex;
      align-items: center;
      gap: 8px;
      flex: 1;
      min-width: 0; // 允许文本截断

      .question-number {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 12px;
        flex-shrink: 0;
      }

      .question-title {
        color: #374151;
        font-weight: 500;
        font-size: 13px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        flex: 1;
      }

      .export-status {
        font-size: 11px;
        color: #f59e0b;
        font-weight: 600;
        white-space: nowrap;
      }
    }

    .toolbar-content {
      flex-shrink: 0;

      // 调整工具栏内部样式
      :deep(.draw-toolbar-pad) {
        position: relative;
        transform: none;
        top: auto;
        left: auto;

        .toolbar-container {
          margin: 0;
          padding: 4px 8px;
          gap: 3px;
          box-shadow: 0 1px 6px rgba(0, 0, 0, 0.1);
        }

        .tool-item {
          width: 24px;
          height: 24px;

          .tool-icon {
            font-size: 14px;
          }
        }
      }
    }
  }

  .board-container {
    position: relative;
    background: white;
    border-bottom: 1px solid #3687ef;
  }

  .question-content-wrap {
    position: relative;
    padding: 15px 20px;
    background: #fff;
    // border-bottom: 2px dashed #dee2e6;
    z-index: 1;

    .question-content {
      h3 {
        margin: 0 0 8px 0;
        color: #343a40;
        font-size: 13px;
        font-weight: 600;
      }

      p {
        margin: 6px 0;
        color: #6c757d;
        font-size: 11px;
        line-height: 1.3;
      }

      .question-body {
        margin: 12px 0;
        padding: 10px;
        background: rgba(255, 255, 255, 0.8);
        border-radius: 6px;
        // border-left: 3px solid #007aff;
      }

      .question-item {
        font-size: 12px;
        color: #495057;
        margin: 5px 0;
        font-weight: 500;
        padding: 3px 0;
      }
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .draw-board-demo {
    padding: 10px;
    padding-top: 70px; // 移动端为fixed工具栏留出空间

    .demo-header {
      margin-bottom: 15px;

      h1 {
        font-size: 0.9rem;
      }

      p {
        font-size: 0.55rem;
      }
    }

    // 移动端公共工具栏布局优化
    .common-toolbar-container {
      height: 50px; // 移动端高度调小
      gap: 12px;
      padding: 0 10px;
      justify-content: center; // 移动端工具栏保持居中

      // 当屏幕太小时的特殊处理
      @media (max-width: 480px) {
        height: 45px;
        padding: 0 60px 0 10px; // 为按钮留出左侧空间
      }
    }

    // 移动端触摸模式按钮适配
    .touch-mode-toggle {
      left: 10px; // 移动端左边距调小

      // 极小屏幕时的调整
      @media (max-width: 480px) {
        left: 8px;
      }

      .touch-mode-button {
        padding: 8px 12px;
        gap: 6px;
        border-radius: 14px;
        font-size: 12px;
        min-height: 40px; // 移动端调整高度

        // 极小屏幕时更紧凑
        @media (max-width: 480px) {
          padding: 6px 10px;
          min-height: 36px;
        }

        .mode-icon {
          width: 16px;
          height: 16px;
        }

        .mode-text {
          font-size: 11px;
        }

        .mode-indicator {
          .indicator-dot {
            width: 5px;
            height: 5px;

            &::before {
              width: 10px;
              height: 10px;
            }
          }
        }
      }
    }

    .question-wrapper {
      margin-bottom: 20px;
    }

    .question-toolbar {
      padding: 8px;
    }

    .question-content-wrap {
      padding: 12px 15px;

      .question-content {
        h3 {
          font-size: 12px;
        }

        p {
          font-size: 10px;
        }

        .question-item {
          font-size: 11px;
        }
      }
    }
  }
}

// iPad横屏适配
@media (orientation: landscape) and (min-width: 768px) {
  .draw-board-demo {
    padding-top: 75px; // iPad横屏时的工具栏间距

    .demo-header {
      h1 {
        font-size: 1.1rem;
      }

      p {
        font-size: 0.65rem;
      }
    }
  }

  .common-toolbar-container {
    height: 55px; // iPad横屏时的工具栏高度

    .touch-mode-toggle {
      .touch-mode-button {
        min-height: 42px;
      }
    }
  }
}

// iPad竖屏适配
@media (orientation: portrait) and (min-width: 768px) and (max-width: 1024px) {
  .draw-board-demo {
    padding-top: 75px;
  }

  .common-toolbar-container {
    height: 55px;

    .touch-mode-toggle {
      .touch-mode-button {
        min-height: 42px;
      }
    }
  }
}

// 动画效果
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }
}
</style>
