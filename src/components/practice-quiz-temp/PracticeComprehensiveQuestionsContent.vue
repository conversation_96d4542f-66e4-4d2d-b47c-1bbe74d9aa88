<script setup lang="ts">
defineOptions({
  name: 'PracticeComprehensiveQuestionsContent'
})
import PracticeMultiChoiceContent from './PracticeMultiChoiceContent.vue'
import PracticeSingleChoiceContent from './PracticeSingleChoiceContent.vue'
import PracticeOtherContent from './PracticeOtherContent.vue'
import type { TVolQuizOutData } from '@/services/practice/types'

export interface Props {
  currentQuizInfo: TVolQuizOutData
  parentIndex?: string
}

const props = withDefaults(defineProps<Props>(), {
  currentQuizInfo: () => ({}),
  parentNumber: ''
})

const genPathIndex = (index: number) => {
  return props.parentIndex ? `${props.parentIndex}-${index}` : `${index}`
}

const getQuizComponent = (type: number) => {
  switch (type) {
    case 1:
      return PracticeSingleChoiceContent
    case 6:
      return PracticeMultiChoiceContent
    case 3:
      return 'PracticeComprehensiveQuestionsContent'
    default:
      return PracticeOtherContent
  }
}
</script>

<template>
  <div class="quiz-content flex">
    <div class="quiz-title flex-shrink-0 mr-[6px]">
      <span class="">{{ props.currentQuizInfo?.quizSeq || props.currentQuizInfo?.quizSeqNo }}</span>
    </div>
    <div class="quiz-body flex-1">
      <van-image
        v-if="props.currentQuizInfo.imgContent"
        :src="props.currentQuizInfo.imgContent"
        v-preview-image
        fit="cover"
      />
      <div
        v-else
        v-preview-image
        class="qzitem-quiz break-word-legacy"
        v-html="props.currentQuizInfo.content || props.currentQuizInfo.htmlContent"
      ></div>
      <div class="sub-quiz-list mb-[6px]" v-if="props.currentQuizInfo.children?.length">
        <component
          v-for="(quiz, index) in props.currentQuizInfo.children"
          :is="getQuizComponent(quiz.quizType || 1)"
          :key="index"
          class="mb-[10px]"
          :parentIndex="genPathIndex(index)"
          :currentQuizInfo="quiz"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.quiz-content {
  font-size: 14px;
  .quiz-title {
    font-weight: 600;
    color: #333;
  }
}
</style>
