<script setup lang="ts">
import { ref } from 'vue'
import { Image as VanImage } from 'vant'
import { getImageUrl } from '@/utils/img'
import { useRoute } from 'vue-router'
import { useGlobal, useLoading } from '@/hooks'
import type { CollectDataList } from 'va.study.service.learntdetail'
import { SUBJECT_LIST, SUBJECT_SCORE_COLUMNS } from './config'
import { cloneDeep } from 'lodash-es'

const { globalProperties } = useGlobal()
const { startLoading, endLoading } = useLoading()
const logo = getImageUrl('document-info/logo.png')
const routes = useRoute()
const confirmLetterDetail = ref({}) as any

const confirmationClauses = ref<any[]>([])
const subjectExamScoreList = ref<any[]>([])
const subjectScoreColumns = ref(SUBJECT_SCORE_COLUMNS as any)

const getTypeList = async () => {
  try {
    startLoading()
    const res: any = await globalProperties.$api.SelectionConfirmLetter.confirmLetterByUid({
      uid: routes?.query?.uid as string
    })
    confirmLetterDetail.value = res

    const { collectDateList, studentCode } = res as any

    res.clauseList?.forEach((item: any) => {
      const typeObj = confirmationClauses.value.find((cc: any) => cc.type === item.type)

      if (typeObj) {
        typeObj.children?.push({
          code: item.code as string,
          content: item.content as string
        })
      } else {
        confirmationClauses.value.push({
          type: item.type as number,
          typeStr: item.typeStr as string,
          children: [
            {
              code: item.code as string,
              content: item.content as string
            }
          ]
        })
      }
    })

    if (!collectDateList?.length) {
      return
    }

    const lscsRes = await globalProperties.$api.collect.listStudentCollectScore({
      studentUid: studentCode,
      subjectsOnlyEstimated: false
    })

    const subjectRes = lscsRes?.records?.filter(
      (item: CollectDataList) => item.examName !== '英语培优'
    )

    const tempSubjectScoreList = cloneDeep(subjectRes).filter((item: any) => {
      const courseType = (SUBJECT_LIST as any)[item.examName]
      const DETAIL_LIST: any = {
        ibList: 'ibDetailList',
        satList: 'satDetailList'
      }
      item[courseType] = cloneDeep(item[courseType])?.filter((score: any) => {
        if (DETAIL_LIST[courseType]) {
          score[DETAIL_LIST[courseType]] = cloneDeep(score[DETAIL_LIST[courseType]]).filter(
            (ictf: any) => {
              return collectDateList.some(
                (cdl: any) => cdl.id === ictf.id && cdl.collectDateUid === ictf.collectDateUid
              )
            }
          )
          return score[DETAIL_LIST[courseType]]?.length
        } else {
          return collectDateList.some(
            (cd: any) => cd.id === score.id && cd.collectDateUid === score.collectDateUid
          )
        }
      })
      return item[courseType]?.length
    })

    subjectExamScoreList.value = tempSubjectScoreList?.map((item: any) => {
      if (!(SUBJECT_SCORE_COLUMNS as any)[item.examName]) {
        return
      }
      const examType = (SUBJECT_LIST as any)[item.examName]
      const { exam, examBoard, examBoardName, examName } = item
      const tableData = getTableData(item, examType) || item[examType]
      tableData.forEach((row: any) => {
        row.exam = exam
        row.examBoard = examBoard
      })
      return {
        exam,
        examBoard,
        examBoardName,
        examName,
        tableData
      }
    })
  } finally {
    endLoading()
  }
}

const getTableData = (item: any, examType: string) => {
  const tableData: any[] = []
  const DETAIL_LIST = {
    ibList: 'ibDetailList',
    satList: 'satDetailList'
  }
  if (['ibList', 'satList'].includes(examType)) {
    item[examType].forEach((table: any) => {
      tableData.push(table[(DETAIL_LIST as any)[examType]])
    })
    const newArr: any[] = []
    tableData.map((tableItem: any) => {
      tableItem.forEach((element: any) => {
        newArr.push(element)
      })
    })
    return newArr
  }
}

getTypeList()
</script>
<template>
  <div class="main-content">
    <div class="logo">
      <img :src="logo" alt="" />
    </div>
    <div class="header-title">选校确认函</div>
    <div class="student-info info-module">
      <div class="subtitle">学生信息</div>
      <div class="content">
        <span>{{ `学生姓名：${confirmLetterDetail.studentName}` }}</span>
        <span>{{ `在读年级：${confirmLetterDetail.grade || '--'}` }}</span>
        <span>{{ `在读学校：${confirmLetterDetail.school || '--'}` }}</span>
      </div>
    </div>
    <div v-if="subjectExamScoreList.length" class="info-module">
      <div class="subtitle">学科成绩</div>
      <div class="content">
        <div class="score-item-wrap">
          <div v-for="score in subjectExamScoreList" :key="score.exam" class="score-item">
            <div class="exam-info">{{ `${score.examName} ${score.examBoardName || ''}` }}</div>
            <div class="score-info">
              <div v-for="(td, index) in score.tableData" :key="index" class="score-info-item">
                <div
                  v-for="(column, colIndex) in subjectScoreColumns[score.examName]"
                  :key="colIndex"
                  class="field-info"
                >
                  <span class="label">{{ `${column.label}：` }}</span>
                  <div class="content">
                    {{ column.formatter ? column.formatter(td) : td[column.prop] }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="school-list info-module">
      <div class="subtitle">申请项目</div>
      <div
        v-for="apply in confirmLetterDetail.studentApplyList"
        :key="apply.applyRecordUid"
        class="school-item"
      >
        <table class="apply-school-table">
          <caption>
            {{
              apply.schoolName
            }}
          </caption>
          <tbody>
            <tr>
              <td :rowspan="apply.applyMajorList.some((aml: any) => aml.majorTag) ? 3 : 2">
                <van-image :src="apply.schoolLogeUrl" lazy-load fit="cover" />
              </td>
              <td>{{ apply.studyDirectionName }}</td>
              <td>{{ apply.degreeTypeTitle }}</td>
              <td>{{ `${apply.entranceYear}(${apply.entranceSeason})` }}</td>
            </tr>
            <tr>
              <td colspan="3">
                <div v-for="aml in apply.applyMajorList" :key="aml.majorUid" class="major-item">
                  <p>{{ aml.majorName }}</p>
                </div>
              </td>
            </tr>
            <tr v-if="apply.applyMajorList.some((aml: any) => aml.majorTag)">
              <td colspan="3">
                <div v-for="aml in apply.applyMajorList" :key="aml.majorUid" class="major-item">
                  <p v-if="aml.majorTag">{{ aml.majorTag.tagAliasLine }}</p>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <div class="informed-argee-module info-module">
      <div class="subtitle">知情同意</div>
      <div class="informed">
        <p>
          请仔细阅读本《选校确认函》（下称 “确认函”），确认信息无误。本确认函一经签署（点击下方
          “确认签署”
          按钮视为签署完成），代表学生及其家长（监护人）认可确认函中的申请方案。若学生未成年，本确认函必须由家长（监护人）签署。
        </p>
        <div v-for="cc in confirmationClauses" :key="cc.type" :class="`module-${cc.type}`">
          <div class="informed-sub-title">{{ cc.typeStr }}</div>
          <p v-for="clause in cc.children" :key="clause.code" v-html="clause.content"></p>
        </div>
        <div class="module-3">
          <div class="informed-sub-title">AI风险</div>
          <p>
            特别提醒，文书写作过程中，根据申请院校要求，强烈建议不要使用AI工具。如因使用AI工具导致权威检测机构Turnitin检测存在AI，须由学生自行降低AI率。因学生坚持递交使用AI工具生成或修改的文书导致的任何不利后果，由学生和家长（监护人）自行承担。
          </p>
        </div>
        <p>
          本确认函一经签署，因调整申请方案导致额外费用的产生或任何不利后果，由学生和家长（监护人）自行承担。
        </p>
      </div>
    </div>
    <div class="company">
      <div class="cn">唯寻国际教育</div>
      <div class="en">Vision Academy</div>
      <div v-if="confirmLetterDetail.confirmTime" class="confirm-date">
        {{ `签署时间：${confirmLetterDetail.confirmTime}` }}
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.main-content {
  height: calc(100% - 10px);
  padding: 0 10px;
  overflow-y: auto;
  box-sizing: border-box;
  .logo {
    margin-top: 20px;
    text-align: center;
    img {
      width: 140px;
      display: inline;
    }
  }
  .header-title {
    position: relative;
    margin: 25px 0;
    font-size: 19px;
    font-weight: 800;
    color: #333;
    text-align: center;
    &::before {
      position: absolute;
      top: 8px;
      left: 60px;
      width: 42px;
      height: 14px;
      background-image: url('@/assets/images/document-info/document-sign-title-icon.png');
      background-size: contain;
      content: '';
    }
    &::after {
      position: absolute;
      top: 8px;
      right: 60px;
      width: 42px;
      height: 14px;
      background-image: url('@/assets/images/document-info/document-sign-title-icon.png');
      background-size: contain;
      content: '';
      transform: rotateZ(180deg);
    }
  }
  .info-module {
    .subtitle {
      position: relative;
      font-size: 17px;
      font-weight: bold;
      line-height: 50px;
      color: #000000;
      text-align: center;
      &::before,
      &::after {
        position: absolute;
        top: 22px;
        width: 7px;
        height: 7px;
        background: linear-gradient(180deg, #8ebff7 0%, #558bed 100%);
        border-radius: 2px;
        content: '';
      }
      &::before {
        left: 230px;
      }
      &::after {
        right: 230px;
      }
    }
  }
  .student-info {
    .content {
      margin: 10px 0;
      font-size: 14px;
      color: #333;
      span {
        display: inline-block;
        width: 50%;
        &:last-child {
          width: 100%;
          margin-top: 25px;
        }
      }
    }
  }
  .school-list {
    .school-item {
      margin-bottom: 20px;
      overflow: hidden;
      border-radius: 6px;
    }
  }
  .apply-school-table {
    width: 100%;
    font-family: sans-serif;
    letter-spacing: 2px;
    border: 2px solid #cbdeff;
    border-collapse: collapse;
    box-shadow: 0 0 6px 0 rgba(85, 139, 237, 0.2);
    caption {
      width: 100%;
      padding: 8px 15px;
      font-size: 13px;
      font-weight: bold;
      color: #000;
      text-align: left;
      border: 2px solid #cbdeff;
      border-bottom: none;
      box-sizing: border-box;
      caption-side: top;
    }
    th,
    td {
      padding: 16px 10px;
      text-align: center;
      border: 2px solid #cbdeff;
    }
    tbody tr {
      td {
        font-size: 13px;
        font-weight: 400;
        line-height: 25px;
        color: #333;
        &:nth-child(1) {
          width: 94px;
        }
        &:nth-child(2) {
          width: 121px;
        }
        &:nth-child(3) {
          width: 121px;
        }
      }
    }
  }
  .informed-argee-module {
    color: #333;
    .informed {
      .informed-sub-title {
        text-align: center;
        font-size: 16px;
        font-weight: 600;
      }
      p {
        line-height: 26px;
        text-indent: 2em;
        font-size: 14px;
        margin: 10px 0;
      }
    }
  }
  .score-item-wrap {
    margin-bottom: 20px;
    .score-item {
      &:not(:last-child) {
        margin-bottom: 20px;
      }
      .exam-info {
        width: 100%;
        height: 38px;
        padding: 0 12px;
        font-size: 16px;
        line-height: 38px;
        color: #fff;
        text-align: center;
        background: #558bed;
        border-radius: 6px 6px 0 0;
        box-shadow: 2px 0 8px 0 #efefef;
        box-sizing: border-box;
      }
      .score-info {
        width: 100%;
        padding: 10px;
        font-size: 14px;
        border: 2px solid #cbdeff;
        border-top: none;
        box-sizing: border-box;
        .score-info-item {
          display: flex;
          flex-wrap: wrap;
          &:not(:last-child) {
            padding-bottom: 10px;
            margin-bottom: 10px;
            border-bottom: 1px solid #cbdeff;
          }
          .field-info {
            display: flex;
            width: 50%;
            line-height: 26px;
            .label {
              color: #606266;
            }
            .content {
              color: #333;
            }
          }
        }
      }
    }
  }
  .company {
    margin-top: 40px;
    font-size: 15px;
    font-weight: bold;
    color: #333;
    text-align: right;
    .confirm-date {
      font-size: 15px;
      font-weight: normal;
      color: #333;
    }
  }
}
</style>
