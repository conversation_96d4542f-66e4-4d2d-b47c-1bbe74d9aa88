<script setup lang="ts">
import { ref, computed, watch } from 'vue'
defineOptions({
  name: 'DragPopup'
})
interface Props {
  minHeight?: number | string
  maxHeight?: number | string
  initialHeight?: number | string
  modelValue?: number
  isFirstDoPracticeGuide?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  minHeight: '30vh',
  maxHeight: 600,
  initialHeight: '40vh',
  modelValue: 238,
  isFirstDoPracticeGuide: false
})

const detectUnit = (value: number | string): 'px' | 'vh' => {
  if (typeof value === 'string' && value.toLowerCase().endsWith('vh')) {
    return 'vh'
  }
  return 'px'
}

const convertToPixels = (value: number | string): number => {
  if (typeof value === 'number') return value
  const unit = detectUnit(value)
  const numValue = parseFloat(value)
  if (unit === 'vh') {
    return (window.innerHeight * numValue) / 100
  }
  return numValue
}

const emit = defineEmits(['update:modelValue'])

const dragHandler = ref<HTMLElement | null>(null)
const currentHeight = ref(convertToPixels(props.modelValue || props.initialHeight))
const startY = ref(0)
const startHeight = ref(0)
const isDragging = ref(false)

watch(
  () => props.initialHeight,
  (newVal) => {
    currentHeight.value = convertToPixels(newVal)
    emit('update:modelValue', currentHeight.value)
  },
  {
    immediate: true
  }
)

const onTouchStart = (e: TouchEvent) => {
  isDragging.value = true
  startY.value = e.touches[0].clientY
  startHeight.value = currentHeight.value
}

const onTouchMove = (e: TouchEvent) => {
  if (!isDragging.value) return

  const deltaY = e.touches[0].clientY - startY.value
  const newHeight = Math.min(
    Math.max(startHeight.value - deltaY, convertToPixels(props.minHeight)),
    convertToPixels(props.maxHeight)
  )

  if (newHeight !== currentHeight.value) {
    currentHeight.value = newHeight
    emit('update:modelValue', newHeight)
  }
}

const onTouchEnd = () => {
  isDragging.value = false
}

const dragHandlerStyle = computed(() => ({
  transition: isDragging.value ? 'none' : 'transform 0.3s'
}))
</script>
<template>
  <div
    class="drag-popup-container"
    :class="{ dragging: isDragging }"
    :style="{ height: `${currentHeight}px` }"
  >
    <div
      class="drag-handler"
      ref="dragHandler"
      :style="dragHandlerStyle"
      @touchstart="onTouchStart"
      @touchmove="onTouchMove"
      @touchend="onTouchEnd"
    >
      <div
        class="drag-handler-inner w-[100%] py-[10px] flex justify-center"
        :class="[{ 'bg-white/80': props.isFirstDoPracticeGuide }]"
      >
        <slot name="handler">
          <img
            src="@/assets/images/practice/handle-bar.png"
            alt="handlebar"
            class="handler-indicator"
          />
        </slot>
      </div>
    </div>
    <div class="content-wrapper">
      <slot name="content"></slot>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.drag-popup-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #f4f6fa;
  border-radius: 20px 20px 0 0;
  border: 2px solid #d4d6de;
  box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.1);
  transform: translateZ(0);
  z-index: 100;
  &.dragging {
    user-select: none;
  }
}

.drag-handler {
  position: absolute;
  top: -20px;
  left: 0;
  right: 0;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: ns-resize;
  z-index: 100;
  touch-action: none;
  user-select: none;
  -webkit-user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.handler-indicator {
  width: 82px;
  height: 18px;
  transition: opacity 0.2s;
  object-fit: contain;
}

.content-wrapper {
  height: 100%;
  // overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  box-sizing: border-box;
  touch-action: pan-y;
  overscroll-behavior: contain;
  position: relative;
  z-index: 1;
}

.drag-popup-container.dragging .content-wrapper {
  pointer-events: none;
}

// body.drag-popup-open {
//   overflow: hidden;
//   touch-action: none;
// }
// @media (max-width: 768px) {
//   .drag-handler {
//     height: 48px;
//   }

//   .handler-indicator {
//     width: 40px;
//   }
// }
</style>
