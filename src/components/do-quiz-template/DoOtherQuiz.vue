<script setup lang="ts">
interface TrunkItem {
  aoVal: string
  content: string
}
export interface QuizProps {
  title: string
  tag?: string
  faIndex: number
  modelValue: {
    studentAnswer?: string[]
    content?: string
    describe?: string
    options: TrunkItem[]
  }
}
const props = withDefaults(defineProps<QuizProps>(), {
  tag: ''
})
const emit = defineEmits(['update:studentAnswer'])
const chooseAnswer = (option: string) => {
  const answers = [option]
  emit('update:studentAnswer', answers)
}
</script>
<template>
  <div class="blank-filling-wrap bg-white px-[15px] pt-[15px] pb-[20px] box-border">
    <div class="title flex items-center mb-[15px]">
      <span class="label text-text-grey text-[16px] font-semibold mr-[14px]">{{
        props.title
      }}</span>
      <van-tag
        color="rgba(100, 129, 238, 0.22)"
        text-color="#6481EE"
        class="text-[12px] leading-[12px] rounded-[4px] py-[2px]"
        >{{ props.tag }}</van-tag
      >
    </div>
    <div v-html="props.modelValue?.content || ''" class="text-[14px] mb-[15px]"></div>
    <!-- <div
      v-for="(item, index) in props.modelValue?.options"
      :key="index"
      class="mb-[10px] px-[4px] py-[6px] last:mb-0 flex items-center"
      @click="chooseAnswer(item.aoVal)"
    >
      <span
        class="inline-block h-[20px] w-[20px] leading-[20px] rounded-[50%] text-center text-[14px] mr-[10px]"
        :class="[
          props.modelValue.studentAnswer?.some((v) => item.aoVal === v)
            ? 'bg-[#6481EE] text-white'
            : 'bg-[#eee] text-[#646B77]'
        ]"
        >{{ item.aoVal }}</span
      >
      <div v-html="item.content" class="text-[14px]"></div> -->
    <!-- <span
        class="text-[14px] ml-[10px]"
        :class="'iconfont iconduigou text-[#6481EE]'"
        v-show="props.modelValue.studentAnswer?.some((v) => item.aoVal === v)"
      ></span> -->
    <!-- </div> -->
  </div>
</template>

<style lang="scss" scoped></style>
