<script setup lang="ts">
import { computed } from 'vue'

defineOptions({
  name: 'VaMulSelectBtn'
})

interface Props {
  modelValue: string[]
  options: { label: string; value: string }[]
  multiple?: boolean
  customClass?: string
}

const props = withDefaults(defineProps<Props>(), {
  multiple: false,
  customClass: ''
})

const emit = defineEmits<{
  (e: 'update:modelValue', value: string[]): void
}>()

const selectedValue = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const handleSelect = (value: string) => {
  const selected = Array.isArray(selectedValue.value) ? selectedValue.value : []
  if (props.multiple) {
    const index = selected.indexOf(value)
    if (index > -1) {
      selected.splice(index, 1)
    } else {
      selected.push(value)
    }
    selectedValue.value = [...selected]
  } else {
    selectedValue.value = selected.includes(value) ? [] : [value]
  }
  navigator?.vibrate?.(50)
}

const isSelected = (value: string) => {
  const selected = Array.isArray(selectedValue.value) ? selectedValue.value : []
  return selected.includes(value)
}
</script>

<template>
  <div class="va-mul-select-btn mr-[-8px]" :class="customClass">
    <div
      v-for="option in options"
      :key="option.value"
      class="va-mul-select-btn__item text-[17px] text-va-grey-text bg-white rounded-[10px] px-[31px] py-[10px] border boder-va-line2 artfont font-bold inline-flex items-center justify-center"
      :class="{ 'is-selected': isSelected(option.value) }"
      @click="handleSelect(option.value)"
    >
      {{ option.label }}
    </div>
  </div>
</template>

<style lang="scss" scoped>
.va-mul-select-btn {
  display: flex;
  flex-wrap: wrap;
  &__item {
    margin-right: 8px;
    margin-bottom: 8px;
  }

  &__item {
    cursor: pointer;
    user-select: none;
    box-shadow: 0px 2px 0px #e9ebf1;
    transition: all 0.2s ease;

    &.is-selected {
      color: #0256ff;
      background-color: #e2e9ff;
      box-shadow: 0px 2px 0px 0px #6c97ff;
      border-color: #6c97ff;
    }
  }
}
</style>
