import { useLocalStorage } from '@vueuse/core'
import axios from 'axios'

const instance = axios.create({})

instance.interceptors.request.use((config) => {
  const { value: token } = useLocalStorage('token', '')
  if (config.headers && token) {
    config.headers['x-token'] = token
  }
  return config
})

instance.interceptors.response.use(
  (response) => {
    const { data: _data } = response
    return _data
  },
  (err) => {
    showToast({
      message: '网络繁忙',
      duration: 1000
    })
  }
)

export default instance
