<script setup lang="ts">
import { computed, markRaw, onMounted, ref } from 'vue'
import { showToast } from 'vant'
import { useRoute, useRouter } from 'vue-router'
import DragPopup from '@/components/drag-popup/index.vue'
import config from '@/config'
import type {
  PagePracticeAnswerDetailReqVO,
  QuizWrongBookPageReqDTO,
  SubmitWrongBookAnswerReqDTO,
  TVolQuizDataVO,
  TVolQuizOutData,
  WrongBookBaseReq
} from '@/services/practice/types'
import { useGlobal, useLoading, useRegWindowMethod, usePhotoCount } from '@/hooks'
import VaBaseTab from '@/components/base-ui/VaBaseTab.vue'
import MyAnswerContent from './sub-practice-detail/MyAnswerContent.vue'
import RightAnswerContent from './sub-practice-detail/RightAnswerContent.vue'
import QuizAnlysisContent from './sub-practice-detail/QuizAnlysisContent.vue'
import AiChatContent from './sub-practice-detail/AiChatContent.vue'
import VideoAnalysisContent from './sub-practice-detail/VideoAnalysisContent.vue'
import MulUploadPhotos from '@/components/upload-photo/MulUploadPhotos.vue'
import VaMulSelectBtn from '@/components/base-ui/VaMulSelectBtn.vue'
import EmptyData from '@/components/empty-data/index.vue'
import { getImageUrl } from '@/utils/img'
import { ensureUrlProtocol } from '@/utils/tool'
import type { DoQuizItemType, QuizAnswerMapType } from './types/dopractice'
import { TransferToFlutter, TransferType } from '@/utils/msg'
import dayjs from 'dayjs'
import { buriedPointsMap } from '@/model/buried-point'

const { globalProperties } = useGlobal()

const router = useRouter()
const currentDragHeight = ref(0)
const currentQuizNumber = ref(1)
const totalQuizCount = ref(0)
const currentVideoList = ref<string[]>([])
const { startLoading, endLoading, loading } = useLoading()
const tabOptions = [
  { label: '我的作答', value: 'MyAnswerContent', comp: markRaw(MyAnswerContent) },
  { label: '正确答案', value: 'RightAnswerContent', comp: markRaw(RightAnswerContent) },
  { label: '题目解析', value: 'QuizAnlysisContent', comp: markRaw(QuizAnlysisContent) }
]
const quizAnswersMap = ref<QuizAnswerMapType>({}) //
const currentTab = ref('MyAnswerContent')
const showAiChat = ref(false)
const showVideoAnalysis = ref(false)
const showModifyQuiz = ref(false)
const currentStartTime = ref(dayjs().format('YYYY-MM-DD HH:mm:ss'))

// 使用照片数量管理 hook
const { restPhotoCountLimit, setCurrentPhotoCount } = usePhotoCount(9)

const currentUploadQuizUid = ref('')
const quizs = ref<TVolQuizDataVO[]>([])

const isAlwaysModify = computed(() => {
  return routeQueryInfo.value?.modifyFirst === '1'
})

const routeQueryInfo = computed(() => {
  const route = useRoute()
  return route?.query
})

const navTitle = computed(() => {
  if (!quizs?.value?.length) {
    return ''
  }
  return `${currentQuizNumber.value}/${totalQuizCount.value}`
})

const isSubmitQuiz = computed(() => {
  return [1, 2, 9].includes(currentQuiz.value.answerState as number)
})

const hasAnswer = computed(() => {
  return Object.values(quizAnswersMap.value)?.some((answer) => answer.userAnswerList?.length)
})

const renderCom = computed(() => {
  return tabOptions.find((item) => item.value === currentTab.value)?.comp
})

const flattenQuizData = (quiz: TVolQuizDataVO) => {
  if (!quiz) return []

  const result: TVolQuizDataVO[] = []
  const clonedQuiz = JSON.parse(JSON.stringify(quiz)) as TVolQuizDataVO

  if (clonedQuiz.quizType === 3 && clonedQuiz.children?.length) {
    clonedQuiz.children.forEach((child) => {
      result.push(...flattenQuizData(child))
    })
  } else {
    result.push(clonedQuiz)
  }

  return result
}

const currentQuiz = computed(() => {
  return quizs.value?.[0] || {}
})

const flattenedQuizzes = computed(() => {
  return flattenQuizData(currentQuiz.value)
})

const paddingTop = computed(() => {
  const appInfoStr = localStorage.getItem(config.appInfoKey)
  if (!appInfoStr) return '44px'
  const appInfo = JSON.parse(appInfoStr)
  return `${Number(appInfo.safeAreaTop) + 44}px`
})

// 返回上一页
const goBack = () => {
  router.back()
}

const generateQuizAnswerObject = (quiz: TVolQuizDataVO) => {
  const result: Record<string, any> = {}
  const processQuiz = (currentQuiz: TVolQuizOutData) => {
    if (currentQuiz.quizType === 3 && currentQuiz.children?.length) {
      currentQuiz.children.forEach((childQuiz) => {
        processQuiz(childQuiz)
      })
    } else {
      result[currentQuiz.uid as string] = {
        quizType: currentQuiz.quizType,
        quizUid: currentQuiz.uid,
        quizVersion: currentQuiz.objectVersion,
        userAnswerList: showModifyQuiz.value ? [] : (currentQuiz as any)?.studentAnswerList || []
      }
    }
  }

  processQuiz(quiz)
  return result
}

const processQuizData = (
  quizList: TVolQuizDataVO[],
  total: number,
  setCurrentTab: boolean = false
) => {
  quizs.value = quizList
  totalQuizCount.value = total
  if (!setCurrentTab) {
    showModifyQuiz.value = isAlwaysModify.value && currentQuiz.value.answerResult === 1
  }
  quizAnswersMap.value = quizList.reduce<QuizAnswerMapType>((acc, quiz) => {
    return { ...acc, ...generateQuizAnswerObject(quiz) }
  }, {})
}

const getQuizInfo = async (setCurrentTab = false) => {
  const isPractice = routeQueryInfo.value.practiceRecordUid
  startLoading()
  try {
    if (isPractice) {
      const params: PagePracticeAnswerDetailReqVO = {
        pageNumber: currentQuizNumber.value,
        practiceRecordUid: (routeQueryInfo.value.practiceRecordUid as string) || undefined
      }

      // 如果从queryParams里面能拿到fetchWrong字段，则添加到请求参数中
      if (routeQueryInfo.value.fetchWrong !== undefined) {
        params.fetchWrong = Number(routeQueryInfo.value.fetchWrong)
      }
      const { data } =
        await globalProperties.$http.Practice.VolQuizManagerController.getPracticeAnswerDetail(
          params
        )
      processQuizData(data?.quizList || [], data?.total || 0, setCurrentTab)
    } else {
      const params: QuizWrongBookPageReqDTO = {
        pageNumber: currentQuizNumber.value,
        exam: routeQueryInfo.value.exam ? Number(routeQueryInfo.value.exam) : undefined,
        subject: routeQueryInfo.value.subject ? Number(routeQueryInfo.value.subject) : undefined,
        examBoard: routeQueryInfo.value.examBoard
          ? Number(routeQueryInfo.value.examBoard)
          : undefined,
        rectifyState: routeQueryInfo.value.rectifyState as string,
        pageSize: 1,
        paper: routeQueryInfo.value.paper as string
      }

      const { data } =
        await globalProperties.$http.Practice.WrongBookAnswerController.wrongBookPage(params)
      processQuizData(data?.records || [], data?.totalCount || 0, setCurrentTab)
    }
  } finally {
    endLoading()
  }
}

const handlePrevQuiz = () => {
  if (currentQuizNumber.value > 1) {
    currentQuizNumber.value--
    getQuizInfo()
  }
}
const handleNextQuiz = () => {
  if (currentQuizNumber.value < totalQuizCount.value) {
    currentQuizNumber.value++
    getQuizInfo()
  }
}

const askAi = () => {
  showAiChat.value = true
  showVideoAnalysis.value = false
}

const handleModifyQuiz = () => {
  showModifyQuiz.value = true
  currentStartTime.value = dayjs().format('YYYY-MM-DD HH:mm:ss')
  // TODO 方便测试 注释
  // quizAnswersMap.value = quizs.value?.reduce<QuizAnswerMapType>((acc, quiz) => {
  //   return { ...acc, ...generateQuizAnswerObject(quiz) }
  // }, {})
}

// 当前答题对象
const quizAnswerList = computed(() => {
  if (!currentQuiz.value) return []
  const result: DoQuizItemType[] = []

  const processQuiz = (quiz: TVolQuizOutData) => {
    if (quiz.quizType === 3 && quiz.children?.length) {
      quiz.children.forEach((childQuiz) => {
        processQuiz(childQuiz)
      })
    } else {
      result.push({
        quizSeqNo: quiz.quizSeq || quiz.quizSeqNo,
        quizUid: quiz.uid as string,
        quizType: quiz.quizType as number,
        quizVersion: quiz.objectVersion as number,
        userAnswerList: quizAnswersMap.value[quiz.uid as string]?.userAnswerList || [],
        score: quiz.score,
        options: quiz.options?.map((v) => ({ label: v.aoVal, value: v.aoVal }))
      })
    }
  }
  processQuiz(currentQuiz.value)
  return result
})

const goVideoAnalysis = (quizItem: TVolQuizDataVO) => {
  currentVideoList.value = (quizItem.avAnalysis || [])
    .map((url) => ensureUrlProtocol(url))
    .filter(Boolean)
  showVideoAnalysis.value = true
  showAiChat.value = false
}

const selectPic = (quizUid?: string) => {
  try {
    // 使用项目中已有的通信工具
    const picCount = (quizAnswersMap.value[quizUid as string]?.userAnswerList || []).length
    setCurrentPhotoCount(picCount) // 获取最新的剩余数据

    if (restPhotoCountLimit.value <= 0) {
      showToast('已达到最大上传数量')
      return
    }

    currentUploadQuizUid.value = quizUid || ''
    TransferToFlutter({
      type: TransferType.getPhotoListCrop,
      data: {
        title: '选择图片',
        url: '',
        max: restPhotoCountLimit.value
      }
    })
    // 显示提示信息
    showToast('已请求打开相册')
  } catch (error) {
    showToast('调用相册功能失败')
  }
}
const removePic = (quizUid: string, idx?: number) => {
  const userAnswerList = quizAnswersMap.value[quizUid]?.userAnswerList || []
  if (idx === undefined) {
    return
  }
  userAnswerList.splice(idx, 1)
  quizAnswersMap.value[quizUid as string] = {
    ...quizAnswersMap.value[quizUid as string],
    userAnswerList
  }
}

const getPhotoInfos = (allFile: any) => {
  const preUserAnswerList = quizAnswersMap.value[currentUploadQuizUid.value]?.userAnswerList || []
  const picList = typeof allFile?.url === 'string' ? [allFile?.url] : [...(allFile as string[])]
  const userAnswerList = preUserAnswerList.concat(picList)
  quizAnswersMap.value[currentUploadQuizUid.value] = {
    ...quizAnswersMap.value[currentUploadQuizUid.value],
    userAnswerList
  }
}

const updateChooseAnswer = (params: any) => {
  quizAnswersMap.value[params.quizUid] = {
    ...quizAnswersMap.value[params.quizUid],
    userAnswerList: params.value
  }
}

const handleRemoveWrongQuiz = async () => {
  try {
    await showConfirmDialog({
      title: '提示',
      message: `确认将题目移除错题本么？`,
      confirmButtonColor: '#326eff'
    })
    const params: WrongBookBaseReq = {
      wrongBookUid: currentQuiz.value.wrongBookUid || ''
    }
    startLoading()
    const res =
      await globalProperties.$http.Practice.WrongBookAnswerController.removeWrongBook(params)
    if (res.success) {
      showNotify({ type: 'success', message: '移除成功' })
      currentQuizNumber.value = 1
      getQuizInfo()
    }
  } catch (error) {
    endLoading()
  }
}

const submitQuiz = async () => {
  const { quizType } = currentQuiz.value
  const params: SubmitWrongBookAnswerReqDTO = {
    wrongBookRecordUid: currentQuiz.value.wrongBookUid as string,
    startTime: currentStartTime.value,
    submitTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    quizUid: currentQuiz.value.uid,
    quizVersion: currentQuiz.value.objectVersion,
    userAnswerList: quizType === 3 ? undefined : quizAnswerList.value?.[0]?.userAnswerList,
    userQuizAnswerDetailList:
      quizType === 3
        ? quizAnswerList.value.map((v) => ({
            quizUid: v.quizUid,
            quizVersion: v.quizVersion,
            userAnswerList: v.userAnswerList
          }))
        : undefined
  }
  try {
    startLoading()
    const res =
      await globalProperties.$http.Practice.WrongBookAnswerController.wrongBookSubmitQuizAnswer(
        params
      )
    if (res.code === 'success') {
      return Promise.resolve()
    } else {
      return Promise.reject(res.message)
    }
  } catch (e) {
    return Promise.reject(e)
  } finally {
    endLoading()
  }
}

const correctQuiz = async () => {
  if (!hasAnswer.value) {
    return
  }
  try {
    await submitQuiz()
    showNotify({
      type: 'success',
      message: '提交成功'
    })
    showModifyQuiz.value = false
    getQuizInfo()
  } catch (error) {
    console.log(error)
  }
}

onMounted(() => {
  globalProperties.$aplusPush.pushEvent({ eventCode: buriedPointsMap.practice_dowrongquiz })
  currentQuizNumber.value = routeQueryInfo.value?.pageNumber
    ? Number(routeQueryInfo.value.pageNumber)
    : 1
  getQuizInfo()
})

useRegWindowMethod([{ methodName: 'getPhotoListCrop', method: getPhotoInfos }])
</script>

<template>
  <div class="practice-detail" :style="{ paddingTop }">
    <common-nav
      :leftArrow="true"
      :showBackground="true"
      title=" "
      style="background-color: #f3f5f8"
    >
      <template #title>
        <span>{{ navTitle }}</span>
      </template>
      <template #right>
        <span
          class="iconfont iconshanchu text-va-grey-text text-[20px]"
          @click="handleRemoveWrongQuiz"
          v-show="totalQuizCount"
        ></span>
      </template>
    </common-nav>
    <!-- 题目展示 -->
    <template v-if="totalQuizCount">
      <div>
        <div
          class="rounded-[10px] mx-[10px] bg-white quizs-container my-[20px]"
          :style="{ paddingBottom: `${currentDragHeight + 50}px` }"
        >
          <PracticeMainQuizContent
            :currentIndex="currentQuizNumber - 1"
            :currentQuizInfo="currentQuiz"
          />
        </div>
        <DragPopup v-model="currentDragHeight" max-height="80vh">
          <template #content>
            <div class="op-wrap flex flex-col h-[100%]" v-if="!showAiChat && !showVideoAnalysis">
              <div class="do-quizs-op-wrap flex-1 p-[16px] pt-[0] overflow-y-auto">
                <template v-if="!showModifyQuiz">
                  <div
                    class="tabs-wrap flex justify-center sticky top-0 z-[100] mx-[-16px] py-[16px] bg-[#f4f6fa] rounded-t-[20px]"
                  >
                    <VaBaseTab :options="tabOptions" v-model="currentTab" class="w-[268px]" />
                  </div>
                  <div
                    class="mb-[16px] text-center text-va-grey-text-2 text-[12px]"
                    v-show="currentTab !== 'RightAnswerContent'"
                  >
                    批改和解析结果均由AI提供，结果仅供参考
                  </div>
                  <component
                    :is="renderCom"
                    :no="currentQuizNumber"
                    :flattenedQuizzes="flattenedQuizzes"
                    :showModifyBtn="true"
                    @go-video-analysis="goVideoAnalysis"
                    @refresh-quiz="() => getQuizInfo(true)"
                    @modify-quiz="handleModifyQuiz"
                  />
                </template>
                <template v-else>
                  <div
                    class="do-quiz-item mb-[20px] pt-[16px]"
                    v-for="item in quizAnswerList"
                    :key="item.quizUid"
                  >
                    <div class="mb-[6px] text-va-grey-text flex items-center">
                      <span class="text-[14px] font-bold mr-[6px] align-middle">{{
                        item.quizSeqNo || currentQuizNumber
                      }}</span>
                      <span class="text-va-grey-text text-[12px]">{{
                        item.score ? ` (${item.score}分) ` : ''
                      }}</span>
                    </div>
                    <VaMulSelectBtn
                      v-if="[1, 6].includes(item.quizType as number)"
                      :options="item.options || []"
                      :multiple="[6].includes(item.quizType as number)"
                      :model-value="item.userAnswerList || []"
                      @update:model-value="
                        (value) => updateChooseAnswer({ quizUid: item.quizUid, value })
                      "
                    />
                    <MulUploadPhotos
                      v-else
                      class="mb-[6px]"
                      :pics="item.userAnswerList || []"
                      @select="selectPic(item.quizUid)"
                      @remove="(idx) => removePic(item.quizUid as string, idx)"
                    />
                  </div>
                </template>
              </div>
              <div
                v-show="totalQuizCount"
                class="btns-wrap border-t-[2px] border-[#E6E9F3] flex-shrink-0 flex justify-between items-center px-[16px] py-[14px]"
              >
                <div class="flex items-center">
                  <VaBaseButton
                    :disabled="currentQuizNumber <= 1"
                    type="text"
                    text="上一题"
                    class="quiz-btn"
                    @click="handlePrevQuiz"
                  />
                </div>
                <div class="flex items-center" v-show="!showModifyQuiz">
                  <div class="ai-helper inline-flex items-center mr-[12px]" @click="askAi">
                    <img
                      :src="getImageUrl('practice/ask-va.png')"
                      alt=""
                      class="w-[49px] h-[41px] inline-flex mr-[4px]"
                    />
                    <span class="text-[14px] text-va-grey-text font-[600]">问小寻</span>
                  </div>
                </div>
                <div class="flex items-center">
                  <VaBaseButton
                    :disabled="currentQuizNumber >= totalQuizCount"
                    text="下一题"
                    class="quiz-btn"
                    @click="handleNextQuiz"
                  />
                </div>
                <div class="flex items-center" v-show="showModifyQuiz">
                  <VaBaseButton
                    text="提交答案"
                    :disabled="!hasAnswer"
                    class="quiz-btn"
                    @click="correctQuiz"
                  />
                </div>
              </div>
            </div>
            <AiChatContent
              v-if="showAiChat && currentQuiz.uid"
              @close="showAiChat = false"
              :currentQuiz="currentQuiz"
              :flattenedQuizzes="flattenedQuizzes"
            />
            <VideoAnalysisContent
              v-show="showVideoAnalysis"
              :showVideoAnalysis="showVideoAnalysis"
              :currentVideoList="currentVideoList"
              @close="showVideoAnalysis = false"
            />
          </template>
        </DragPopup>
      </div>
    </template>
    <EmptyData v-if="!loading && !totalQuizCount" />
  </div>
</template>

<style lang="scss" scoped>
.practice-detail {
  min-height: 100vh;
  background-color: #f3f5f8;
}
.quiz-btn {
  font-size: 14px;
  padding: 8px 24px;
}
</style>
