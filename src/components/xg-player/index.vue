<script setup lang="ts">
import { ref, watch, onMounted, onBeforeUnmount } from 'vue'
import Player from 'xgplayer'
import 'xgplayer/dist/index.min.css'

defineOptions({
  name: 'XgPlayer'
})

const props = defineProps({
  src: {
    type: String,
    required: true
  },
  options: {
    type: Object,
    default: () => ({})
  }
})

const events = ['play', 'pause', 'ended', 'error'] as const
const eventHandlers = {} as Record<(typeof events)[number], () => void>
const emit = defineEmits(['ready', 'play', 'pause', 'ended', 'error'])

const containerRef = ref<HTMLElement | null>(null)
const player = ref<Player | null>(null)

onMounted(() => {
  requestAnimationFrame(initPlayer)
})

onBeforeUnmount(() => {
  destroyPlayer()
})

// 监听 src 变化 (优化触发逻辑)
watch(
  () => props.src,
  (newVal, oldVal) => {
    if (newVal !== oldVal) changeSource(newVal)
  }
)

const initPlayer = () => {
  destroyPlayer()
  if (!containerRef.value) return
  player.value = new Player({
    el: containerRef.value,
    url: props.src,
    videoInit: true,
    playbackRate: false,
    download: false,
    controls: {
      initShow: true,
      autoHide: false
    },
    fluid: true,
    lang: 'zh-cn',
    ...props.options
  })

  events.forEach((event) => {
    const handler = () => emit(event)
    eventHandlers[event] = handler
    player.value?.on(event, handler)
  })

  player.value.on('ready', () => emit('ready', player.value))
}

const destroyPlayer = () => {
  if (player.value) {
    player.value.pause()
    // 清理所有事件监听
    events.forEach((event) => {
      player.value?.off(event, eventHandlers[event])
    })
    player.value.destroy()
    player.value = null
  }
}

const changeSource = (newSrc: string) => {
  if (player.value) {
    player.value.src = newSrc
    player.value.currentTime = 0
    player.value.switchURL(newSrc)
    player.value.pause()
  }
}
</script>
<template>
  <div ref="containerRef" class="xgplayer-container"></div>
</template>

<style lang="scss" scoped></style>
