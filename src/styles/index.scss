@import url('@/assets/font/iconfont.css');
@import url('@/assets/art-fonts/artfont.css');

// 防止页面缩放的全局样式
html,
body {
  // 禁止双击缩放
  touch-action: manipulation;
  // 防止意外的缩放
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  // 防止双击缩放（iOS Safari）
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
}

.custom-html-image-preview.van-image-preview {
  .van-image-preview__image {
    background-color: rgba(255, 255, 255, 1);
  }
  .van-image-preview__close-icon {
    font-size: 28px;
    top: 86px !important;
  }
  .van-image-preview__index {
    top: 90px !important;
  }
}

.van-hairline--top.van-dialog__footer {
  .van-button {
    border-radius: 0 !important;
    border-width: 0 !important;
  }
}

.van-popup {
  &.van-notify {
    top: 40vh;
    left: 0;
    right: 0;
    border-radius: 10px;
    width: fit-content;
    max-width: 90vw;
    margin: 0 auto;
  }
}

.global-app-loading.van-toast--loading {
  background-color: rgb(255, 255, 255, 0.8);
  color: #666;
  .van-toast__loading {
    color: #666;
  }
}
