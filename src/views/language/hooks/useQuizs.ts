import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showConfirmDialog, showToast, Toast, type ToastWrapperInstance } from 'vant'

import type { TQuizOutData, VolGroupUidVO } from 'va.study.service.volquiz'
import { useLoading, useGlobal } from '@/hooks'
import { languagePageNames } from '@/router/modules/language'
import type { SubmitSectionsReqVO } from 'va.study.service.homework'
import { transferAppMsg } from '@/utils/msg'
export function useQuizs() {
  const routes = useRoute()
  const router = useRouter()
  const quizs = ref<TQuizOutData[]>([])
  const currentQuiz = ref<(TQuizOutData & { attachments?: any[] }) | null>(null)
  const { globalProperties } = useGlobal()
  const { loading, startLoading, endLoading } = useLoading()

  onMounted(() => {
    getQuizList()
  })

  const getQuizList = async () => {
    startLoading()
    try {
      const params: VolGroupUidVO = {
        volGroupUid: routes.query.volGroupUid as string,
        groupUid: routes.query.groupUid as string
      }
      const res = await globalProperties.$api.VolQuiz.getQuizList(params)
      currentQuiz.value = (res?.quizs || []).find((item) => item.quizType === 3) || null
      quizs.value = currentQuiz.value?.children || []
      // quizs.value = (currentQuiz.value?.children || []).filter((v) =>
      //   [1, 5, 6].includes(v.quizType)
      // )
    } catch (e) {
      showToast({
        message: '网络繁忙',
        duration: 1000
      })
    } finally {
      endLoading()
    }
  }

  const checkAnswers = (quizs?: any[]) => {
    return (
      quizs?.some((v: any) => {
        if ([5].includes(v.quizType)) {
          return v?.answers?.some((subAnswer: any) => !subAnswer?.studentAnswer?.trim())
        }
        return !v?.studentAnswer?.length
      }) || false
    )
  }

  const saveQuizs = async (quizs: any[], timeConsume: number) => {
    const quizList =
      quizs?.map((v: any) => {
        let studentAnswer = []
        if ([5].includes(v.quizType)) {
          studentAnswer = v?.answers?.map((subAnswer: any) => {
            return subAnswer?.studentAnswer?.trim() || ''
          })
        } else if ([1, 6].includes(v.quizType)) {
          studentAnswer = [...(v.studentAnswer || [])]
        }
        return {
          quizUid: v.uid,
          quizVersion: v.objectVersion,
          studentAnswer
        }
      }) || []
    try {
      loading.value = true
      const params: SubmitSectionsReqVO = {
        volGroupUid: routes.query.volGroupUid as string,
        groupUid: routes.query.groupUid as string,
        timeConsume,
        quizList: quizList
      }
      const res = await globalProperties.$api.LanguageTest.submitSections(params)
      showToast('提交成功')
      router.replace({
        name: languagePageNames.LanguageResult,
        query: {
          languageType: routes.query.languageType,
          title: routes.query.title,
          sectionUid: res?.sectionUid || ''
        }
      })
    } finally {
      loading.value = false
    }
  }

  const submitAnswer = (quizs: any[], timeConsume: number) => {
    if (loading.value) {
      showToast('请求中，请稍后重试...')
      return
    }
    const isInvalidResult = checkAnswers(quizs)
    if (isInvalidResult) {
      showConfirmDialog({
        title: '提交答案提示',
        message: '您的题目还未做完,确定提交吗？',
        confirmButtonText: '继续做题',
        cancelButtonText: '提 交',
        confirmButtonColor: '#6481EE',
        cancelButtonColor: '#646B77'
      })
        .then(() => {})
        .catch(() => {
          saveQuizs(quizs, timeConsume)
        })
      return
    }
    saveQuizs(quizs, timeConsume)
  }

  return {
    submitAnswer,
    loading,
    currentQuiz,
    quizs,
    getQuizList
  }
}
