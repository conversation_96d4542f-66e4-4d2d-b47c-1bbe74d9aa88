<script setup lang="ts">
import type { TVolQuizOutData } from '@/services/practice/types'
import { computed } from 'vue'

export interface Props {
  currentQuizInfo: TVolQuizOutData
  parentIndex?: string
}

const props = withDefaults(defineProps<Props>(), { currentQuizInfo: () => ({}), parentIndex: '' })

const optionNotEmpty = computed(() => {
  return (
    props.currentQuizInfo.options?.some(
      (option) => option.content || option.htmlContent || option.imgContent
    ) || false
  )
})
</script>

<template>
  <div class="quiz-content flex">
    <div class="quiz-title flex-shrink-0">
      <span>{{ props.currentQuizInfo?.quizSeq || props.currentQuizInfo?.quizSeqNo }}</span>
    </div>
    <div class="quiz-body flex-1 ml-[6px]" v-preview-image>
      <img v-if="props.currentQuizInfo.imgContent" :src="props.currentQuizInfo.imgContent" />
      <div
        v-else
        class="qzitem-quiz break-word-legacy"
        v-html="props.currentQuizInfo.content || props.currentQuizInfo.htmlContent"
      ></div>

      <div
        class="options-list mt-[10px]"
        v-show="props.currentQuizInfo.options?.length && optionNotEmpty"
      >
        <div
          v-for="option in props.currentQuizInfo.options"
          :key="option.aoVal"
          class="option-item"
        >
          <div class="option-key">{{ option.aoVal }}</div>
          <div class="option-text">
            <img v-if="option.imgContent" :src="option.imgContent" />
            <div
              v-else
              class="qzitem-quiz break-word-legacy"
              v-html="option.content || option.htmlContent"
            ></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.quiz-content {
  .quiz-title {
    font-size: 14px;
    font-weight: 600;
    color: #3d3d3d;
  }
  .options-list {
    .option-item {
      display: flex;
      align-items: flex-start;
      margin-bottom: 12px;
      .option-key {
        width: 24px;
        height: 24px;
        line-height: 24px;
        text-align: center;
        border-radius: 12px;
        background: #f5f5f5;
        margin-right: 12px;
        font-size: 14px;
        color: #666;
      }
      .option-text {
        flex: 1;
        font-size: 14px;
        color: #333;
        line-height: 24px;
      }
    }
  }
}
</style>
