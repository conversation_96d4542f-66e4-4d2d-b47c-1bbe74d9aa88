<script setup lang="ts">
import { showImagePreview } from 'vant'
import { withDefaults } from 'vue'
export interface QuizProps {
  contentList?: { original?: string }[]
  article?: string
}
const props = withDefaults(defineProps<QuizProps>(), {
  contentList: () => [],
  article: ''
})

const previewImg = ($e: Event) => {
  if ($e.target instanceof HTMLImageElement && $e.target.tagName.toUpperCase() === 'IMG') {
    showImagePreview([$e.target.src])
  }
}
</script>
<template>
  <div
    class="content-wrap px-[15px] pt-[15px] min-h-[100%] text-[14px] bg-white overflow-y-auto whitespace-pre-line pb-[100px]"
  >
    <div v-if="props.article" v-html="props.article" @click="previewImg"></div>
    <template v-else-if="contentList.length">
      <div
        v-for="(item, index) in props.contentList"
        :key="index"
        v-html="item.original"
        class="mb-[8px]"
      ></div>
    </template>
    <span v-else>暂无数据</span>
  </div>
</template>

<style lang="scss" scoped></style>
