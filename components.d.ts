/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    Album: typeof import('./src/components/album/index.vue')['default']
    AudioPlayer: typeof import('./src/components/audio-player/index.vue')['default']
    BeautiLoading: typeof import('./src/components/beauti-loading/index.vue')['default']
    BlankFilling: typeof import('./src/components/quiz-template/BlankFilling.vue')['default']
    CommonNav: typeof import('./src/components/common-nav/index.vue')['default']
    copy: typeof import('./src/components/upload-photo/index copy.vue')['default']
    CountDown: typeof import('./src/components/setting-countdown/CountDown.vue')['default']
    DoBlankFilling: typeof import('./src/components/do-quiz-template/DoBlankFilling.vue')['default']
    DoMultiChoose: typeof import('./src/components/do-quiz-template/DoMultiChoose.vue')['default']
    DoOtherQuiz: typeof import('./src/components/do-quiz-template/DoOtherQuiz.vue')['default']
    DoSingleChoose: typeof import('./src/components/do-quiz-template/DoSingleChoose.vue')['default']
    DragPopup: typeof import('./src/components/drag-popup/index.vue')['default']
    DrawBoardContent: typeof import('./src/components/draw-board/DrawBoardContent.vue')['default']
    DrawBoardDemo: typeof import('./src/components/draw-board/DrawBoardDemo.vue')['default']
    DrawToolBar: typeof import('./src/components/draw-board/DrawToolBar.vue')['default']
    EmptyData: typeof import('./src/components/empty-data/index.vue')['default']
    GuideContent: typeof import('./src/components/guide-content/index.vue')['default']
    MarkdownRender: typeof import('./src/components/markdown-render/index.vue')['default']
    MultiChoose: typeof import('./src/components/quiz-template/MultiChoose.vue')['default']
    MulUploadPhotos: typeof import('./src/components/upload-photo/MulUploadPhotos.vue')['default']
    OtherQuiz: typeof import('./src/components/quiz-template/OtherQuiz.vue')['default']
    PracticeComprehensiveQuestionsContent: typeof import('./src/components/practice-quiz-temp/PracticeComprehensiveQuestionsContent.vue')['default']
    PracticeMainQuizContent: typeof import('./src/components/practice-quiz-temp/PracticeMainQuizContent.vue')['default']
    PracticeMultiChoiceContent: typeof import('./src/components/practice-quiz-temp/PracticeMultiChoiceContent.vue')['default']
    PracticeOtherContent: typeof import('./src/components/practice-quiz-temp/PracticeOtherContent.vue')['default']
    PracticeSingleChoiceContent: typeof import('./src/components/practice-quiz-temp/PracticeSingleChoiceContent.vue')['default']
    ProgressBar: typeof import('./src/components/audio-player/ProgressBar.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SettingCountdown: typeof import('./src/components/setting-countdown/index.vue')['default']
    SingleChoose: typeof import('./src/components/quiz-template/SingleChoose.vue')['default']
    SubMenu: typeof import('./src/components/draw-board/SubMenu.vue')['default']
    UploadPhoto: typeof import('./src/components/upload-photo/index.vue')['default']
    VaBaseButton: typeof import('./src/components/base-ui/VaBaseButton.vue')['default']
    VaBaseTab: typeof import('./src/components/base-ui/VaBaseTab.vue')['default']
    VaCustomButton: typeof import('./src/components/base-ui/VaCustomButton.vue')['default']
    VaMulSelectBtn: typeof import('./src/components/base-ui/VaMulSelectBtn.vue')['default']
    VanButton: typeof import('vant/es')['Button']
    VanCircle: typeof import('vant/es')['Circle']
    VanDialog: typeof import('vant/es')['Dialog']
    VanEmpty: typeof import('vant/es')['Empty']
    VanField: typeof import('vant/es')['Field']
    VanIcon: typeof import('vant/es')['Icon']
    VanImage: typeof import('vant/es')['Image']
    VanList: typeof import('vant/es')['List']
    VanLoading: typeof import('vant/es')['Loading']
    VanPopup: typeof import('vant/es')['Popup']
    VanPullRefresh: typeof import('vant/es')['PullRefresh']
    VanSticky: typeof import('vant/es')['Sticky']
    VanSwipeCell: typeof import('vant/es')['SwipeCell']
    VanSwitch: typeof import('vant/es')['Switch']
    VanTab: typeof import('vant/es')['Tab']
    VanTabs: typeof import('vant/es')['Tabs']
    VanTag: typeof import('vant/es')['Tag']
    VanTimePicker: typeof import('vant/es')['TimePicker']
    XgPlayer: typeof import('./src/components/xg-player/index.vue')['default']
  }
}
