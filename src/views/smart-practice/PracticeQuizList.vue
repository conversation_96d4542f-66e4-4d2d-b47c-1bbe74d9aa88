<script setup lang="ts">
import { computed, nextTick, ref, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import FilterSelector from './components/FilterSelector.vue'
import { SmartPracticePageNames } from '@/router/modules/practice'
import SelectionPopup from './components/SelectionPopup.vue'
import { useGlobal, useLoading } from '@/hooks'
import type { PracticeSubmitReqDTO, TagData, VolTagReqDTO } from '@/services/practice'
import { usePracticeStore, useDeviceStore } from '@/stores'
import { buriedPointsMap } from '@/model/buried-point'
import { TransferToFlutter, TransferType } from '@/utils/msg'
const { startLoading, endLoading } = useLoading()
const practiceStore = usePracticeStore()
const deviceStore = useDeviceStore()

// 定义筛选项接口，与FilterSelector组件的要求匹配
interface FilterItem {
  label: string
  value: string
  active: boolean
}

// 定义试卷数据接口
interface QuizItem {
  id: string | number
  title: string
  practiceCount: number
  status: 'completed' | 'incomplete' | string
  // 其他可能的字段
  [key: string]: any
}

interface PaperOption {
  title: string
  value: number
}

const router = useRouter()
const route = useRoute()
const { globalProperties } = useGlobal()
// 科目名称
const subjectName = ref('数学')

// 年份、试卷、月份数据
const data: {
  paper: PaperOption[]
  season: any[]
  years: number[]
} = {
  paper: [
    {
      title: 'P1',
      value: 0
    },
    {
      title: 'P3',
      value: 1
    },
    {
      title: 'P4',
      value: 2
    },
    {
      title: 'P5',
      value: 3
    }
  ],
  season: [],
  years: []
}

// exam name
const examTitle = computed(() => {
  return route.query.examTitle
})

// exam board
const examBoardTitle = computed(() => {
  return route.query.examBoardTitle
})

// 年份筛选 - 添加active属性
const yearFilters = ref<FilterItem[]>(
  data.years.map((year, index) => ({
    label: String(year),
    value: String(year),
    active: index === 0 // 默认第一个（不限）为活跃
  }))
)

// 年份选择弹窗
const showYearPopup = ref(false)
const selectedYear = ref<string | number>('0')

// 滚动容器引用
const yearFilterRef = ref<InstanceType<typeof FilterSelector> | null>(null)

// 试卷筛选
const paperFilters = ref<FilterItem[]>([])

// 试卷选择弹窗
const showPaperPopup = ref(false)
const selectedPaper = ref<string | number>('0')
const paperFilterRef = ref<InstanceType<typeof FilterSelector> | null>(null)

// 月份筛选
const monthFilters = ref<FilterItem[]>(
  data.season.map((month, index) => ({
    label: month.title,
    value: String(month.value),
    active: index === 0 // 默认第一个（不限）为活跃
  }))
)

// 月份选择弹窗
const showMonthPopup = ref(false)
const selectedMonth = ref<string | number>('0')
const monthFilterRef = ref<InstanceType<typeof FilterSelector> | null>(null)

// 排除已刷
const excludePracticed = ref(false)

// 监听排除已刷开关变化
watch(excludePracticed, () => {
  // 当开关状态变化时，重新获取试卷列表
  getExamPaperList()

  // 保存当前筛选选项到 store
  saveFilterOptionsToStore()
})

// 保存当前筛选选项到 store
const saveFilterOptionsToStore = () => {
  const exam = Number(route.query.exam || 0)
  const examBoard = Number(route.query.examBoard || 0)
  const subject = Number(route.query.subject || 0)

  if (exam && examBoard && subject) {
    practiceStore.saveQuizListFilterOptions(exam, examBoard, subject, {
      yearValue: getActiveValue(yearFilters.value),
      paperValue: getActiveValue(paperFilters.value),
      monthValue: getActiveValue(monthFilters.value),
      excludePracticed: excludePracticed.value
    })
  }
}

// 获取当前选中的值
const getActiveValue = (filters: FilterItem[]): string => {
  const activeFilter = filters.find((filter) => filter.active)
  return activeFilter ? activeFilter.value : '0'
}

// 试题总数
const totalQuizzes = ref(120)

// 试题列表
const quizzes = ref<QuizItem[]>([])

// 显示年份选择弹窗
const openYearPopup = () => {
  // 设置当前选中的年份
  const activeFilter = yearFilters.value.find((filter: any) => filter.active)
  if (activeFilter) {
    selectedYear.value = activeFilter.value
  }
  showYearPopup.value = true
}

// 显示试卷选择弹窗
const openPaperPopup = () => {
  // 设置当前选中的试卷
  const activeFilter = paperFilters.value.find((filter: any) => filter.active)
  if (activeFilter) {
    selectedPaper.value = activeFilter.value
  }
  showPaperPopup.value = true
}

// 显示月份选择弹窗
const openMonthPopup = () => {
  // 设置当前选中的月份
  const activeFilter = monthFilters.value.find((filter: any) => filter.active)
  if (activeFilter) {
    selectedMonth.value = activeFilter.value
  }
  showMonthPopup.value = true
}

// 通用滚动函数
const scrollToActiveItem = (
  filterRef: InstanceType<typeof FilterSelector> | null,
  activeItemId: string,
  isAllOption: boolean,
  itemsCount: number
) => {
  if (filterRef === null) {
    // 根据activeItemId判断应该使用哪个筛选器引用
    if (activeItemId === 'active-year-item') {
      filterRef = yearFilterRef.value
    } else if (activeItemId === 'active-paper-item') {
      filterRef = paperFilterRef.value
    } else if (activeItemId === 'active-month-item') {
      filterRef = monthFilterRef.value
    }
  }

  if (!filterRef) return

  const scrollContainer = filterRef.scrollContainer

  // 如果元素数量不超过5个，不需要滚动
  if (itemsCount <= 5) return

  if (isAllOption) {
    // 如果是"不限"选项，滚动到最左侧
    if (scrollContainer) scrollContainer.scrollLeft = 0
    return
  }

  // 使用ID选择器找到活跃元素
  setTimeout(() => {
    const activeElement = document.getElementById(activeItemId)
    if (activeElement && scrollContainer) {
      // 获取活跃元素在容器中的位置
      const containerRect = scrollContainer.getBoundingClientRect()
      const elementRect = activeElement.getBoundingClientRect()

      // 计算需要滚动的距离，确保元素显示在最左侧
      const scrollOffset = elementRect.left - containerRect.left + scrollContainer.scrollLeft

      // 设置滚动位置
      scrollContainer.scrollLeft = scrollOffset
    }
  }, 0)
}

// 处理年份选择
const handleYearSelect = (value: string | number) => {
  const stringValue = String(value)
  // 更新所有年份的活跃状态
  yearFilters.value.forEach((filter) => {
    filter.active = filter.value === stringValue
  })

  // 将选中的年份滚动到最前面
  nextTick(() => {
    scrollToActiveItem(
      yearFilterRef.value,
      `active-year-item`,
      stringValue === '0',
      yearFilters.value.length
    )
  })

  // 关闭弹窗
  showYearPopup.value = false
  // 获取试卷列表
  getExamPaperList()

  // 保存当前筛选选项到 store
  saveFilterOptionsToStore()
}

// 处理试卷选择
const handlePaperSelect = (value: string | number) => {
  const stringValue = String(value)
  // 更新所有试卷的活跃状态
  paperFilters.value.forEach((filter) => {
    filter.active = filter.value === stringValue
  })

  // 将选中的试卷滚动到最前面
  nextTick(() => {
    scrollToActiveItem(
      paperFilterRef.value,
      `active-paper-item`,
      stringValue === '0',
      paperFilters.value.length
    )
  })

  // 关闭弹窗
  showPaperPopup.value = false
  // 获取试卷列表
  getExamPaperList()

  // 保存当前筛选选项到 store
  saveFilterOptionsToStore()
}

// 处理月份选择
const handleMonthSelect = (value: string | number) => {
  const stringValue = String(value)
  // 更新所有月份的活跃状态
  monthFilters.value.forEach((filter) => {
    filter.active = filter.value === stringValue
  })

  // 将选中的月份滚动到最前面
  nextTick(() => {
    scrollToActiveItem(
      monthFilterRef.value,
      `active-month-item`,
      stringValue === '0',
      monthFilters.value.length
    )
  })

  // 关闭弹窗
  showMonthPopup.value = false
  // 获取试卷列表
  getExamPaperList()

  // 保存当前筛选选项到 store
  saveFilterOptionsToStore()
}

// 切换年份筛选
const toggleYearFilter = (index: number) => {
  // 如果已经是活跃状态，不做任何操作
  if (yearFilters.value[index].active) return

  // 更新所有年份的活跃状态
  yearFilters.value.forEach((filter, i) => {
    filter.active = i === index
  })

  // 更新选中的年份
  selectedYear.value = yearFilters.value[index].value

  // 将选中的年份滚动到最前面
  nextTick(() => {
    scrollToActiveItem(
      yearFilterRef.value,
      'active-year-item',
      index === 0,
      yearFilters.value.length
    )
  })

  // 获取试卷列表
  getExamPaperList()

  // 保存当前筛选选项到 store
  saveFilterOptionsToStore()
}

// 切换试卷筛选
const togglePaperFilter = (index: number) => {
  // 如果已经是活跃状态，不做任何操作
  if (paperFilters.value[index].active) return

  // 更新所有试卷的活跃状态
  paperFilters.value.forEach((filter, i) => {
    filter.active = i === index
  })

  // 更新选中的试卷
  selectedPaper.value = paperFilters.value[index].value

  // 将选中的试卷滚动到最前面
  nextTick(() => {
    scrollToActiveItem(
      paperFilterRef.value,
      'active-paper-item',
      index === 0,
      paperFilters.value.length
    )
  })

  // 获取试卷列表
  getExamPaperList()

  // 保存当前筛选选项到 store
  saveFilterOptionsToStore()
}

// 切换月份筛选
const toggleMonthFilter = (index: number) => {
  // 如果已经是活跃状态，不做任何操作
  if (monthFilters.value[index].active) return

  // 更新所有月份的活跃状态
  monthFilters.value.forEach((filter, i) => {
    filter.active = i === index
  })

  // 更新选中的月份
  selectedMonth.value = monthFilters.value[index].value

  // 将选中的月份滚动到最前面
  nextTick(() => {
    scrollToActiveItem(
      monthFilterRef.value,
      'active-month-item',
      index === 0,
      monthFilters.value.length
    )
  })

  // 获取试卷列表
  getExamPaperList()

  // 保存当前筛选选项到 store
  saveFilterOptionsToStore()
}

// 查看批改结果
const viewResult = (quiz: QuizItem) => {
  // 跳转到批改结果页面
  router.push({
    name: SmartPracticePageNames.PracticeResult,
    query: {
      practiceRecordUid: quiz.originalData?.practiceRecordUid,
      practiceState: quiz.originalData?.practiceState,
      pageNumber: 1
    }
  })
}

// 分页相关
const pageSize = ref(10)
const currentPage = ref(1)
const loading = ref(false)
const finished = ref(false)

// 下拉刷新状态
const refreshing = ref(false)

// 处理下拉刷新
const onRefresh = async () => {
  try {
    // 重置分页参数并重新获取数据
    currentPage.value = 1
    quizzes.value = []
    finished.value = false
    refreshing.value = true
    await getExamPaperList()
  } finally {
    refreshing.value = false
  }
}

// 设置某个过滤器的选中项
const setActiveFilterByValue = (filters: FilterItem[], value: string): boolean => {
  if (!filters || filters.length === 0) return false

  const index = filters.findIndex((item) => item.value === value)
  if (index !== -1) {
    filters.forEach((filter, i) => {
      filter.active = i === index
    })
    return true
  }

  // 如果没找到，默认选中第一项
  filters[0].active = true
  return false
}

// 获取考试体系+考试局+科目下真题试卷所属标签（Paper、年份、月份）列表
const getExamPaperYearMonthList = async () => {
  // 参数从query中获取
  const exam = Number(route.query.exam)
  const examBoard = Number(route.query.examBoard)
  const subject = Number(route.query.subject)

  // 如果有科目标题，直接使用
  if (route.query.subjectTitle) {
    subjectName.value = route.query.subjectTitle as string
  }

  try {
    const params: VolTagReqDTO = {
      exam,
      examBoard,
      subject
    }
    loading.value = true
    const response =
      await globalProperties.$http.Practice.VolQuizManagerController.getVolTagList(params)
    if (response.data) {
      // 如果响应中包含科目标题信息，更新科目名称
      if (response.data && (response.data as any).subjectTitle) {
        subjectName.value = (response.data as any).subjectTitle
      }
      // 处理 Paper 数据，添加 active 属性
      paperFilters.value = [
        { label: '不限', value: '0', active: true }, // 添加"不限"选项
        ...(response.data.paper?.map((item: TagData) => ({
          label: item.title || '', // 确保不为undefined
          value: String(item.value || 0),
          active: false
        })) || [])
      ]

      // 处理 Years 数据，添加 active 属性
      const yearsData = response.data.years || []
      yearFilters.value = [
        { label: '不限', value: '0', active: true }, // 添加"不限"选项
        ...yearsData.map((year: number) => ({
          label: String(year),
          value: String(year),
          active: false
        }))
      ]

      // 处理 Season 数据，添加 active 属性
      monthFilters.value = [
        { label: '不限', value: '0', active: true }, // 添加"不限"选项
        ...(response.data.season?.map((item: TagData) => ({
          label: item.title || '', // 确保不为undefined
          value: String(item.value || 0),
          active: false
        })) || [])
      ]

      // 从 store 中恢复过滤器状态
      if (exam && examBoard && subject) {
        const storedOptions = practiceStore.getQuizListFilterOptions(exam, examBoard, subject)

        // 设置年份选择
        setActiveFilterByValue(yearFilters.value, storedOptions.yearValue)
        selectedYear.value = storedOptions.yearValue

        // 设置试卷选择
        setActiveFilterByValue(paperFilters.value, storedOptions.paperValue)
        selectedPaper.value = storedOptions.paperValue

        // 设置月份选择
        setActiveFilterByValue(monthFilters.value, storedOptions.monthValue)
        selectedMonth.value = storedOptions.monthValue

        // 设置排除已刷
        excludePracticed.value = storedOptions.excludePracticed

        // 滚动到当前选中的筛选项
        nextTick(() => {
          // 滚动年份筛选器
          scrollToActiveItem(
            yearFilterRef.value,
            'active-year-item',
            storedOptions.yearValue === '0',
            yearFilters.value.length
          )

          // 滚动试卷筛选器
          scrollToActiveItem(
            paperFilterRef.value,
            'active-paper-item',
            storedOptions.paperValue === '0',
            paperFilters.value.length
          )

          // 滚动月份筛选器
          scrollToActiveItem(
            monthFilterRef.value,
            'active-month-item',
            storedOptions.monthValue === '0',
            monthFilters.value.length
          )
        })
      }

      // 获取试卷列表
      await getExamPaperList()
    }
  } finally {
    loading.value = false
  }
}

// 根据条件获取真题试卷
const getExamPaperList = async (isLoadMore = false) => {
  if (!isLoadMore) {
    // 重置分页参数
    currentPage.value = 1
    quizzes.value = []
    finished.value = false
  }

  if (finished.value) {
    return
  }

  loading.value = true
  try {
    const params: any = {
      // 如果选择了"不限"，则不传该参数（使用undefined）
      paper: selectedPaper.value === '0' ? undefined : Number(selectedPaper.value),
      season: selectedMonth.value === '0' ? undefined : Number(selectedMonth.value),
      years: selectedYear.value === '0' ? undefined : Number(selectedYear.value),
      // 如果需要排除已刷题目，可以添加下面的参数
      forbiddenFinish: excludePracticed.value ? 1 : 0,
      // 分页参数
      pageNumber: currentPage.value,
      pageSize: pageSize.value,
      exam: Number(route.query.exam),
      examBoard: Number(route.query.examBoard),
      subject: Number(route.query.subject)
    }
    const response =
      await globalProperties.$http.Practice.VolQuizManagerController.getVolList(params)
    if (response.data) {
      // 将 VolListData 映射为 QuizItem
      const newQuizzes = (response.data.dataList || []).map((item) => ({
        id: item.volUid || '',
        title: item.volName || '',
        practiceCount: item.finishTimes || 0,
        status: item.practiceState === 2 ? 'completed' : 'incomplete',
        // 保留原始数据以便需要时使用
        originalData: item
      }))

      // 追加数据
      quizzes.value = [...quizzes.value, ...newQuizzes]

      // 更新总数
      totalQuizzes.value = response.data.total || 0

      // 判断是否加载完成
      if (quizzes.value.length >= totalQuizzes.value) {
        finished.value = true
      } else {
        currentPage.value++
      }
    }
    return Promise.resolve()
  } catch (error) {
    return Promise.reject(error)
  } finally {
    loading.value = false
  }
}

// 加载更多数据
const onLoad = () => {
  getExamPaperList(true)
}

onMounted(() => {
  globalProperties.$aplusPush.pushEvent({ eventCode: buriedPointsMap['practice_quiz-list'] })
  // 获取试题标签列表和试卷列表
  getExamPaperYearMonthList()
})

const paddingTop = computed(() => {
  const appInfo = (window as any)?.appInfo
  const safeAreaTop = appInfo?.safeAreaTop || 10

  // 检测是否为iPad设备或大屏设备
  const isLargeDevice = window.innerWidth >= 768 // iPad通常宽度至少为768px

  // 对于iPad或大屏设备使用更大的padding值
  const additionalPadding = isLargeDevice ? 52 : 44

  return `${Number(safeAreaTop) + additionalPadding}px`
})

const containerStyle = computed(() => {
  return {
    paddingTop: paddingTop.value
  }
})

const handleRecord = () => {
  router.push({
    name: SmartPracticePageNames.PracticeHistory,
    query: {
      exam: route.query.exam,
      examBoard: route.query.examBoard,
      subject: route.query.subject
    }
  })
}
const handleBack = () => {
  router.back()
}

// 检查是否需要打开Flutter界面
const checkAndOpenFlutterInterface = async (quiz: QuizItem, practiceRecordUid?: string) => {
  console.log('deviceStore.deviceType', deviceStore.deviceType)

  if (deviceStore.deviceType === 'pad') {
    try {
      // 构建URL参数
      let url = `PracticeDopractice?paperUid=${quiz.originalData.volUid}&paperVersion=${quiz.originalData.volVersion}`

      // 如果有practiceRecordUid，添加到URL中
      if (practiceRecordUid) {
        url += `&practiceRecordUid=${practiceRecordUid}&practiceState=${quiz.originalData.practiceState}`
      }

      TransferToFlutter({
        type: TransferType.jump,
        data: {
          title: '',
          url: url
        }
      })
      return true // 已经打开Flutter界面
    } catch (error) {
      console.error('发送消息给Flutter失败:', error)
      // 如果发送失败，继续使用当前逻辑
      return false
    }
  }
  return false // 不是pad设备，使用当前逻辑
}

// 提交试卷
const submitTestPaper = async (practiceRecordUid: string) => {
  const params: PracticeSubmitReqDTO = {
    practiceRecordUid
  }
  try {
    startLoading()
    const res =
      await globalProperties.$http.Practice.QuizAnswerProcessController.submitPractice(params)
    if (res.success) {
      showNotify({ type: 'success', message: '练习已提交, 请稍等片刻' })
      router.push({
        name: SmartPracticePageNames.PracticeResult,
        query: {
          practiceRecordUid
        }
      })
    }
  } finally {
    endLoading()
  }
}

// 继续刷题
const continuePractice = async (quiz: QuizItem) => {
  // 检查是否需要打开Flutter界面
  const openedFlutter = await checkAndOpenFlutterInterface(
    quiz,
    quiz.originalData.practiceRecordUid
  )
  if (openedFlutter) {
    return // 已经打开Flutter界面，不需要继续执行
  }

  // 如果版本号不一致提示用户 practiceVolVersion
  if (
    quiz.originalData.practiceVolVersion &&
    quiz.originalData.volVersion !== quiz.originalData.practiceVolVersion
  ) {
    showConfirmDialog({
      title: '提示',
      message: `试卷版本已更新，请重新刷题`,
      confirmButtonText: '提交试卷',
      cancelButtonText: '继续作答',
      confirmButtonColor: '#326eff'
    })
      .then(() => {
        submitTestPaper(quiz.originalData.practiceRecordUid)
      })
      .catch(() => {
        router.push({
          name: SmartPracticePageNames.PracticeDopractice,
          query: {
            paperUid: quiz.originalData.volUid,
            practiceRecordUid: quiz.originalData.practiceRecordUid,
            practiceState: quiz.originalData.practiceState,
            paperVersion: quiz.originalData.volVersion
          }
        })
      })
  } else {
    router.push({
      name: SmartPracticePageNames.PracticeDopractice,
      query: {
        paperUid: quiz.originalData.volUid,
        practiceRecordUid: quiz.originalData.practiceRecordUid,
        practiceState: quiz.originalData.practiceState,
        paperVersion: quiz.originalData.volVersion
      }
    })
  }
}

// 开始刷题
const startPractice = async (quiz: QuizItem) => {
  // 检查是否需要打开Flutter界面
  const openedFlutter = await checkAndOpenFlutterInterface(quiz)
  if (openedFlutter) {
    return // 已经打开Flutter界面，不需要继续执行
  }

  router.push({
    name: SmartPracticePageNames.PracticeDopractice,
    query: {
      paperUid: quiz.originalData.volUid,
      paperVersion: quiz.originalData.volVersion
    }
  })
}

// 重新刷题
const rePractice = async (quiz: QuizItem) => {
  // 检查是否需要打开Flutter界面
  const openedFlutter = await checkAndOpenFlutterInterface(quiz)
  if (openedFlutter) {
    return // 已经打开Flutter界面，不需要继续执行
  }

  router.push({
    name: SmartPracticePageNames.PracticeDopractice,
    query: {
      paperUid: quiz.originalData.volUid,
      paperVersion: quiz.originalData.volVersion
    }
  })
}
</script>
<template>
  <div class="container" :style="containerStyle">
    <!-- 标题栏 -->
    <common-nav
      :leftArrow="true"
      :showBackground="true"
      :scrollOpacity="false"
      :maxScrollForOpacity="100"
      :enableTransition="false"
    >
      <template #left>
        <van-icon name="arrow-left" @click="handleBack" />
        <span
          class="text-[#3C4258] font-[600] text-[19px] ml-[10px] cursor-pointer"
          @click="handleBack"
          >{{ subjectName }}</span
        >
        <span class="text-[#8F94A8] text-[12px] ml-[12px]">{{ examTitle }}</span>
        <span class="text-[#8F94A8] text-[12px]">-{{ examBoardTitle }}</span>
      </template>
      <template #right>
        <div class="flex items-center" @click="handleRecord">
          <van-icon name="clock-o" class="text-[#3C4258]" color="#3C4258" size="18" />
          <span class="text-[#3C4258] ml-[4px] font-[400]">刷题记录</span>
        </div>
      </template>
    </common-nav>

    <!-- 筛选器 -->
    <div class="px-[16px] py-[10px] bg-white">
      <!-- 年份筛选 -->
      <FilterSelector
        ref="yearFilterRef"
        :filters="yearFilters"
        type="year"
        @toggle="toggleYearFilter"
        @open-popup="openYearPopup"
      />

      <!-- 试卷筛选 -->
      <FilterSelector
        ref="paperFilterRef"
        :filters="paperFilters"
        type="paper"
        @toggle="togglePaperFilter"
        @open-popup="openPaperPopup"
      />

      <!-- 月份筛选 -->
      <FilterSelector
        ref="monthFilterRef"
        :filters="monthFilters"
        type="month"
        @toggle="toggleMonthFilter"
        @open-popup="openMonthPopup"
      />
    </div>

    <!-- 试题统计 -->
    <div class="flex items-center justify-between px-[16px] pt-[20px] pb-[10px]">
      <div class="text-[14px] text-[#8F94A8]">
        共 <span class="artfont text-[#3C4258] font-[700] text-[16px]">{{ totalQuizzes }}</span>
        套真题
      </div>
      <div class="flex items-center">
        <span class="text-[14px] text-[#3C4258] mr-[8px]">排除已刷</span>
        <van-switch
          v-model="excludePracticed"
          size="20px"
          active-color="#326EFF"
          inactive-color="#E5E6EB"
        />
      </div>
    </div>

    <!-- 试题列表 -->
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh" class="px-[16px] py-[10px]">
      <van-list
        v-model:loading="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="onLoad"
        :immediate-check="false"
      >
        <div
          v-for="(quiz, index) in quizzes"
          :key="index"
          class="bg-white rounded-[10px] mb-[12px] p-[16px]"
        >
          <!-- 给卡片添加继续刷题的点击事件 -->
          <div class="flex flex-col">
            <!-- 标题行：图片和标题在同一行 -->
            <div class="flex mb-[6px]">
              <img
                src="@/assets/images/practice/ic_paper.png"
                alt="试卷"
                class="w-[16px] h-[16px] mr-[4px] object-contain flex-shrink-0 mt-[4px]"
              />
              <div class="text-[16px] font-medium text-[#3C4258]">{{ quiz.title }}</div>
            </div>

            <!-- 底部行：刷题次数和按钮 -->
            <div class="flex items-end justify-between">
              <div class="text-[11px] text-[#8F94A8]">刷题{{ quiz.practiceCount }}次</div>
              <div>
                <!-- 批改结果 -->
                <VaBaseButton
                  text="批改结果"
                  label-style="font-size: 13px"
                  class="mr-[10px]"
                  type="plain"
                  @click="viewResult(quiz)"
                  v-if="quiz.originalData.practiceState === 2"
                />
                <VaBaseButton
                  text="重新刷题"
                  label-style="font-size: 13px"
                  type="plain"
                  @click="rePractice(quiz)"
                  v-if="quiz.originalData.practiceState === 2"
                />
                <VaBaseButton
                  class="ml-[10px]"
                  text="开始刷题"
                  label-style="font-size: 13px"
                  v-if="quiz.originalData.practiceState === 0"
                  type="primary"
                  @click="startPractice(quiz)"
                />
                <!-- 继续刷题 -->
                <VaBaseButton
                  class="ml-[10px]"
                  label-style="font-size: 13px;"
                  text="继续刷题"
                  v-if="
                    quiz.originalData.practiceState === 1 && quiz.originalData.practiceRecordUid
                  "
                  type="primary"
                  @click="continuePractice(quiz)"
                />
              </div>
            </div>
          </div>
        </div>
      </van-list>
    </van-pull-refresh>

    <!-- 使用通用选择弹窗组件 -->
    <selection-popup
      :show="showYearPopup"
      @update:show="showYearPopup = $event"
      :model-value="selectedYear"
      @update:model-value="selectedYear = $event"
      title="请选择年份"
      :options="yearFilters.map((item) => ({ value: item.value, label: item.label }))"
      @select="handleYearSelect"
    />

    <selection-popup
      :show="showPaperPopup"
      @update:show="showPaperPopup = $event"
      :model-value="selectedPaper"
      @update:model-value="selectedPaper = $event"
      title="请选择试卷"
      :options="paperFilters.map((item) => ({ value: item.value, label: item.label }))"
      @select="handlePaperSelect"
    />

    <selection-popup
      :show="showMonthPopup"
      @update:show="showMonthPopup = $event"
      :model-value="selectedMonth"
      @update:model-value="selectedMonth = $event"
      title="请选择月份"
      :options="monthFilters.map((item) => ({ value: item.value, label: item.label }))"
      @select="handleMonthSelect"
    />
  </div>
</template>

<style lang="scss" scoped>
.container {
  background-color: #f3f5f8;
  min-height: 100vh;
}

.hide-scrollbar {
  scrollbar-width: none;
  -ms-overflow-style: none;
  &::-webkit-scrollbar {
    display: none;
  }
}
</style>
