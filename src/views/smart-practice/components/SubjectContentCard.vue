<script setup lang="ts">
interface Subject {
  id: string | number
  name: string
  icon?: string
}

defineProps<{
  subjects: Subject[]
}>()

defineEmits<{
  (e: 'select', subject?: Subject): void
}>()
</script>

<template>
  <div class="flex flex-col w-[100%] bg-white mx-[16px] rounded-[10px] p-4">
    <div v-if="subjects && subjects.length" class="grid grid-cols-2 gap-4">
      <div
        v-for="subject in subjects"
        :key="subject.id"
        class="flex flex-col items-center p-4 bg-[#F5F8FF] rounded-[10px] cursor-pointer"
        @click="$emit('select', subject)"
      >
        <div
          class="w-[60px] h-[60px] bg-[#F1F4F7] rounded-full flex items-center justify-center mb-2"
        >
          <img
            v-if="subject.icon"
            :src="subject.icon"
            class="w-[40px] h-[40px]"
            alt="subject icon"
          />
        </div>
        <span class="text-[16px] font-medium">{{ subject.name }}</span>
      </div>
    </div>
    <div v-else class="flex flex-col items-center py-16">
      <div class="w-[120px] h-[120px] bg-[#F1F4F7] rounded-full"></div>
      <span class="text-[18px] mt-[20px]">选择科目，获取最新考题</span>
      <span class="text-[14px]">小寻陪你高效刷题提分！</span>
      <button
        class="w-[200px] h-[40px] bg-[#326EFF] text-white rounded-[40px] mt-[20px] flex items-center justify-center shadow-[0px_4px_10px_0px_rgba(25,93,255,0.2002),inset_0px_-3px_0px_0px_#1358fd]"
        @click="$emit('select')"
      >
        <span class="text-[18px]">选择科目</span>
      </button>
    </div>
  </div>
</template>
