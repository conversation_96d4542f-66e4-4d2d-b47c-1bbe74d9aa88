import { fetchEventSource } from '@microsoft/fetch-event-source'
import type { EventSourceMessage } from '@microsoft/fetch-event-source'
import { useLocalStorage } from '@vueuse/core'
import $config from '@/config'

interface AIStreamOptions {
  onMessage?: (message: any) => void
  onError?: (error: Error) => void
  onComplete?: () => void
}

interface FetchStreamOptions {
  onData?: (data: string) => void
  onError?: (error: Error) => void
  onComplete?: () => void
}

export const createFetchStream = (url: string, params: any, options: FetchStreamOptions = {}) => {
  const { onData, onError, onComplete } = options
  let controller: AbortController | null = null

  const start = async () => {
    try {
      controller = new AbortController()
      const { value: appInfo } = useLocalStorage('appInfo', { TEMPLATE_USER_ACCOUNT: '' })
      const token = appInfo.TEMPLATE_USER_ACCOUNT
      const response = await fetch(url, {
        method: 'POST',
        body: JSON.stringify({
          ...params,
          api_key: 'qxcbhFGVfuRJWh4JdHsqctG+JjeARPKPS219bdrBKhE=' // for validate
        }),
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
          [$config.studentTokenKey]: token || $config.fakeToken
        }
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      if (!response.body) {
        throw new Error('Response body is null')
      }
      const reader = response.body.getReader()
      const decoder = new TextDecoder('utf-8')
      let result = ''

      // eslint-disable-next-line no-constant-condition
      while (true) {
        const { value, done } = await reader.read()
        if (done) break

        const chunk = decoder.decode(value, { stream: true })
        result += chunk
        onData?.(chunk)
      }

      onComplete?.()
    } catch (error) {
      showNotify({ type: 'warning', message: '网络繁忙, 请稍等片刻' })
      onError?.(error as Error)
    } finally {
      close()
    }
  }

  const close = () => {
    controller?.abort()
    controller = null
  }

  return {
    start,
    close
  }
}

export const createAIStream = (url: string, params: any, options: AIStreamOptions = {}) => {
  const { onMessage, onError, onComplete } = options
  let controller: AbortController | null = null

  const start = async () => {
    try {
      controller = new AbortController()
      const { value: appInfo } = useLocalStorage('appInfo', { TEMPLATE_USER_ACCOUNT: '' })
      const token = appInfo.TEMPLATE_USER_ACCOUNT
      await fetchEventSource(url, {
        method: 'POST',
        body: JSON.stringify(params),
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
          [$config.studentTokenKey]: token || $config.fakeToken
        },
        onmessage(event: EventSourceMessage) {
          console.log(event, 'xxx')
          try {
            if (event.data === '[DONE]') return
            const data = event.data
            onMessage?.(data)
          } catch (error) {
            console.error('Parse message error:', error)
          }
        },
        onclose() {
          onComplete?.()
          close()
        },
        onerror(error: Error) {
          console.error('Stream error:', error)
          onError?.(error)
          close()
        }
      })
    } catch (error) {
      console.error('Stream error:', error)
      onError?.(error as Error)
      close()
    }
  }

  const close = () => {
    controller?.abort()
    controller = null
  }

  return {
    start,
    close
  }
}
