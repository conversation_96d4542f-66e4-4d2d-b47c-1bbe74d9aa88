import { createApp } from 'vue'
import $config from '@/config'
import App from './App.vue'
import router from './router'
import { setupPermission } from '@/plugins'
import config from '@/config'
import Vue3Lottie from 'vue3-lottie'
import eruda from 'eruda'
import './styles/reset.scss' // 重置样式
import { Lazyload, setNotifyDefaultOptions } from 'vant'
import 'vant/lib/index.css'

import './styles/index.scss' // 公共样式
import 'vant/es/toast/style'
import '@/styles/tailwind.scss'

import { setupStore } from '@/stores'
import { useDeviceStore } from '@/stores'

import { limitScreenFontSize } from '@/utils/screen'
import { initPreventZoom } from '@/utils/prevent-zoom'

// 初始化防缩放
initPreventZoom()

setNotifyDefaultOptions({
  duration: 1000
})

// 限制大屏幕下的字体大小
limitScreenFontSize(680)

import directives from '@/directives'
import globalComp from '@/components/global-comp'
import vaService from '@/services'
import Service, { getLoginToken } from '@/utils/ajax'
import $aplusPush from '@/utils/aplus_push'

import * as appMsg from '@/utils/msg'
const app = createApp(App)

Service.then(async (service) => {
  app.config.globalProperties.$msg = appMsg
  app.config.globalProperties.$aplusPush = $aplusPush
  app.config.globalProperties.$api = Object.assign({}, service)
  app.config.globalProperties.$http = Object.assign({}, vaService)

  if (localStorage.getItem(config.jwtTokenKey)) {
    await getLoginToken()
  }
  setupStore(app)

  // 初始化设备类型（只初始化一次，不监听变化）
  const deviceStore = useDeviceStore()
  deviceStore.initDeviceType()

  // 初始化屏幕数据监听（监听resize变化）
  deviceStore.initListeners()

  setupPermission()
  app.use(router).use(globalComp).use(directives).use(Lazyload).use(Vue3Lottie).mount('#app')
  if (!$config.vconsoleBlackList.includes(import.meta.env.MODE)) {
    eruda.init()
  }
})
export default app
