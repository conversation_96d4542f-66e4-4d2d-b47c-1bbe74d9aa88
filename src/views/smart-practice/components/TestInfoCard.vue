<script setup lang="ts">
import type { PracticeAnswerResultItem } from '@/services/practice'
import { defineProps, defineEmits } from 'vue'

const emit = defineEmits(['questionClick'])

const props = defineProps({
  courseName: {
    type: String,
    default: ''
  },
  currentRate: {
    type: String,
    default: '45'
  },
  timeUsed: {
    type: String,
    default: ''
  },

  totalScore: {
    type: String,
    default: ''
  },

  studentScore: {
    type: String,
    default: ''
  },
  questionStatusList: {
    type: Array<PracticeAnswerResultItem>,
    default: () => []
  },
  getQuestionStatusClass: {
    type: Function,
    required: true
  }
})

const handleQuestionClick = (item: PracticeAnswerResultItem, index: number) => {
  emit('questionClick', item, index)
}
</script>

<script lang="ts">
// This is needed to provide a default export for the component
export default {
  name: 'TestInfoCard'
}
</script>

<template>
  <div class="bg-white rounded-[10px] p-[16px]">
    <div class="flex justify-center items-center mb-[12px]">
      <span class="text-[12px] text-[#8F94A8] text-center">
        批改和解析结果均由Al提供，结果仅供参考
      </span>
    </div>
    <!-- 试卷名称 -->
    <div class="text-[14px] text-[#3C4258] font-medium mb-[16px] text-center">
      {{ courseName }}
    </div>

    <!-- 统计信息 -->
    <div class="flex justify-around items-center px-[12px]">
      <div class="text-center">
        <div class="artfont text-[#3C4258] text-[20px] font-bold">{{ props.currentRate }}</div>
        <div class="text-[12px] text-[#646B77]">正确率</div>
      </div>
      <div class="text-center">
        <div class="artfont text-[#3C4258] text-[20px] font-bold">{{ props.timeUsed }}</div>
        <div class="text-[12px] text-[#646B77]">用时</div>
      </div>
      <div class="text-center">
        <div class="artfont text-[#3C4258] text-[20px] font-bold">
          {{ props.studentScore }}
          <span class="text-[#3C4258] text-[16px]">/ </span>
          <span class="text-[16px] text-[#3C4258]">{{ props.totalScore }}</span>
        </div>
        <div class="text-[12px] text-[#646B77]">试卷分数</div>
      </div>
    </div>

    <!-- 分隔线 -->
    <div class="h-[1px] bg-[#EEEEEE] my-[16px]"></div>

    <!-- 题目状态指示器 -->
    <div class="flex items-center justify-end gap-[12px] mb-[8px]">
      <div class="w-[8px] h-[8px] rounded-full bg-white border-[2px] border-[#E9EAEE]"></div>
      <div class="text-[12px] text-[#8F94A8]">未作答</div>
      <div class="w-[8px] h-[8px] rounded-full bg-[#FF6B6B] ml-[8px]"></div>
      <div class="text-[12px] text-[#8F94A8]">答错题</div>
      <div class="w-[8px] h-[8px] rounded-full bg-[#4CAF50] ml-[8px]"></div>
      <div class="text-[12px] text-[#8F94A8]">答对题</div>
    </div>

    <!-- 题号列表 -->
    <div class="grid grid-cols-5 gap-[12px] mt-[16px]">
      <div
        v-for="(item, index) in questionStatusList"
        :key="index"
        class="h-[40px] rounded-[8px] flex items-center justify-center text-[16px] font-medium cursor-pointer"
        :class="getQuestionStatusClass(item)"
        @click="handleQuestionClick(item, index)"
      >
        <span class="artfont font-[700]">
          {{ index + 1 }}
        </span>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
// 媒体查询
</style>
