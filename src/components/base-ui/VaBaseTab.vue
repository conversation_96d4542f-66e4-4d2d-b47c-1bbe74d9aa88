<script setup lang="ts">
import { computed, type StyleValue } from 'vue'

defineOptions({
  name: 'VaBaseTab'
})

interface Props {
  modelValue: string
  options: { label: string; value: string }[]
  customStyle?: StyleValue
}

const props = defineProps<Props>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
}>()

const selectedValue = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const handleSelect = (value: string) => {
  selectedValue.value = value
}
</script>

<template>
  <div
    class="va-base-tab-wrap bg-[#E7EBF3] flex justify-around gap-[8px] p-[2px] rounded-[6px]"
    :style="customStyle"
  >
    <div
      v-for="option in options"
      :key="option.value"
      class="tab-item w-[88px] h-[30px] flex items-center justify-center rounded-[6px] text-[12px] cursor-pointer"
      :class="[
        selectedValue === option.value
          ? 'bg-white text-va-primary-text font-bold'
          : 'text-[#3C4258]'
      ]"
      @click="handleSelect(option.value)"
    >
      {{ option.label }}
    </div>
  </div>
</template>

<style lang="scss" scoped>
.va-base-tab-wrap {
  .tab-item {
    user-select: none;
    transition: all 0.2s ease;
  }
}
</style>
