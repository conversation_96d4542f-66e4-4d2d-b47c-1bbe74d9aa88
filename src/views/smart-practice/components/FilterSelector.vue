<template>
  <div class="relative">
    <div
      class="flex items-center overflow-x-auto hide-scrollbar mb-[10px]"
      :class="{ 'pr-[40px]': shouldShowFilterIcon }"
      ref="scrollContainer"
    >
      <div
        v-for="(item, index) in filters"
        :key="item.value"
        class="flex-shrink-0 mr-[10px] py-[2px]"
        :id="item.active ? `active-${type}-item` : ''"
      >
        <div
          class="h-[28px] flex items-center justify-center rounded-full text-[14px] overflow-hidden"
          :class="[
            item.active
              ? 'bg-[rgba(50,110,255,0.08)] text-[#0256FF] border-[1px] border-[#0256FF] active'
              : 'bg-white text-[#333] border-[1px] border-[#E5E6EB]'
          ]"
          @click="toggleFilter(index)"
        >
          <span class="truncate px-2 text-center">{{ item.label }}</span>
        </div>
      </div>
    </div>
    <div v-if="shouldShowFilterIcon" class="absolute -right-[6px] top-0">
      <div
        class="flex items-center justify-center bg-white cursor-pointer py-[3px]"
        @click="openPopup"
      >
        <img
          src="@/assets/images/practice/ic_filter.png"
          alt="筛选"
          class="w-[28px] h-[28px] object-contain"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, defineExpose, computed } from 'vue'

interface FilterItem {
  label: string
  value: string
  active: boolean
}

const props = defineProps({
  filters: {
    type: Array as () => FilterItem[],
    required: true
  },
  type: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['toggle', 'open-popup'])

const scrollContainer = ref<HTMLElement | null>(null)

// 计算是否应该显示筛选图标
const shouldShowFilterIcon = computed(() => {
  return props.filters.length > 4
})

// 切换筛选
const toggleFilter = (index: number) => {
  emit('toggle', index)
}

// 打开弹窗
const openPopup = () => {
  emit('open-popup')
}

// 暴露滚动容器引用
defineExpose({
  scrollContainer,
  scrollToFirst: () => {
    if (scrollContainer.value) {
      scrollContainer.value.scrollLeft = 0
    }
  }
})
</script>

<style lang="scss" scoped>
.hide-scrollbar {
  scrollbar-width: none;
  -ms-overflow-style: none;
  &::-webkit-scrollbar {
    display: none;
  }
}
</style>
