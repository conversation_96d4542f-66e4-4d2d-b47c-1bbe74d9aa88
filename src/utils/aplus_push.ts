// 友盟埋点pv事件上报

import config from '@/config'

// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
export const pushPv = (args?: any) => {
  const { aplus_queue: aplusQueue }: { aplus_queue: any[] } = window as any
  aplusQueue.push({
    action: 'aplus.sendPV',
    arguments: [
      {
        is_auto: false
      },
      { ...args }
    ]
  })
}

// 友盟埋点点击事件上报
// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
export const pushEvent = ({
  eventCode,
  eventType = 'CLK'
}: {
  eventCode: string
  eventType?: string
}) => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const { aplus_queue } = window as any
  // 判断config中env 是否为生产环境 只有生产环境生效
  if (config.runEnv !== 'pro') {
    return
  }
  aplus_queue.push({
    action: 'aplus.record',
    arguments: [eventCode, eventType]
  })
}

// 友盟埋点异步点击事件上报
// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
export const asyncPushEvent = ({
  eventCode,
  eventType = 'CLK'
}: {
  eventCode: string
  eventType?: string
}) => {
  const { aplus_queue } = window as any
  new Promise(() => {
    aplus_queue.push({
      action: 'aplus.record',
      arguments: [eventCode, eventType]
    })
  })
}

export default {
  pushPv,
  pushEvent,
  asyncPushEvent
}
