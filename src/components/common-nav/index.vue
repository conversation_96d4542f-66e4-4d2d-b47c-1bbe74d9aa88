<script setup lang="ts">
import { NavBar, type NavBarProps } from 'vant'
import { computed, ref, watch, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import TransferToFlutter, { TransferType } from '../../utils/msg'
import config from '@/config'
interface Props extends Partial<NavBarProps> {
  title?: string
  leftArrow?: boolean
  showBackground?: boolean
  backgroundOpacity?: number // Manual opacity control
  scrollOpacity?: boolean // Enable scroll-based opacity
  maxScrollForOpacity?: number // The scroll position at which opacity reaches 1
  enableTransition?: boolean // Enable background opacity transition effect
  backHandler?: () => void
}

defineOptions({
  name: 'CommonNav'
})
const router = useRouter()
const route = useRoute()

const props = withDefaults(defineProps<Props>(), {
  leftArrow: true,
  showBackground: false,
  backgroundOpacity: 1, // Default to fully opaque when background is shown
  scrollOpacity: false, // Default to not using scroll-based opacity
  maxScrollForOpacity: 100, // Default to full opacity at 100px scroll
  enableTransition: true // Default to enabling transition effect
})

// Track scroll position for nav background opacity
const scrollY = ref(0)

// Calculate opacity based on scroll position
const calculatedOpacity = computed(() => {
  // If scrollOpacity is enabled and showBackground is false, use scroll-based opacity
  if (props.scrollOpacity && !props.showBackground) {
    // Calculate opacity: 0 at scroll position 0, 1 at scroll position maxScrollForOpacity
    return Math.min(scrollY.value / props.maxScrollForOpacity, 1)
  }

  // Otherwise use the fixed backgroundOpacity value
  return props.backgroundOpacity
})

// Handle scroll event
const handleScroll = () => {
  scrollY.value = window.scrollY
}

// Add and remove scroll event listener
onMounted(() => {
  if (props.scrollOpacity) {
    window.addEventListener('scroll', handleScroll)
  }
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})

const canBack = ref(!!window.history?.state?.back)

watch(
  () => route.fullPath,
  () => {
    canBack.value = !!window.history?.state?.back
  },
  { flush: 'post', immediate: true }
)

// Watch for changes in scrollOpacity to add/remove scroll listener
watch(
  () => props.scrollOpacity,
  (scrollOpacity) => {
    // Remove existing listener if any
    window.removeEventListener('scroll', handleScroll)

    // Add listener only if scrollOpacity is true
    if (scrollOpacity) {
      window.addEventListener('scroll', handleScroll)
    }
  }
)

const showBackArrow = computed(() => {
  return props.leftArrow
})

const navTitle = computed(() => {
  return props.title ?? route.meta.title ?? ''
})

const handleBack = () => {
  if (props?.backHandler) {
    props.backHandler()
    return
  }
  if (canBack.value && props.leftArrow) {
    router.go(-1)
  } else {
    // if cannot back, send message to flutter
    localStorage.setItem('firstEnter', 'false')
    TransferToFlutter({
      type: TransferType.back,
      data: {
        title: '返回',
        url: ''
      }
    })
  }
}

const paddingTop = computed(() => {
  const appInfoStr = localStorage.getItem(config.appInfoKey)
  if (!appInfoStr) return '44px'
  const appInfo = JSON.parse(appInfoStr)
  return `${Number(appInfo.safeAreaTop)}px`
})

// Compute the background style based on the showBackground prop and calculatedOpacity
const navBackgroundStyle = computed(() => {
  // Base style object with proper typing
  const style: Record<string, string> = {
    paddingTop: paddingTop.value
  }

  // Add transition if enabled
  if (props.enableTransition) {
    style.transition = 'background-color 0.3s ease'
  }

  // If showBackground is false and not using scroll opacity, return transparent background
  if (!props.showBackground && !props.scrollOpacity) {
    return {
      ...style,
      backgroundColor: 'transparent'
    }
  }

  // If showBackground is false but using scroll opacity, apply the calculated opacity
  if (!props.showBackground && props.scrollOpacity) {
    return {
      ...style,
      backgroundColor: `rgba(255, 255, 255, ${calculatedOpacity.value})`
    }
  }

  // If showBackground is true, apply the fixed opacity
  return {
    ...style,
    backgroundColor: `rgba(255, 255, 255, ${props.backgroundOpacity})`
  }
})
</script>
<template>
  <div class="common-nav-wrap" :style="navBackgroundStyle">
    <NavBar :left-arrow="showBackArrow" v-bind="{ ...$props, ...$attrs }">
      <!-- 左侧区域 -->
      <template #left>
        <slot name="left">
          <div v-if="showBackArrow" class="nav-left" @click="handleBack">
            <van-icon name="arrow-left" />
            <span class="nav-title">{{ navTitle }}</span>
          </div>
        </slot>
      </template>

      <!-- 标题区域 -->
      <template #title>
        <slot name="title"></slot>
      </template>

      <!-- 右侧区域 -->
      <template #right>
        <slot name="right"></slot>
      </template>
    </NavBar>
  </div>
</template>

<style lang="scss" scoped>
.common-nav-wrap {
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 999;
  color: #3c4258;
  .nav-title {
    font-size: 19px;
    margin-left: 10px;
    font-weight: 600;
  }
  :deep(.van-nav-bar) {
    background-color: transparent;
    .van-nav-bar__left {
      padding-left: 12px;
    }
    .van-icon-arrow-left {
      color: #3c4258;
      font-size: 19px;
      font-weight: 600;
    }
  }
}
</style>
