<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: ''
  },
  minHeight: {
    type: String,
    default: '30vh'
  },
  maxHeight: {
    type: String,
    default: '80vh'
  },
  initialHeight: {
    type: String,
    default: '50vh'
  },
  backgroundColor: {
    type: String,
    default: 'white'
  },
  zIndex: {
    type: Number,
    default: 1000
  },
  showHandle: {
    type: Boolean,
    default: true
  },
  showHeader: {
    type: Boolean,
    default: true
  },
  closeOnClickOutside: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['update:show', 'close', 'heightChange'])

const sheetRef = ref<HTMLElement | null>(null)
const isDragging = ref(false)
const startY = ref(0)
const startHeight = ref(0)
const currentHeight = ref(props.initialHeight)
const showSheet = ref(props.show)

// 监听show属性变化
watch(
  () => props.show,
  (newVal) => {
    showSheet.value = newVal
    if (newVal) {
      // 重置高度
      currentHeight.value = props.initialHeight

      // 添加点击外部关闭事件
      if (props.closeOnClickOutside) {
        setTimeout(() => {
          document.addEventListener('click', handleOutsideClick)
        }, 0)
      }
    } else {
      // 移除点击外部关闭事件
      document.removeEventListener('click', handleOutsideClick)
    }
  }
)

// 计算样式
const sheetStyle = computed(() => {
  return {
    height: currentHeight.value,
    backgroundColor: props.backgroundColor,
    zIndex: props.zIndex,
    transition: isDragging.value ? 'none' : 'height 0.15s ease-out'
  }
})

// 处理拖拽开始
const handleDragStart = (e: MouseEvent | TouchEvent) => {
  if (!sheetRef.value) return

  isDragging.value = true

  // 获取起始位置和高度
  if (e instanceof MouseEvent) {
    startY.value = e.clientY
  } else {
    startY.value = e.touches[0].clientY
  }

  startHeight.value = sheetRef.value.offsetHeight

  // 添加事件监听
  document.addEventListener('mousemove', handleDragMove, { passive: true })
  document.addEventListener('touchmove', handleDragMove, { passive: true })
  document.addEventListener('mouseup', handleDragEnd)
  document.addEventListener('touchend', handleDragEnd)

  // 阻止默认行为和冒泡
  if (e.cancelable) {
    e.preventDefault()
  }
  e.stopPropagation()
}

// 处理拖拽移动
const handleDragMove = (e: MouseEvent | TouchEvent) => {
  if (!isDragging.value || !sheetRef.value) return

  let currentY: number

  if (e instanceof MouseEvent) {
    currentY = e.clientY
  } else {
    currentY = e.touches[0].clientY
  }

  // 计算高度变化
  const deltaY = startY.value - currentY
  const newHeight = startHeight.value + deltaY

  // 获取视口高度
  const viewportHeight = window.innerHeight

  // 将百分比转换为像素
  const minHeightPx = (parseFloat(props.minHeight) * viewportHeight) / 100
  const maxHeightPx = (parseFloat(props.maxHeight) * viewportHeight) / 100

  // 限制高度范围
  if (newHeight >= minHeightPx && newHeight <= maxHeightPx) {
    // 转换为vh单位
    currentHeight.value = `${(newHeight / viewportHeight) * 100}vh`

    // 使用requestAnimationFrame优化性能
    requestAnimationFrame(() => {
      emit('heightChange', currentHeight.value)
    })
  }
}

// 处理拖拽结束
const handleDragEnd = () => {
  isDragging.value = false

  // 移除事件监听
  document.removeEventListener('mousemove', handleDragMove)
  document.removeEventListener('touchmove', handleDragMove)
  document.removeEventListener('mouseup', handleDragEnd)
  document.removeEventListener('touchend', handleDragEnd)
}

// 处理点击外部
const handleOutsideClick = (e: MouseEvent) => {
  if (sheetRef.value && !sheetRef.value.contains(e.target as Node) && props.closeOnClickOutside) {
    closeSheet()
    e.stopPropagation()
  }
}

// 阻止点击弹窗内部关闭
const handleSheetClick = (e: MouseEvent) => {
  e.stopPropagation()
}

// 关闭弹窗
const closeSheet = () => {
  showSheet.value = false
  emit('update:show', false)
  emit('close')
  document.removeEventListener('click', handleOutsideClick)
}

// 组件卸载时清理事件监听
onUnmounted(() => {
  document.removeEventListener('mousemove', handleDragMove)
  document.removeEventListener('touchmove', handleDragMove)
  document.removeEventListener('mouseup', handleDragEnd)
  document.removeEventListener('touchend', handleDragEnd)
  document.removeEventListener('click', handleOutsideClick)
})
</script>

<template>
  <div v-if="showSheet" class="draggable-bottom-sheet-container" @click.stop>
    <!-- 底部弹窗 -->
    <div ref="sheetRef" class="bottom-sheet" :style="sheetStyle" @click="handleSheetClick">
      <!-- 拖拽手柄 -->
      <div
        v-if="showHandle"
        class="drag-handle"
        @mousedown="handleDragStart"
        @touchstart="handleDragStart"
      >
        <div class="handle-bar"></div>
      </div>

      <!-- 标题栏 -->
      <div v-if="showHeader" class="sheet-header">
        <div class="sheet-title">{{ title }}</div>
        <div class="close-button" @click="closeSheet">×</div>
      </div>

      <!-- 内容区域 -->
      <div class="sheet-content">
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<style scoped>
.draggable-bottom-sheet-container {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  pointer-events: none;
  z-index: v-bind(zIndex);
}

.bottom-sheet {
  position: relative;
  width: 100%;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  box-shadow: 0 -4px 16px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  will-change: height;
  pointer-events: auto;
}

.drag-handle {
  width: 100%;
  height: 36px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: grab;
  user-select: none;
  touch-action: none;
  padding: 8px 0;
}

.drag-handle:active {
  cursor: grabbing;
}

.handle-bar {
  width: 40px;
  height: 5px;
  background-color: #e0e0e0;
  border-radius: 3px;
}

.sheet-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px 12px;
  border-bottom: 1px solid #f0f0f0;
}

.sheet-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.close-button {
  font-size: 24px;
  color: #999;
  cursor: pointer;
  width: 24px;
  height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
}

.close-button:hover {
  background-color: #f5f5f5;
}

.sheet-content {
  padding: 16px;
  overflow-y: auto;
  max-height: calc(100% - 72px);
  -webkit-overflow-scrolling: touch;
}
</style>
