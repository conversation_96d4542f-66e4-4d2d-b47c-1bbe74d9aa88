import { v0, type ContractServiceContext, type ActionServiceContext } from 'savml'
import config from '@/config'
import Auth, { contract, service, type TContract } from 'va.basic.service.auth'
import User from 'va.basic.service.user'
import VolQuiz from 'va.study.service.volquiz'
import HomeWork from 'va.study.service.homework'
import CollegePlan from 'va.biz.service.collegePlan'
import Learntdetail from 'va.study.service.learntdetail'

const { jwtTokenKey, applicationUid, runEnv } = config
import TransferToFlutter, { TransferType, type ConnectParam } from '@/utils/msg'
import app from '@/main'

const fakeToken = ['dev', 'development'].includes(runEnv)
  ? 'G1003038202862374912:Weixun_ESchool:6d879827-5f3c-4f82-b24b-920f650c984e:cf7e9832-cf47-4a8b-b5ae-57cff284b8f3'
  : '' // 模拟登陆token
const TOKENCONFIG = [
  { contractUid: 'va.basic.service.auth' },
  { contractUid: 'va.basic.service.user' },
  { contractUid: 'va.study.service.volquiz' },
  { contractUid: 'va.study.service.homework' },
  { contractUid: 'va.biz.service.collegePlan' },
  { contractUid: 'va.study.service.learntdetail' }
]

const factory = v0({
  fetch: window.fetch.bind(window)
})

factory.use(Auth)
factory.use(User)
factory.use(VolQuiz)
factory.use(HomeWork)
factory.use(CollegePlan)
factory.use(Learntdetail)

/**
 * 初始化
 */
factory.on('contract.prepare', (ctx: ContractServiceContext) => {
  Object.assign(ctx.routerOptions, {
    prefix: config.apiHost
  })
})

/**
 * 初始化完成
 */
factory.on('contract.ready', () => {})

/**
 * 控制接口token失效请求次数， 默认获取token成功后，只请求一次，若失败，1min后方可再次请求
 * 前提：createContractToken接口不允许失败
 * 暂时按照接口进行控制(也可按照合约进行控制)
 * ajaxInterfaceDatas 存储接口的数组
 * <AUTHOR>
 * @date 2020-06-20
 * @param {any} {interfaceName}:{interfaceName:string}  接口名称
 */
const ajaxInterfaceDatas: string[] = []
const ajaxInterfaceFilter = ({ interfaceName }: { interfaceName: string }) => {
  ajaxInterfaceDatas.push(interfaceName)
  const timer = setTimeout(() => {
    ajaxInterfaceDatas.splice(0, 1)
    clearTimeout(timer)
  }, 1000 * 60)
}

export const getLoginToken = (contracts?: TContract[]) => {
  return app.config.globalProperties.$api.Auth.createContractToken({
    applicationUid,
    contracts: TOKENCONFIG || contracts
  }).then((res: any) => {
    if (res.code === 'auth.sessionInvalid.error') {
      localStorage.clear()
      showNotify({ type: 'danger', message: '登陆失效！请重新登陆。' })
      // 重新apptoken
      const param: ConnectParam = {
        type: TransferType.logOut
      }
      TransferToFlutter(param)
      // window.location.href = location.href.replace(/#.*/, '')
      throw new Error('登陆失效！请重新登陆。')
    }
    for (const token of res.records) {
      localStorage.setItem(token.contractUid, token.token)
    }
    return 'success'
  })
}

/**
 * 设置ajax请求的一些信息
 */
factory.on('action.prepare', ([ctx, service]: [ActionServiceContext, any]) => {
  // if (!localStorage.getItem('token')) {
  //   Notify({ type: 'danger', message: '登陆失效！请重新登陆。' })
  //   window.location.href = location.href.replace(/\#.*/,'')
  // }
  let Authorization = 'JWT '
  Authorization = 'JWT ' + localStorage.getItem(ctx.package)
  ctx.checkRequest = false
  ctx.checkResponse = false
  let headers = new Headers({
    ...ctx.headers
  })
  if (ctx.package === 'va.basic.service.auth') {
    const Token = localStorage.getItem(jwtTokenKey) || fakeToken
    if (!Token) {
      const param: ConnectParam = {
        type: TransferType.logOut
      }
      TransferToFlutter(param)
      return
    }
    headers = new Headers({
      Client: 'APP',
      Token,
      ...ctx.headers
    })
  } else {
    headers = new Headers({
      ...ctx.headers,
      Authorization
    })
  }
  ctx.fetch = (parms: any) =>
    fetch(ctx.url, {
      method: ctx.method,
      headers,
      body: JSON.stringify(ctx.requestData),
      ...parms
    })
      .then((res) => res.json())
      .then((res) => {
        if (res.code === 'auth.sessionInvalid.error') {
          localStorage.clear()
          showNotify({ type: 'danger', message: '登陆失效！请重新登陆。' })
          // 重新apptoken
          const param: ConnectParam = {
            type: TransferType.logOut
          }
          TransferToFlutter(param)
          // window.location.href = location.href.replace(/#.*/, '')
          throw new Error('登陆失效！请重新登陆。')
        }
        if (res.code === 'auth.tokenInvalid.error') {
          if (ajaxInterfaceDatas.includes(`${service.modalName}.${service.actionName}`)) {
            throw new Error('多次请求错误！！！')
          }

          return getLoginToken([
            {
              contractUid: ctx.package
            }
          ]).then(() => {
            ajaxInterfaceFilter({ interfaceName: `${service.modalName}.${service.actionName}` })

            return (app.config.globalProperties.$api as any)[service.modalName]
              [service.actionName](ctx.requestData || {})
              .then((data: any) => {
                return {
                  json: () => data
                }
              })
          })
        } else if (res.code !== 'success') {
          showNotify({ type: 'danger', message: res.message || '网络繁忙' })
          throw new Error(`SENTRY FLAG: NO ENTRY! \n ${res.code}, ${res.message}`)
        } else {
          return {
            json: () => res.data
          }
        }
      })
      .catch((err) => {
        throw new Error(`SENTRY FLAG: NO ENTRY! \n ${err}`)
      })
})

/**
 * 请求完成
 */
factory.on('action.done', () => {})

/**
 * 请求失败
 */
factory.on('action.error', () => {})

export interface Service
  extends Auth.ServicesHandler,
    User.ServicesHandler,
    VolQuiz.ServicesHandler,
    HomeWork.ServicesHandler,
    CollegePlan.ServicesHandler,
    Learntdetail.ServicesHandler {}

export default Promise.all([
  Auth.service(),
  User.service(),
  VolQuiz.service(),
  HomeWork.service(),
  CollegePlan.service(),
  Learntdetail.service()
]).then(
  ([
    AuthService,
    UserService,
    VolQuizService,
    HomeWorkService,
    CollegePlanService,
    LearntdetailService
  ]): Service => {
    return {
      ...AuthService,
      ...UserService,
      ...VolQuizService,
      ...HomeWorkService,
      ...CollegePlanService,
      ...LearntdetailService
    }
  }
)
