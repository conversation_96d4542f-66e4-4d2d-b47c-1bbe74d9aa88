<script setup lang="ts">
import { computed, markRaw, onMounted, ref, shallowRef } from 'vue'
import { showToast } from 'vant'
import { useRoute, useRouter } from 'vue-router'
import DragPopup from '@/components/drag-popup/index.vue'
import config from '@/config'
import type { PagePracticeAnswerDetailReqVO, TVolQuizDataVO } from '@/services/practice/types'
import { useGlobal, useLoading } from '@/hooks'
import VaBaseTab from '@/components/base-ui/VaBaseTab.vue'
import MyAnswerContent from './sub-practice-detail/MyAnswerContent.vue'
import RightAnswerContent from './sub-practice-detail/RightAnswerContent.vue'
import QuizAnlysisContent from './sub-practice-detail/QuizAnlysisContent.vue'
import AiChatContent from './sub-practice-detail/AiChatContent.vue'
import VideoAnalysisContent from './sub-practice-detail/VideoAnalysisContent.vue'
import { getImageUrl } from '@/utils/img'
import { ensureUrlProtocol } from '@/utils/tool'
import { buriedPointsMap } from '@/model/buried-point'

const { globalProperties } = useGlobal()

const router = useRouter()
const currentDragHeight = ref(0)
const currentQuizNumber = ref(1)
const totalQuizCount = ref(0)
const currentVideoList = ref<string[]>([])
const { startLoading, endLoading, loading } = useLoading()
const tabOptions = [
  { label: '我的作答', value: 'MyAnswerContent', comp: markRaw(MyAnswerContent) },
  { label: '正确答案', value: 'RightAnswerContent', comp: markRaw(RightAnswerContent) },
  { label: '题目解析', value: 'QuizAnlysisContent', comp: markRaw(QuizAnlysisContent) }
]

const currentTab = ref('MyAnswerContent')
const showAiChat = ref(false)
const showVideoAnalysis = ref(false)

const quizs = ref<TVolQuizDataVO[]>([])

const routeQueryInfo = computed(() => {
  const route = useRoute()
  return route?.query
})

const navTitle = computed(() => {
  if (!quizs?.value?.length) {
    return ''
  }
  return `${currentQuizNumber.value}/${totalQuizCount.value}`
})

const renderCom = computed(() => {
  return tabOptions.find((item) => item.value === currentTab.value)?.comp
})

const flattenQuizData = (quiz: TVolQuizDataVO) => {
  if (!quiz) return []

  const result: TVolQuizDataVO[] = []
  const clonedQuiz = JSON.parse(JSON.stringify(quiz)) as TVolQuizDataVO

  if (clonedQuiz.quizType === 3 && clonedQuiz.children?.length) {
    clonedQuiz.children.forEach((child) => {
      result.push(...flattenQuizData(child))
    })
  } else {
    result.push(clonedQuiz)
  }

  return result
}

const currentQuiz = computed(() => {
  return quizs.value?.[0] || {}
})

const flattenedQuizzes = computed(() => {
  return flattenQuizData(currentQuiz.value)
})

const paddingTop = computed(() => {
  const appInfoStr = localStorage.getItem(config.appInfoKey)
  if (!appInfoStr) return '44px'
  const appInfo = JSON.parse(appInfoStr)
  return `${Number(appInfo.safeAreaTop) + 44}px`
})

// 返回上一页
const goBack = () => {
  router.back()
}

const getQuizInfo = async () => {
  const params: PagePracticeAnswerDetailReqVO = {
    pageNumber: currentQuizNumber.value,
    practiceRecordUid: routeQueryInfo.value.practiceRecordUid as string
  }
  try {
    startLoading()
    const { data } =
      await globalProperties.$http.Practice.VolQuizManagerController.getPracticeAnswerDetail(params)
    quizs.value = data?.quizList || []
    totalQuizCount.value = data?.total || 0
  } finally {
    endLoading()
  }
}

const handlePrevQuiz = () => {
  if (currentQuizNumber.value > 1) {
    currentQuizNumber.value--
    getQuizInfo()
  }
}
const handleNextQuiz = () => {
  if (currentQuizNumber.value < totalQuizCount.value) {
    currentQuizNumber.value++
    getQuizInfo()
  }
}

const askAi = () => {
  showAiChat.value = true
  showVideoAnalysis.value = false
}

const goVideoAnalysis = (quizItem: TVolQuizDataVO) => {
  currentVideoList.value = (quizItem.avAnalysis || [])
    .map((url) => ensureUrlProtocol(url))
    .filter(Boolean)
  showVideoAnalysis.value = true
  showAiChat.value = false
}

onMounted(() => {
  globalProperties.$aplusPush.pushEvent({ eventCode: buriedPointsMap.practice_detail })
  currentQuizNumber.value = routeQueryInfo.value?.pageNumber
    ? Number(routeQueryInfo.value.pageNumber)
    : 1
  getQuizInfo()
})
</script>

<template>
  <div class="practice-detail" :style="{ paddingTop }">
    <common-nav :leftArrow="true" :showBackground="true" title=" ">
      <template #title>
        <span>{{ navTitle }}</span>
      </template>
    </common-nav>
    <!-- 题目展示 -->
    <div
      class="rounded-[10px] mx-[10px] bg-white quizs-container my-[20px]"
      :style="{ paddingBottom: `${currentDragHeight + 50}px` }"
    >
      <PracticeMainQuizContent
        :currentIndex="currentQuizNumber - 1"
        :currentQuizInfo="currentQuiz"
      />
    </div>
    <DragPopup v-model="currentDragHeight" max-height="80vh">
      <template #content>
        <div class="op-wrap flex flex-col h-[100%]" v-if="!showAiChat && !showVideoAnalysis">
          <div class="do-quizs-op-wrap flex-1 p-[16px] pt-[0] overflow-y-auto">
            <div
              class="tabs-wrap flex justify-center sticky top-0 z-[100] left-0 mx-[-16px] py-[16px] bg-[#f4f6fa] rounded-t-[20px]"
            >
              <VaBaseTab :options="tabOptions" v-model="currentTab" class="w-[268px]" />
            </div>
            <div
              class="mb-[16px] text-center text-va-grey-text-2 text-[12px]"
              v-show="currentTab !== 'RightAnswerContent'"
            >
              批改和解析结果均由AI提供，结果仅供参考
            </div>
            <component
              :is="renderCom"
              :no="currentQuizNumber"
              :flattenedQuizzes="flattenedQuizzes"
              @go-video-analysis="goVideoAnalysis"
              @refresh-quiz="getQuizInfo"
            />
          </div>
          <div
            v-show="totalQuizCount"
            class="btns-wrap border-t-[2px] border-[#E6E9F3] flex-shrink-0 flex justify-between px-[16px] py-[14px]"
          >
            <div class="flex items-center">
              <VaBaseButton
                :disabled="currentQuizNumber <= 1"
                type="text"
                text="上一题"
                class="text-[14px] px-[32px] py-[8px]"
                @click="handlePrevQuiz"
              />
            </div>
            <div class="flex items-center gap-[12px]">
              <div class="ai-helper inline-flex items-center" @click="askAi">
                <img
                  :src="getImageUrl('practice/ask-va.png')"
                  alt=""
                  class="w-[49px] h-[41px] inline-flex mr-[4px]"
                />
                <span class="text-[14px] text-va-grey-text font-[600]">问小寻</span>
              </div>
            </div>
            <div class="flex items-center">
              <VaBaseButton
                :disabled="currentQuizNumber >= totalQuizCount"
                text="下一题"
                class="text-[14px] px-[32px] py-[8px]"
                @click="handleNextQuiz"
              />
            </div>
          </div>
        </div>
        <AiChatContent
          v-if="showAiChat && currentQuiz.uid"
          :current-quiz="currentQuiz"
          :flattenedQuizzes="flattenedQuizzes"
          @close="showAiChat = false"
        />
        <VideoAnalysisContent
          v-show="showVideoAnalysis"
          :showVideoAnalysis="showVideoAnalysis"
          :currentVideoList="currentVideoList"
          @close="showVideoAnalysis = false"
        />
      </template>
    </DragPopup>
  </div>
</template>

<style scoped>
.practice-detail {
  min-height: 100vh;
  background-color: #f3f5f8;
}
</style>
