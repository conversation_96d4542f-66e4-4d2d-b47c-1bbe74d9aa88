import dayjs from 'dayjs'

const formatNewDate = (date: string | undefined, format: string) => {
  return date ? dayjs(date).format(format) : '--'
}

export const SUBJECT_LIST = {
  ALEVEL: 'alevelList',
  GCSE: 'gcselList',
  IGCSE: 'gcselList',
  AP: 'apList',
  SAT: 'satList',
  'SAT-II': 'satList',
  IB: 'ibList',
  英语培优: 'languagelList',
  新加坡JC体系: 'singaporeJcList',
  ACT: 'actList',
  澳洲体系: 'australianList',
  'Irish Leaving Certificate': 'irishLeavingCertificateList',
  预科foundation: 'foundationList',
  'IPQ/EPQ': 'ipqList',
  马耳他体系: 'maltaList'
}

// Alevel成绩类型
export const ALEVEL_TYPE = {
  1: 'AL',
  2: 'ASL',
  3: 'paper'
}

// IB成绩类型
export const IB_SCORE_TYPE = {
  1: '学科',
  2: 'IA',
  3: 'EE',
  4: 'TOK'
}

// 分数类型
export const SCORETYPES = {
  1: '实考',
  2: '预估',
  3: '评估',
  4: '期中模考',
  5: '全真模考',
  6: '结课模考',
  7: '入营模考'
}

export const SUBJECT_SCORE_COLUMNS = {
  ALEVEL: [
    {
      prop: 'examTime',
      label: '考试日期',
      formatter: ({ examTime }: { examTime: string }) => {
        return formatNewDate(examTime, 'YYYY-MM')
      },
      className: 'exam-time'
    },
    {
      prop: 'alevelType',
      label: '成绩类别',
      formatter: ({ alevelType }: { alevelType: number }) => {
        return (ALEVEL_TYPE as any)[alevelType]
      }
    },
    {
      prop: 'subjectName',
      label: '科目'
    },
    {
      prop: 'paperName',
      label: 'paper',
      formatter: ({ paperName }: { paperName: string }) => {
        return paperName || '--'
      }
    },
    {
      prop: 'examLevel',
      label: '等级'
    },
    {
      prop: 'score',
      label: '得分',
      formatter: ({ score }: { score: number }) => {
        return score || '--'
      }
    },
    {
      prop: 'scoreType',
      label: '分数类别',
      formatter: ({ scoreType }: { scoreType: number }) => {
        return (SCORETYPES as any)[scoreType]
      }
    }
  ],
  IGCSE: [
    {
      prop: 'examTime',
      label: '考试日期',
      formatter: ({ examTime }: { examTime: string }) => {
        return formatNewDate(examTime, 'YYYY-MM')
      },
      className: 'exam-time'
    },
    {
      prop: 'examName',
      label: '成绩类别'
    },
    {
      prop: 'subjectName',
      label: '科目'
    },
    {
      prop: 'paperName',
      label: 'paper',
      formatter: ({ paperName }: { paperName: string }) => {
        return paperName || '--'
      }
    },
    {
      prop: 'examLevel',
      label: '得分/等级'
    },
    {
      prop: 'score',
      label: '得分',
      formatter: ({ score }: { score: number }) => {
        return score || '--'
      }
    },
    {
      prop: 'scoreType',
      label: '分数类别',
      formatter: ({ scoreType }: { scoreType: number }) => {
        return (SCORETYPES as any)[scoreType]
      }
    }
  ],
  GCSE: [
    {
      prop: 'examTime',
      label: '考试日期',
      formatter: ({ examTime }: { examTime: string }) => {
        return formatNewDate(examTime, 'YYYY-MM')
      },
      className: 'exam-time'
    },
    {
      prop: 'examName',
      label: '成绩类别'
    },
    {
      prop: 'subjectName',
      label: '科目'
    },
    {
      prop: 'paperName',
      label: 'paper',
      formatter: ({ paperName }: { paperName: string }) => {
        return paperName || '--'
      }
    },
    {
      prop: 'examLevel',
      label: '得分/等级'
    },
    {
      prop: 'score',
      label: '得分',
      formatter: ({ score }: { score: number }) => {
        return score || '--'
      }
    },
    {
      prop: 'scoreType',
      label: '分数类别',
      formatter: ({ scoreType }: { scoreType: number }) => {
        return (SCORETYPES as any)[scoreType]
      }
    }
  ],
  IB: [
    {
      prop: 'examTime',
      label: '考试日期',
      formatter: ({ examTime }: { examTime: string }) => {
        return formatNewDate(examTime, 'YYYY-MM')
      },
      className: 'exam-time'
    },
    {
      prop: 'ibScoreType',
      label: '成绩类别',
      formatter: ({ ibScoreType }: { ibScoreType: number }) => {
        return (IB_SCORE_TYPE as any)[ibScoreType]
      }
    },
    {
      prop: 'subjectName',
      label: '科目',
      formatter: ({ subjectName }: { subjectName: string }) => {
        return subjectName || '--'
      }
    },
    {
      prop: 'difficulty',
      label: '难度',
      formatter: ({ difficulty }: { difficulty: string }) => {
        return difficulty || '--'
      }
    },
    {
      prop: 'score',
      label: '得分/等级',
      formatter: ({ score, examLevel }: { score: number; examLevel: string }) => {
        return examLevel || (score === -2 ? '未采到分数' : score)
      }
    },
    {
      prop: 'scoreType',
      label: '分数类别',
      formatter: ({ scoreType }: { scoreType: number }) => {
        return (SCORETYPES as any)[scoreType]
      }
    }
  ],
  AP: [
    {
      prop: 'examTime',
      label: '考试日期',
      formatter: ({ examTime }: { examTime: string }) => {
        return formatNewDate(examTime, 'YYYY-MM')
      },
      className: 'exam-time'
    },
    {
      prop: 'subjectName',
      label: '科目'
    },
    {
      prop: 'examLevel',
      label: '等级'
    }
  ],
  SAT: [
    {
      prop: 'examTime',
      label: '考试日期',
      formatter: ({ examTime }: { examTime: string }) => {
        return formatNewDate(examTime, 'YYYY-MM')
      },
      className: 'exam-time'
    },
    {
      prop: 'subjectName',
      label: '科目'
    },
    {
      prop: 'score',
      label: '得分',
      formatter: ({ score }: { score: number }) => {
        return score || '--'
      }
    },
    {
      prop: 'scoreType',
      label: '分数类别',
      formatter: ({ scoreType }: { scoreType: number }) => {
        return (SCORETYPES as any)[scoreType]
      }
    }
  ],
  'SAT-II': [
    {
      prop: 'examTime',
      label: '考试日期',
      formatter: ({ examTime }: { examTime: string }) => {
        return formatNewDate(examTime, 'YYYY-MM')
      },
      className: 'exam-time'
    },
    {
      prop: 'subjectName',
      label: '科目'
    },
    {
      prop: 'score',
      label: '得分',
      formatter: ({ score }: { score: number }) => {
        return score || '--'
      }
    },
    {
      prop: 'scoreType',
      label: '分数类别',
      formatter: ({ scoreType }: { scoreType: number }) => {
        return (SCORETYPES as any)[scoreType]
      }
    }
  ],
  新加坡JC体系: [
    {
      prop: 'examTime',
      label: '考试日期',
      formatter: ({ examTime }: { examTime: string }) => {
        return formatNewDate(examTime, 'YYYY-MM')
      },
      className: 'exam-time'
    },
    {
      prop: 'subjectName',
      label: '科目',
      formatter: ({ subjectName }: { subjectName: string }) => {
        return subjectName || '--'
      }
    },
    {
      prop: 'difficulty',
      label: '难度',
      formatter: ({ difficulty }: { difficulty: string }) => {
        return difficulty || '--'
      }
    },
    {
      prop: 'examLevel',
      label: '等级'
    },
    {
      prop: 'scoreType',
      label: '分数类别',
      formatter: ({ scoreType }: { scoreType: number }) => {
        return (SCORETYPES as any)[scoreType]
      }
    }
  ],
  ACT: [
    {
      prop: 'examTime',
      label: '考试日期',
      formatter: ({ examTime }: { examTime: string }) => {
        return formatNewDate(examTime, 'YYYY-MM')
      },
      className: 'exam-time'
    },
    {
      prop: 'examName',
      label: '成绩类别'
    },
    {
      prop: 'examLevel',
      label: '等级'
    },
    {
      prop: 'scoreType',
      label: '分数类别',
      formatter: ({ scoreType }: { scoreType: number }) => {
        return (SCORETYPES as any)[scoreType]
      }
    }
  ],
  澳洲体系: [
    {
      prop: 'examTime',
      label: '考试日期',
      formatter: ({ examTime }: { examTime: string }) => {
        return formatNewDate(examTime, 'YYYY-MM')
      },
      className: 'exam-time'
    },
    {
      prop: 'subjectName',
      label: '科目',
      formatter: ({ subjectName }: { subjectName: string }) => {
        return subjectName || '--'
      }
    },
    {
      prop: 'atar',
      label: 'ATAR',
      formatter: ({ atar }: { atar: string }) => {
        return `${atar}%` || '--'
      }
    },
    {
      prop: 'scoreType',
      label: '分数类别',
      formatter: ({ scoreType }: { scoreType: number }) => {
        return (SCORETYPES as any)[scoreType]
      }
    }
  ],
  'Irish Leaving Certificate': [
    {
      prop: 'examTime',
      label: '考试日期',
      formatter: ({ examTime }: { examTime: string }) => {
        return formatNewDate(examTime, 'YYYY-MM')
      },
      className: 'exam-time'
    },
    {
      prop: 'subjectName',
      label: '科目',
      formatter: ({ subjectName }: { subjectName: string }) => {
        return subjectName || '--'
      }
    },
    {
      prop: 'subjectDifficultyDegreeName',
      label: '科目难度',
      formatter: ({ subjectDifficultyDegreeName }: { subjectDifficultyDegreeName: string }) => {
        return subjectDifficultyDegreeName || '--'
      }
    },
    {
      prop: 'subjectScoreName',
      label: '科目得分',
      formatter: ({ subjectScoreName }: { subjectScoreName: string }) => {
        return subjectScoreName || '--'
      }
    },
    {
      prop: 'scoreType',
      label: '分数类别',
      formatter: ({ scoreType }: { scoreType: number }) => {
        return (SCORETYPES as any)[scoreType]
      }
    }
  ],
  预科foundation: [
    {
      prop: 'examTime',
      label: '考试日期',
      formatter: ({ examTime }: { examTime: string }) => {
        return formatNewDate(examTime, 'YYYY-MM')
      },
      className: 'exam-time'
    },
    {
      prop: 'school',
      label: '所属学校',
      formatter: ({ school }: { school: string }) => {
        return school || '--'
      }
    },
    {
      prop: 'score',
      label: '成绩',
      formatter: ({ score }: { score: number }) => {
        return score || score === 0 ? `${score}%` : '--'
      }
    },
    {
      prop: 'scoreType',
      label: '分数类别',
      formatter: ({ scoreType }: { scoreType: number }) => {
        return (SCORETYPES as any)[scoreType]
      }
    }
  ],
  'IPQ/EPQ': [
    {
      prop: 'examTime',
      label: '考试日期',
      formatter: ({ examTime }: { examTime: string }) => {
        return formatNewDate(examTime, 'YYYY-MM')
      },
      className: 'exam-time'
    },
    { prop: 'ipqTypeName', label: '成绩类别' },
    { prop: 'subjectName', label: '科目' },
    { prop: 'examLevel', label: '实考分' },
    {
      prop: 'scoreType',
      label: '分数类别',
      formatter: ({ scoreType }: { scoreType: number }) => {
        return (SCORETYPES as any)[scoreType]
      }
    },
    {
      prop: 'publishScoreTime',
      label: '出分时间',
      formatter: ({ publishScoreTime }: { publishScoreTime: string }) => {
        return formatNewDate(publishScoreTime, 'YYYY-MM')
      }
    }
  ]
}
