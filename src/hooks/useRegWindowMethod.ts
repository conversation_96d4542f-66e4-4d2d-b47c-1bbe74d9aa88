import { onBeforeMount, onBeforeUnmount } from 'vue'

type MethodConfig = {
  methodName: string
  method: Function
}

/**
 * 注册window方法的Hook，组件卸载时自动清理
 * @param methodConfig 方法配置，可以是单个配置对象或配置对象数组
 * @returns void
 */
export const useRegWindowMethod = (methodConfig: MethodConfig | MethodConfig[]): void => {
  const configs = Array.isArray(methodConfig) ? methodConfig : [methodConfig]

  // 注册所有方法
  configs.forEach(({ methodName, method }) => {
    // 如果已存在同名方法，直接覆盖（以最新的为准）
    if ((window as any)[methodName]) {
      console.warn(`Window method ${methodName} already exists, will be overridden`)
    }

    // 注册方法到window对象
    ;(window as any)[methodName] = method
  })

  // 组件卸载前清理所有注册的方法
  onBeforeUnmount(() => {
    configs.forEach(({ methodName }) => {
      if ((window as any)[methodName]) {
        delete (window as any)[methodName]
      }
    })
  })
}
