import type { App } from 'vue' // 新增类型导入
import CommonNav from './common-nav/index.vue' // 确保路径正确
import VaBaseButton from './base-ui/VaBaseButton.vue' // 确保路径正确
import VaCustomButton from './base-ui/VaCustomButton.vue' // 确保路径正确
export default function install(app: App) {
  app.component('CommonNav', CommonNav)
  app.component('VaBaseButton', VaBaseButton)
  app.component('VaCustomButton', VaCustomButton)
}
