<script setup lang="ts">
import { getImageUrl } from '@/utils/img'
import dayjs from 'dayjs'
import { ref, nextTick, onMounted, onBeforeUnmount, computed } from 'vue'
import MarkdowRender from '@/components/markdown-render/index.vue'
import BeautiLoading from '@/components/beauti-loading/index.vue'
import type { TVolQuizDataVO } from '@/services/practice/types'
import { useGlobal, useLoading } from '@/hooks'
import { ensureUrlProtocol } from '@/utils/tool'
const { startLoading, endLoading, loading } = useLoading()
const inputRef = ref<FieldInstance | null>(null)
const { globalProperties } = useGlobal()

import { createFetchStream } from '@/utils/ai'
import $config from '@/config'
import type { FieldInstance } from 'vant'

const props = withDefaults(
  defineProps<{
    currentQuiz?: TVolQuizDataVO
  }>(),
  {
    currentQuiz: () => ({})
  }
)
interface Message {
  type?: 1 | 2 // 1: 用户 2: AI
  chatTime?: string
  content?: string
}
const firstMsg = 'Hi，我是唯小寻，你可以询问我关于本题的知识点错题分析'
const MAX_MESSAGES = 12
const messages = ref<Message[]>([])
const userMsg = ref('')
const bottomRef = ref<HTMLElement | null>(null)
const dataLoading = ref(false)

const referQuizInfo = computed(() => {
  return collectQuizData(props.currentQuiz)
})

const scrollToBottom = async () => {
  await nextTick()
  bottomRef.value?.scrollIntoView({ behavior: 'smooth' })
}

const addMessage = (message: Message) => {
  if (message.type === 2 && messages.value.length > 0) {
    const lastMessage = messages.value[messages.value.length - 1]
    if (lastMessage.type === 2) {
      lastMessage.content = message.content
      return
    }
  }
  messages.value.push(message)
  if (messages.value.length > MAX_MESSAGES) {
    // 移除最早的消息 保证消息数量在范围内
    messages.value = messages.value.slice(messages.value.length - MAX_MESSAGES)
  }
}

const collectQuizData = (quiz: TVolQuizDataVO) => {
  const data: any[] = []
  // 收集题目图片
  if (quiz?.imgContent) {
    data.push({
      type: 'image_url',
      image_url: {
        url: ensureUrlProtocol(quiz.imgContent)
      }
    })
  } else if (quiz?.content) {
    // 从content中提取图片URL
    const imgRegex = /<img[^>]+src="([^"]+)"/g
    let match
    while ((match = imgRegex.exec(quiz.content)) !== null) {
      data.push({
        type: 'image_url',
        image_url: {
          url: ensureUrlProtocol(match[1])
        }
      })
    }
  }
  // 收集学生答案
  if (quiz?.studentAnswerList?.length && quiz.quizType != 3) {
    const isChoose = [1, 6].includes(quiz.quizType as number)
    const answerItems = isChoose
      ? [{ type: 'text', text: quiz.studentAnswerList.join(',') }]
      : quiz.studentAnswerList?.map((v) => ({
          type: 'image_url',
          image_url: { url: ensureUrlProtocol(v) }
        })) || []
    data.push(...answerItems)
  }
  // 递归处理子题目
  if (quiz?.quizType === 3 && Array.isArray(quiz?.children)) {
    quiz.children.forEach((child) => {
      data.push(...collectQuizData(child))
    })
  }
  return data
}

const sendMessage = async () => {
  if (dataLoading.value) {
    showNotify({ type: 'warning', message: '数据加载中，请稍后重试' })
    return
  }
  if (!userMsg.value?.trim()) {
    showNotify({ type: 'warning', message: '请输入内容' })
    return
  }
  addMessage({
    type: 1,
    chatTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    content: userMsg.value?.trim() || ''
  })
  scrollToBottom()
  dataLoading.value = true
  const url = $config.clientApiHost + '/api/practice/xiaoxun/askV1'
  const params: any = {
    messages: messages.value?.map((v, idx) => {
      const role = v.type === 1 ? 'user' : 'assistant'
      const contentList: any[] = [
        {
          type: 'text',
          text: v.content
        }
      ]
      if (v.type === 1 && idx === messages?.value?.length - 1) {
        contentList.push(...referQuizInfo.value)
      }
      return {
        role,
        content: contentList
      }
    })
  }
  let content = ''
  const { start, close } = createFetchStream(url, params, {
    onData: (chunk) => {
      content += chunk
      addMessage({
        type: 2,
        chatTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        content: content || ''
      })
      scrollToBottom()
    },
    onComplete: () => {
      close()
      dataLoading.value = false
    },
    onError: (err) => {
      close()
      dataLoading.value = false
    }
  })
  userMsg.value = ''
  start()
}

const getMsgList = async () => {
  const params = {
    quizUid: props.currentQuiz.uid,
    quizVersion: props.currentQuiz.objectVersion
  }
  try {
    startLoading()
    const { data } =
      await globalProperties.$http.Practice.AnsweringQuestionChatRecordController.list(params)
    messages.value = (data || [])
      .map((v) => {
        return {
          type: v.type as 1 | 2,
          chatTime: v.chatTime,
          content: v.content
        }
      })
      .reverse()
      .slice(-MAX_MESSAGES)
  } finally {
    endLoading()
  }
}
const saveMsgList = async () => {
  const dtoList =
    messages.value?.map((msg) => {
      return {
        quizUid: props.currentQuiz.uid,
        quizVersion: props.currentQuiz.objectVersion,
        chatTime: msg.chatTime,
        content: msg.content,
        type: msg.type
      }
    }) || []
  await globalProperties.$http.Practice.AnsweringQuestionChatRecordController.batchInsert(dtoList)
}

onMounted(() => {
  getMsgList()
})

onBeforeUnmount(() => {
  saveMsgList()
  inputRef.value?.blur()
})
</script>

<template>
  <div class="ai-chat-content flex flex-col">
    <!-- 返回按钮 -->
    <div
      class="flex flex-shrink-0 justify-center items-center my-[12px] mx-[25px] cursor-pointer relative"
    >
      <i
        @click="$emit('close')"
        class="iconfont icondanchuangfanhui text-[20px] w-[22px] font-bold text-va-primary-text absolute left-0 top-[50%] translate-y-[-50%]"
      ></i>
      <span class="text-[18px] font-bold text-va-grey-text">
        <img
          :src="getImageUrl('practice/ask-va.png')"
          alt=""
          class="w-[49px] h-[41px] inline-flex mr-[10px]"
        />
        <span class="text-[16px] text-va-grey-text font-[600]">有问题，向小寻提问</span>
      </span>
    </div>
    <div class="messages-container flex-1 overflow-y-auto px-[16px]">
      <div class="messages-list flex flex-col">
        <div class="tip text-center text-[12px] text-va-grey-text-2 mb-[6px]">
          仅展示最近12条对话记录
        </div>
        <div
          v-for="(message, index) in [{ type: 2, content: firstMsg }, ...messages]"
          :key="index"
          :class="['message-item mb-[16px]', message.type === 1 ? 'flex justify-end' : '']"
        >
          <div
            class="text-[14px] px-[12px] py-[10px] line-height-[24px] text-va-grey-text rounded-[20px]"
            :class="[
              'message-content max-w-[100%]',
              message.type === 1 ? 'bg-[#D9ECFF] rounded-tr-none' : 'bg-white rounded-tl-none'
            ]"
          >
            <span v-if="message.type === 1">{{ message.content }}</span>
            <template v-else>
              <MarkdowRender :content="message.content" />
              <BeautiLoading v-show="dataLoading && index === messages.length" />
            </template>
          </div>
        </div>
        <div ref="bottomRef" class="h-[1px]"></div>
      </div>
    </div>
    <div class="input-container p-[16px] flex-shrink-0">
      <div class="flex items-center relative box-border">
        <van-field
          v-model="userMsg"
          rows="1"
          autosize
          type="textarea"
          class="rounded-[10px] bg-white send-msg"
          placeholder="请输入您的疑问..."
          ref="inputRef"
        />
        <span
          class="iconfont iconfasong absolute text-[24px] text-va-primary-btn w-[28px] right-[10px] top-[50%] translate-y-[-50%]"
          @click="sendMessage"
        ></span>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.send-msg {
  padding-right: 40px;
}
.ai-chat-content {
  height: 100%;
  position: relative;
}

.messages-container {
  scroll-behavior: smooth;
}

.messages-wrapper {
  min-height: 100%;
}

.messages-list {
  min-height: 100%;
}
</style>
