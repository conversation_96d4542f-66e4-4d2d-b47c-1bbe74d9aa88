/* eslint-disable quotes */
/* eslint-disable no-unused-vars */
/* eslint-disable no-use-before-define */
/* eslint-disable camelcase */
/* eslint-disable @typescript-eslint/member-delimiter-style */
/* eslint-disable @typescript-eslint/no-unused-vars */
import type { AxiosRequestConfig } from 'axios'
import type { TSObject } from '../types'

/**
 * swagger上未写注释
 */
export interface AnsweringQuestionChatRecordQueryDTO {
  /**
   * 题目uid
   */
  quizUid?: string

  /**
   * 题目版本
   */
  quizVersion?: number
}

/**
 * swagger上未写注释
 */
export interface AnsweringQuestionChatRecordReqDTO {
  /**
   * 当前消息时间
   */
  chatTime?: string

  /**
   * 聊天内容
   */
  content?: string

  /**
   * 题目uid
   */
  quizUid?: string

  /**
   * 题目版本
   */
  quizVersion?: number

  /**
   * 1 回答 2 问答
   */
  type?: number
}

/**
 * swagger上未写注释
 */
export interface AnsweringQuestionChatRecordRespDTO {
  /**
   * 当前消息时间,yyyy-MM-dd HH:mm:ss
   */
  chatTime?: string

  /**
   * 聊天内容
   */
  content?: string

  /**
   * 题目uid
   */
  quizUid?: string

  /**
   * 题目版本
   */
  quizVersion?: number

  /**
   * 1 回答 2 问答
   */
  type?: number
}

/**
 * swagger上未写注释
 */
export interface CorrectQuizAIResult {
  /**
   * swagger上未写注释
   */
  comment?: string

  /**
   * swagger上未写注释
   */
  score?: number
}

/**
 * swagger上未写注释
 */
export interface DictTagData {
  /**
   * 标题
   */
  title?: string

  /**
   * 字典值
   */
  value?: number
}

/**
 * swagger上未写注释
 */
export interface ExamBoardTagNumberData {
  /**
   * 已订阅科目数
   */
  subjectNumber?: number

  /**
   * 最近试卷更新年份
   */
  volYear?: number
}

/**
 * swagger上未写注释
 */
export interface ExamBoardTagNumberParam {
  /**
   * 考试体系
   */
  exam?: number

  /**
   * 考试局
   */
  examBoard?: number
}

/**
 * swagger上未写注释
 */
export interface ExamBoardTagNumberReqDTO {
  /**
   * 考试局列表
   */
  paramList?: Array<ExamBoardTagNumberParam>
}

/**
 * swagger上未写注释
 */
export interface ExamBoardTagNumberResDTO {
  /**
   * 列表
   */
  dataList?: Array<ExamBoardTagNumberData>
}

/**
 * swagger上未写注释
 */
export interface ExamSubjectDictTagData {
  /**
   * 子集
   */
  childrenList?: Array<ExamSubjectDictTagData>

  /**
   * 是否选中 0否 1是
   */
  selected?: number

  /**
   * 标题
   */
  title?: string

  /**
   * 字典值
   */
  value?: number

  /**
   * 最近试卷更新年份
   */
  volYear?: number
}

/**
 * swagger上未写注释
 */
export interface ExamSubjectTagReqDTO {
  /**
   * 考试种类
   */
  exam?: number
}

/**
 * swagger上未写注释
 */
export interface ExamSubjectTagResDTO {
  /**
   * 级联列表
   */
  dataList?: Array<ExamSubjectDictTagData>
}

/**
 * swagger上未写注释
 */
export interface ListVolAnswerDetailRespVO {
  /**
   * 题目列表
   */
  quizList?: Array<TVolQuizDataVO>

  /**
   * 试卷名称
   */
  volName?: string
}

/**
 * swagger上未写注释
 */
export interface ListVolQuizDetailReqVO {
  /**
   * 练习记录UID
   */
  practiceRecordUid?: string
}

/**
 * swagger上未写注释
 */
export interface ListVolQuizzesReqVO {
  /**
   * 试卷uid
   */
  volUid?: string

  /**
   * 试卷版本
   */
  volVersion?: number
}

/**
 * swagger上未写注释
 */
export interface ListVolQuizzesRespVO {
  /**
   * swagger上未写注释
   */
  quizs?: Array<TVolQuizOutData>

  /**
   * 试卷名称
   */
  volName?: string
}

/**
 * swagger上未写注释
 */
export interface PagePracticeAnswerDetailReqVO {
  /**
   * 是否查本练习内错题集 1查练习内错题 0查练习内所有题
   */
  fetchWrong?: number

  /**
   * 页码（第一页传0，以此类推，只传页码即可，size已固定设置为1）
   */
  pageNumber?: number

  /**
   * 练习记录UID
   */
  practiceRecordUid?: string
}

/**
 * swagger上未写注释
 */
export interface PagePracticeAnswerDetailRespVO {
  /**
   * 题目列表
   */
  quizList?: Array<TVolQuizDataVO>

  /**
   * 总数（前端用于判断最后一页时不再翻页）
   */
  total?: number

  /**
   * 试卷名称
   */
  volName?: string
}

/**
 * 分页实体
 */
export interface PageResult<T0> {
  /**
   * 页码
   */
  pageNumber?: number

  /**
   * 分页大小
   */
  pageSize?: number

  /**
   * 开始页
   */
  pageStart?: number

  /**
   * 返回对象集合
   */
  records?: Array<T0>

  /**
   * 返回总条数
   */
  totalCount?: number
}

/**
 * AI回调
 */
export interface PaperAiCallbackDTO {
  /**
   * swagger上未写注释
   */
  comments?: string

  /**
   * 请求ID
   */
  requestId?: string
}

/**
 * 查询试卷回答结果
 */
export interface PracticeAnswerResultItem {
  /**
   * swagger上未写注释
   */
  answerResult?: number

  /**
   * swagger上未写注释
   */
  answerState?: number

  /**
   * swagger上未写注释
   */
  quizSeq?: number

  /**
   * swagger上未写注释
   */
  quizUid?: string

  /**
   * swagger上未写注释
   */
  quizVersion?: number
}

/**
 * 查询试卷回答状态
 */
export interface PracticeAnswerResultResp {
  /**
   * 考试种类
   */
  exam?: number

  /**
   * 考试局
   */
  examBoard?: number

  /**
   * 考试局标题
   */
  examBoardTitle?: string

  /**
   * 考试种类标题
   */
  examTitle?: string

  /**
   * 1 试卷批改完成  9试卷批改中
   */
  paperAnswerResult?: number

  /**
   * 总分
   */
  paperScore?: string

  /**
   * swagger上未写注释
   */
  practiceAnswerResultItemList?: Array<PracticeAnswerResultItem>

  /**
   * 正确率 45%
   */
  rightRate?: string

  /**
   * 学生得分
   */
  studentScore?: string

  /**
   * 科目
   */
  subject?: number

  /**
   * 科目标题
   */
  subjectTitle?: string

  /**
   * 试卷名称
   */
  volName?: string

  /**
   * 错题未订正数量
   */
  wrongBookCount?: number
}

/**
 * 查询试卷回答状态
 */
export interface PracticeAnswerStateItem {
  /**
   * swagger上未写注释
   */
  answerState?: number

  /**
   * swagger上未写注释
   */
  quizSeq?: number

  /**
   * swagger上未写注释
   */
  quizUid?: string

  /**
   * swagger上未写注释
   */
  quizVersion?: number
}

/**
 * swagger上未写注释
 */
export interface PracticeListQueryReqDTO {
  /**
   * 考试品类, 对应字典表
   */
  exam?: number

  /**
   * 考试局, 对应字典表
   */
  examBoard?: number

  /**
   * 页码
   */
  pageNumber?: number

  /**
   * 分页大小
   */
  pageSize?: number

  /**
   * 学科, 对应字典表
   */
  subject?: number
}

/**
 * 练习基本请求
 */
export interface PracticeRecordBaseReqDTO {
  /**
   * 刷题记录uid
   */
  practiceRecordUid?: string
}

/**
 * 练习分析查询请求实体
 */
export interface PracticeResultAnalysisQueryReqDTO {
  /**
   * 刷题记录uid
   */
  practiceRecordUid?: string
}

/**
 * 试卷分析查询返回实体
 */
export interface PracticeResultAnalysisQueryResDTO {
  /**
   * swagger上未写注释
   */
  analysisData?: string

  /**
   * swagger上未写注释
   */
  analysisState?: number
}

/**
 * 开始刷题请求实体
 */
export interface PracticeStartReqDTO {
  /**
   * 试卷uid(practiceType=1时,必传)
   */
  paperUid?: string

  /**
   * 试卷版本(practiceType=1时,必传)
   */
  paperVersion?: number

  /**
   * 刷题记录uid(有该值时,其余值不用传,表示继续刷该真题卷)
   */
  practiceRecordUid?: string
}

/**
 * 开始刷题返回实体
 */
export interface PracticeStartResDTO {
  /**
   * 上次做到题目大题序号
   */
  beforeOffsetQuizSeq?: number

  /**
   * 上次结束答题位置
   */
  beforeOffsetQuizUid?: string

  /**
   * 刷题记录uid
   */
  practiceRecordUid?: string
}

/**
 * 结束刷题请求实体
 */
export interface PracticeSubmitReqDTO {
  /**
   * 刷题记录uid
   */
  practiceRecordUid?: string
}

/**
 * swagger上未写注释
 */
export interface QuizAnalysisVO {
  /**
   * swagger上未写注释
   */
  analysisStats?: number

  /**
   * 解析内容 - 用于显示
   */
  content?: string

  /**
   * 可用于直接渲染内容, 适用于pc、app
   */
  htmlContent?: string

  /**
   * swagger上未写注释
   */
  imgContent?: string
}

/**
 * swagger上未写注释
 */
export interface QuizAnswerVO {
  /**
   * 答案分值，针对填空题（精确到每一个填空设置分数）
   */
  answerScore?: number

  /**
   * 答案内容 - 用于显示
   */
  content?: string

  /**
   * 可用于直接渲染内容, 适用于pc、app
   */
  htmlContent?: string

  /**
   * swagger上未写注释
   */
  imgContent?: string

  /**
   * 答案，针对填空题一个空多答案
   */
  options?: Array<string>
}

/**
 * swagger上未写注释
 */
export interface QuizAttachmentVO {
  /**
   * 附件名称
   */
  name?: string

  /**
   * 音频转文字
   */
  text?: string

  /**
   * 附件类型
   */
  type?: string

  /**
   * 附件地址
   */
  url?: string
}

/**
 * swagger上未写注释
 */
export interface QuizKnowledgeData {
  /**
   * swagger上未写注释
   */
  id?: number

  /**
   * swagger上未写注释
   */
  knowledgeName?: string
}

/**
 * swagger上未写注释
 */
export interface QuizKnowledgeVO {
  /**
   * 知识点ID
   */
  id?: number

  /**
   * 知识点名称
   */
  knowledgeName?: string
}

/**
 * swagger上未写注释
 */
export interface QuizOptionVO {
  /**
   * 选项值
   */
  aoVal?: string

  /**
   * 选项内容
   */
  content?: string

  /**
   * 可用于直接渲染内容, 适用于pc、app
   */
  htmlContent?: string

  /**
   * swagger上未写注释
   */
  imgContent?: string
}

/**
 * swagger上未写注释
 */
export interface QuizOutDTO {
  /**
   * 题目解析内容
   */
  analysis?: Array<QuizAnalysisVO>

  /**
   * 题目答案
   */
  answers?: Array<QuizAnswerVO>

  /**
   * 题目附件(音频地址、视频地址等)
   */
  attachments?: Array<QuizAttachmentVO>

  /**
   * 题目视频解析-列表
   */
  avAnalysis?: Array<string>

  /**
   * 子题目列表, 只有综合题会有
   */
  children?: Array<QuizOutDTO>

  /**
   * 题目的完整性: 1-完整的 2-题目options可能与实际不符[同步老题库数据的时候可能出现的情况]
   */
  completed?: string

  /**
   * 题干内容 - 用于显示
   */
  content?: string

  /**
   * 题干内容MD5 - 用于IB去重
   */
  contentMD5?: string

  /**
   * 题目内容类型, 1-题库录入【默认】, 2-全图片题目
   */
  contentType?: string

  /**
   * 创建时间/毫秒
   */
  createTime?: number

  /**
   * 创建人
   */
  creator?: string

  /**
   * 题目难度, 对应字典表
   */
  difficulty?: string

  /**
   * 题目难度标题
   */
  difficultyName?: string

  /**
   * 考试品类, 对应字典表
   */
  exam?: number

  /**
   * 考试局, 对应字典表
   */
  examBoard?: number

  /**
   * 考试局标题
   */
  examBoardName?: string

  /**
   * 考试品类标题
   */
  examName?: string

  /**
   * 题干内容 - 可用于直接渲染的, 适用于pc、app
   */
  htmlContent?: string

  /**
   * swagger上未写注释
   */
  imgContent?: string

  /**
   * 题目说明
   */
  instructions?: Array<string>

  /**
   * 知识点ID
   */
  knowledgeIds?: Array<number>

  /**
   * 是否已经标记 0-未标记 1-已标记
   */
  knowledgeStatus?: number

  /**
   * 知识点列表
   */
  knowledges?: Array<QuizKnowledgeVO>

  /**
   * 引用外部材料ID【兼容老题目使用】
   */
  linkUid?: string

  /**
   * 对象版本号
   */
  objectVersion?: number

  /**
   * 题目选项-只有选择题会有
   */
  options?: Array<QuizOptionVO>

  /**
   * 考试卷标题列表
   */
  paperNames?: Array<string>

  /**
   * 考试卷列表，对应字典表
   */
  papers?: Array<number>

  /**
   * 大题顺序
   */
  quizSeq?: number

  /**
   * 题号编号, 只有子题目会有
   */
  quizSeqNo?: string

  /**
   * 题目类型: 1-单选题, 2-解答题, 3-综合题, 4听力题, 5-填空题
   */
  quizType?: string

  /**
   * 题目标题
   */
  quizTypeName?: string

  /**
   * 题目分值
   */
  score?: number

  /**
   * 考试季, 对应字典表
   */
  season?: number

  /**
   * 考试季标题
   */
  seasonName?: string

  /**
   * 所属试卷
   */
  source?: string

  /**
   * 学科, 对应字典表
   */
  subject?: number

  /**
   * 学科名称
   */
  subjectName?: string

  /**
   * Ib题库迁移原id(仅迁移使用）
   */
  transferSourceUid?: string

  /**
   * Ib题库迁移原url(仅迁移使用）
   */
  transferSourceUrl?: string

  /**
   * 题目uid
   */
  uid?: string

  /**
   * 年份
   */
  year?: number
}

/**
 * swagger上未写注释
 */
export interface QuizQueryReqDTO {
  /**
   * 题目uid & 版本号 列表
   */
  quizUidVersionList?: Array<QuizUidVersionVO>
}

/**
 * swagger上未写注释
 */
export interface QuizQueryRespDTO {
  /**
   * 题目列表
   */
  quizs?: Array<QuizOutDTO>
}

/**
 * 提交题目答案详情请求实体
 */
export interface QuizSubmitAnswerDetailReqDTO {
  /**
   * 题号
   */
  quizSeq?: number

  /**
   * 题目uid
   */
  quizUid?: string

  /**
   * 题目版本
   */
  quizVersion?: number

  /**
   * 用户答案
   */
  userAnswerList?: Array<string>
}

/**
 * 提交题目答案请求实体
 */
export interface QuizSubmitAnswerReqDTO {
  /**
   * 刷题记录uid
   */
  practiceRecordUid?: string

  /**
   * 大题序号
   */
  quizSeq?: number

  /**
   * 大题uid
   */
  quizUid?: string

  /**
   * 大题版本
   */
  quizVersion?: number

  /**
   * 开始作答时间(yyyy-MM-dd HH:mm:ss)
   */
  startTime?: string

  /**
   * 提交作答时间(yyyy-MM-dd HH:mm:ss)
   */
  submitTime?: string

  /**
   * 用户答案 没有小题的情况下, 有小题时答案在 userQuizAnswerDetailList
   */
  userAnswerList?: Array<string>

  /**
   * 题目详情列表
   */
  userQuizAnswerDetailList?: Array<QuizSubmitAnswerDetailReqDTO>
}

/**
 * swagger上未写注释
 */
export interface QuizUidVersionVO {
  /**
   * 对象版本号
   */
  objectVersion?: number

  /**
   * 题目uid
   */
  uid?: string
}

/**
 * 错题本查询请求实体
 */
export interface QuizWrongBookPageReqDTO {
  /**
   * 考试种类
   */
  exam?: number

  /**
   * 考试局
   */
  examBoard?: number

  /**
   * 页码
   */
  pageNumber?: number

  /**
   * 分页大小
   */
  pageSize?: number

  /**
   * paper值
   */
  paper?: string

  /**
   * 是否已纠错 0未纠错 1已纠错
   */
  rectifyState?: string

  /**
   * 科目
   */
  subject?: number
}

/**
 * swagger上未写注释
 */
export interface Result<T0> {
  /**
   * swagger上未写注释
   */
  code?: string

  /**
   * swagger上未写注释
   */
  data?: T0

  /**
   * swagger上未写注释
   */
  message?: string

  /**
   * swagger上未写注释
   */
  success?: boolean
}

/**
 * swagger上未写注释
 */
export interface SubjectTagResDTO {
  /**
   * 学科标签级联列表
   */
  dataList?: Array<DictTagData>
}

/**
 * 提交题目答案请求实体
 */
export interface SubmitWrongBookAnswerReqDTO {
  /**
   * 题目uid
   */
  quizUid?: string

  /**
   * 题目版本
   */
  quizVersion?: number

  /**
   * 开始作答时间(yyyy-MM-dd HH:mm:ss)
   */
  startTime?: string

  /**
   * 提交作答时间(yyyy-MM-dd HH:mm:ss)
   */
  submitTime?: string

  /**
   * 用户答案 没有小题的情况下 有小题时答案在 userQuizAnswerDetailList
   */
  userAnswerList?: Array<string>

  /**
   * 题目详情列表
   */
  userQuizAnswerDetailList?: Array<QuizSubmitAnswerDetailReqDTO>

  /**
   * 刷题记录uid
   */
  wrongBookRecordUid?: string
}

/**
 * swagger上未写注释
 */
export interface TQuizAnalysisData {
  /**
   * swagger上未写注释
   */
  analysisStats?: number

  /**
   * swagger上未写注释
   */
  content?: string

  /**
   * swagger上未写注释
   */
  htmlContent?: string

  /**
   * swagger上未写注释
   */
  imgContent?: string

  /**
   * swagger上未写注释
   */
  originContent?: string
}

/**
 * swagger上未写注释
 */
export interface TQuizAnswerData {
  /**
   * 答案分值，针对填空题（精确到每一个填空设置分数）
   */
  answerScore?: number

  /**
   * swagger上未写注释
   */
  content?: string

  /**
   * swagger上未写注释
   */
  htmlContent?: string

  /**
   * swagger上未写注释
   */
  imgContent?: string

  /**
   * 答案，针对填空题一个空多答案
   */
  options?: Array<string>

  /**
   * swagger上未写注释
   */
  originContent?: string
}

/**
 * swagger上未写注释
 */
export interface TQuizAnswerDataVO {
  /**
   * 答案分值，针对填空题（精确到每一个填空设置分数）
   */
  answerScore?: number

  /**
   * swagger上未写注释
   */
  content?: string

  /**
   * swagger上未写注释
   */
  htmlContent?: string

  /**
   * swagger上未写注释
   */
  imgContent?: string

  /**
   * 答案，针对填空题一个空多答案
   */
  options?: Array<string>

  /**
   * swagger上未写注释
   */
  originContent?: string
}

/**
 * swagger上未写注释
 */
export interface TQuizAttachmentData {
  /**
   * 附件名称
   */
  name?: string

  /**
   * 音频转文字
   */
  text?: string

  /**
   * 附件类型
   */
  type?: string

  /**
   * 附件地址
   */
  url?: string
}

/**
 * swagger上未写注释
 */
export interface TQuizOptionData {
  /**
   * swagger上未写注释
   */
  aoVal?: string

  /**
   * swagger上未写注释
   */
  content?: string

  /**
   * swagger上未写注释
   */
  htmlContent?: string

  /**
   * swagger上未写注释
   */
  imgContent?: string

  /**
   * swagger上未写注释
   */
  originContent?: string
}

/**
 * swagger上未写注释
 */
export interface TVolQuizDataVO {
  /**
   * 错题本加入时间
   */
  addWrongBookTime?: string

  /**
   * AI解析
   */
  aiAnalysis?: string

  /**
   * 题目解析内容
   */
  analysis?: Array<TQuizAnalysisData>

  /**
   * 批注
   */
  annotation?: string

  /**
   * 学生作答状态 0未批改 9批改中 1错误 2正确
   */
  answerResult?: number

  /**
   * 作答分数
   */
  answerScore?: number

  /**
   * 学生答案提交状态 1已全答 2已提交未答 3未提交未答 9部分答题
   */
  answerState?: number

  /**
   * 题目答案
   */
  answers?: Array<TQuizAnswerDataVO>

  /**
   * 题目附件(音频地址、视频地址等)
   */
  attachments?: Array<QuizAttachmentVO>

  /**
   * 题目视频解析-列表
   */
  avAnalysis?: Array<string>

  /**
   * 题目版本
   */
  children?: Array<TVolQuizDataVO>

  /**
   * 题干内容
   */
  content?: string

  /**
   * 题目内容类型
   */
  contentType?: number

  /**
   * 创建时间/毫秒
   */
  createTime?: number

  /**
   * 题目难度
   */
  difficulty?: number

  /**
   * 题目难度标题
   */
  difficultyName?: string

  /**
   * 考试品类
   */
  exam?: number

  /**
   * 考试局
   */
  examBoard?: number

  /**
   * 考试局标题
   */
  examBoardName?: string

  /**
   * 考试品类标题
   */
  examName?: string

  /**
   * 可用于直接渲染内容, 适用于pc、app
   */
  htmlContent?: string

  /**
   * 图片内容
   */
  imgContent?: string

  /**
   * 知识点
   */
  knowledgeIds?: Array<number>

  /**
   * swagger上未写注释
   */
  knowledges?: Array<QuizKnowledgeData>

  /**
   * 版本号
   */
  objectVersion?: number

  /**
   * 题目选项-只有选择题会有
   */
  options?: Array<TQuizOptionData>

  /**
   * 原始内容-用于编辑
   */
  originContent?: string

  /**
   * 考试卷标题列表
   */
  paperNames?: Array<string>

  /**
   * 考试卷
   */
  papers?: Array<number>

  /**
   * 试卷题目说明
   */
  quizInstructions?: Array<string>

  /**
   * 题目编号
   */
  quizSeq?: number

  /**
   * 题目编号
   */
  quizSeqNo?: string

  /**
   * 题目类型
   */
  quizType?: number

  /**
   * 题目标题
   */
  quizTypeName?: string

  /**
   * 订正状态 -1无需纠正 0未纠正 1已纠正
   */
  rectifyState?: number

  /**
   * 订正提交状态 1已提交  0未提交
   */
  rectifySubmitStatus?: number

  /**
   * 题目分值
   */
  score?: number

  /**
   * 考试季
   */
  season?: number

  /**
   * 考试季标题
   */
  seasonName?: string

  /**
   * 顺序
   */
  seq?: number

  /**
   * 题目来源
   */
  source?: string

  /**
   * 学生答案列表
   */
  studentAnswerList?: Array<string>

  /**
   * 学科
   */
  subject?: number

  /**
   * 学科名称
   */
  subjectName?: string

  /**
   * 题目uid
   */
  uid?: string

  /**
   * 所属试卷Uid
   */
  volUid?: string

  /**
   * 错题本uid
   */
  wrongBookUid?: string

  /**
   * 年份
   */
  year?: number
}

/**
 * swagger上未写注释
 */
export interface TVolQuizOutData {
  /**
   * swagger上未写注释
   */
  analysis?: Array<TQuizAnalysisData>

  /**
   * swagger上未写注释
   */
  annotation?: string

  /**
   * swagger上未写注释
   */
  answers?: Array<TQuizAnswerData>

  /**
   * swagger上未写注释
   */
  attachments?: Array<TQuizAttachmentData>

  /**
   * swagger上未写注释
   */
  children?: Array<TVolQuizOutData>

  /**
   * swagger上未写注释
   */
  content?: string

  /**
   * swagger上未写注释
   */
  difficulty?: number

  /**
   * swagger上未写注释
   */
  difficultyName?: string

  /**
   * swagger上未写注释
   */
  exam?: number

  /**
   * swagger上未写注释
   */
  examBoard?: number

  /**
   * swagger上未写注释
   */
  examBoardName?: string

  /**
   * swagger上未写注释
   */
  examName?: string

  /**
   * swagger上未写注释
   */
  htmlContent?: string

  /**
   * swagger上未写注释
   */
  imgContent?: string

  /**
   * swagger上未写注释
   */
  instructions?: Array<string>

  /**
   * swagger上未写注释
   */
  knowledgeIds?: Array<number>

  /**
   * swagger上未写注释
   */
  knowledges?: Array<QuizKnowledgeData>

  /**
   * swagger上未写注释
   */
  objectVersion?: number

  /**
   * swagger上未写注释
   */
  options?: Array<TQuizOptionData>

  /**
   * swagger上未写注释
   */
  originContent?: string

  /**
   * swagger上未写注释
   */
  paperNames?: Array<string>

  /**
   * swagger上未写注释
   */
  papers?: Array<number>

  /**
   * 试卷题目说明
   */
  quizInstructions?: Array<string>

  /**
   * swagger上未写注释
   */
  quizSeq?: number

  /**
   * swagger上未写注释
   */
  quizSeqNo?: string

  /**
   * swagger上未写注释
   */
  quizType?: number

  /**
   * swagger上未写注释
   */
  quizTypeName?: string

  /**
   * swagger上未写注释
   */
  score?: number

  /**
   * swagger上未写注释
   */
  season?: number

  /**
   * swagger上未写注释
   */
  seasonName?: string

  /**
   * swagger上未写注释
   */
  seq?: number

  /**
   * swagger上未写注释
   */
  source?: string

  /**
   * swagger上未写注释
   */
  subject?: number

  /**
   * swagger上未写注释
   */
  subjectName?: string

  /**
   * swagger上未写注释
   */
  uid?: string

  /**
   * swagger上未写注释
   */
  volUid?: string

  /**
   * swagger上未写注释
   */
  year?: number
}

/**
 * swagger上未写注释
 */
export interface TagData {
  /**
   * 标题
   */
  title?: string

  /**
   * value
   */
  value?: number
}

/**
 * swagger上未写注释
 */
export interface UserHeaderCountResDTO {
  /**
   * 刷题天数
   */
  practiceDays?: number

  /**
   * 刷题套数
   */
  practiceVolCount?: number

  /**
   * 待订正题数
   */
  unRectifyQuizCount?: number
}

/**
 * swagger上未写注释
 */
export interface UserPracticeReportCalcReqDTO {
  /**
   * 刷题记录uid
   */
  practiceRecordUid?: string
}

/**
 * swagger上未写注释
 */
export interface UserPracticeReportCalcRespDTO {
  /**
   * 刷题天数
   */
  practiceDays?: number

  /**
   * 刷题套数
   */
  practiceVolCount?: number

  /**
   * 本套练习用时 格式：1H20min
   */
  timeLength?: string
}

/**
 * swagger上未写注释
 */
export interface UserSubjectTagAddReqDTO {
  /**
   * 订阅标签列表
   */
  subscribeList?: Array<UserSubjectTagInfo>
}

/**
 * swagger上未写注释
 */
export interface UserSubjectTagData {
  /**
   * 考试种类
   */
  exam?: number

  /**
   * 考试局
   */
  examBoard?: number

  /**
   * 考试局标题
   */
  examBoardTitle?: string

  /**
   * 考试种类标题
   */
  examTitle?: string

  /**
   * 科目
   */
  subject?: number

  /**
   * 科目标题
   */
  subjectTitle?: string
}

/**
 * swagger上未写注释
 */
export interface UserSubjectTagInfo {
  /**
   * 考试种类
   */
  exam?: number

  /**
   * 考试局
   */
  examBoard?: number

  /**
   * 科目
   */
  subject?: number
}

/**
 * swagger上未写注释
 */
export interface UserSubjectTagInfoData {
  /**
   * 考试种类
   */
  exam?: number

  /**
   * 考试局
   */
  examBoard?: number

  /**
   * 考试局标题
   */
  examBoardTitle?: string

  /**
   * 考试种类标题
   */
  examTitle?: string

  /**
   * 已刷试卷套数
   */
  practicePaperCount?: number

  /**
   * 科目
   */
  subject?: number

  /**
   * 科目标题
   */
  subjectTitle?: string

  /**
   * 待订正数
   */
  unRectifyCount?: number

  /**
   * 用户订阅Id
   */
  userTagId?: number
}

/**
 * swagger上未写注释
 */
export interface UserSubjectTagInfoResDTO {
  /**
   * 列表
   */
  dataList?: Array<UserSubjectTagInfoData>
}

/**
 * swagger上未写注释
 */
export interface UserSubjectTagRemoveReqDTO {
  /**
   * 用户订阅Id
   */
  userTagId?: number
}

/**
 * swagger上未写注释
 */
export interface UserSubjectTagResDTO {
  /**
   * 列表
   */
  dataList?: Array<UserSubjectTagData>
}

/**
 * 唯小寻问答请求
 */
export interface VXiaoXunAskReq {
  /**
   * swagger上未写注释
   */
  content?: string

  /**
   * swagger上未写注释
   */
  quizUid?: string

  /**
   * swagger上未写注释
   */
  quizVersion?: number

  /**
   * user assistant
   */
  role?: number
}

/**
 * 唯小寻问答返回
 */
export interface VXiaoXunAskResp {
  /**
   * swagger上未写注释
   */
  content?: string
}

/**
 * swagger上未写注释
 */
export interface VolListData {
  /**
   * 考试品类, 对应字典表
   */
  exam?: number

  /**
   * 考试局, 对应字典表
   */
  examBoard?: number

  /**
   * swagger上未写注释
   */
  examBoardTitle?: string

  /**
   * swagger上未写注释
   */
  examTitle?: string

  /**
   * 刷题次数
   */
  finishTimes?: number

  /**
   * 练习日期
   */
  practiceDate?: string

  /**
   * 刷题记录uid(用于跳转查看结果，继续刷题时使用)
   */
  practiceRecordUid?: string

  /**
   * 刷题状态(0:未刷题;1:刷题中;2:刷题结束)
   */
  practiceState?: number

  /**
   * 练习记录的总分数
   */
  practiceTotalScore?: number

  /**
   * 练习中的待订正数(未订正的错题数)
   */
  practiceUnRectifyCount?: number

  /**
   * 练习记录的用户得分
   */
  practiceUserScore?: number

  /**
   * 练习记录的试卷版本
   */
  practiceVolVersion?: number

  /**
   * 学科, 对应字典表
   */
  subject?: number

  /**
   * swagger上未写注释
   */
  subjectTitle?: string

  /**
   * 试卷名称
   */
  volName?: string

  /**
   * 试卷UID
   */
  volUid?: string

  /**
   * 试卷版本（最新版本）
   */
  volVersion?: number
}

/**
 * swagger上未写注释
 */
export interface VolListQueryReqDTO {
  /**
   * 考试品类, 对应字典表
   */
  exam?: number

  /**
   * 考试局, 对应字典表
   */
  examBoard?: number

  /**
   * 排除已刷 1排查 0不排除
   */
  forbiddenFinish?: number

  /**
   * 页码
   */
  pageNumber?: number

  /**
   * 分页大小
   */
  pageSize?: number

  /**
   * paper
   */
  paper?: number

  /**
   * 考试季
   */
  season?: number

  /**
   * 学科, 对应字典表
   */
  subject?: number

  /**
   * 年份
   */
  years?: number
}

/**
 * swagger上未写注释
 */
export interface VolListQueryResDTO {
  /**
   * 真题列表
   */
  dataList?: Array<VolListData>

  /**
   * 已刷试卷套数
   */
  practicePaperCount?: number

  /**
   * 真题总数
   */
  total?: number
}

/**
 * swagger上未写注释
 */
export interface VolTagReqDTO {
  /**
   * 考试种类
   */
  exam?: number

  /**
   * 考试局
   */
  examBoard?: number

  /**
   * 科目
   */
  subject?: number
}

/**
 * swagger上未写注释
 */
export interface VolTagResDTO {
  /**
   * paper列表
   */
  paper?: Array<TagData>

  /**
   * 考试季列表
   */
  season?: Array<TagData>

  /**
   * 年份列表
   */
  years?: Array<number>
}

/**
 * 错题本uid
 */
export interface WrongBookBaseReq {
  /**
   * 错题本uid
   */
  wrongBookUid?: string
}

/**
 * swagger上未写注释
 */
export interface WrongBookCategoryStatsRespDTO {
  /**
   * 业务类型
   */
  bizType?: string

  /**
   * 数量
   */
  count?: number

  /**
   * paper标题
   */
  tagTitle?: string

  /**
   * paper值
   */
  tagValue?: string
}

/**
 * 错题本统计请求实体
 */
export interface WrongBookStatsReqDTO {
  /**
   * 考试种类
   */
  exam?: number

  /**
   * 考试局
   */
  examBoard?: number

  /**
   * 科目
   */
  subject?: number
}

/**
 * swagger上未写注释
 */
export interface WrongBookSubjectStatsRespDTO {
  /**
   * 已纠正数量
   */
  rectifyCount?: number

  /**
   * 未纠正数量
   */
  unRectifyCount?: number

  /**
   * 全部错题数量
   */
  wrongCount?: number
}

/**
 * 试卷结果查询
 */
export interface PracticeResultQueryController {
  /**
   * 【考试报告】统计用户累计刷题套数、天数，当前练习的用时
   * @param {UserPracticeReportCalcReqDTO} req req
   */
  calcUserPracticeReport(
    req: UserPracticeReportCalcReqDTO,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<UserPracticeReportCalcRespDTO>>

  /**
   * 查询试卷回答结果 每题是否答对答错
   * @param {PracticeRecordBaseReqDTO} reqDTO reqDTO
   */
  queryAnswerResult(
    reqDTO: PracticeRecordBaseReqDTO,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<PracticeAnswerResultResp>>

  /**
   * 查询试卷回答状态 只是返回有没有答题 不关心正确
   * @param {PracticeRecordBaseReqDTO} reqDTO reqDTO
   */
  queryAnswerState(
    reqDTO: PracticeRecordBaseReqDTO,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<Array<PracticeAnswerStateItem>>>

  /**
   * 查询试卷解析结果接口
   * @param {PracticeResultAnalysisQueryReqDTO} reqDTO reqDTO
   */
  queryResultAnalysis(
    reqDTO: PracticeResultAnalysisQueryReqDTO,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<PracticeResultAnalysisQueryResDTO>>
}

/**
 * 答题过程相关接口
 */
export interface QuizAnswerProcessController {
  /**
   * 删除练习, 如果所有题目，没有提交答案（包括提交答案是空）, 前端调用接口删除本次练习
   * @param {PracticeRecordBaseReqDTO} reqDTO reqDTO
   */
  deletePractice(
    reqDTO: PracticeRecordBaseReqDTO,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<boolean>>

  /**
   * 开始刷题
   * @param {PracticeStartReqDTO} reqDTO reqDTO
   */
  startPractice(
    reqDTO: PracticeStartReqDTO,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<PracticeStartResDTO>>

  /**
   * 交卷
   * @param {PracticeSubmitReqDTO} reqDTO reqDTO
   */
  submitPractice(
    reqDTO: PracticeSubmitReqDTO,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<boolean>>

  /**
   * 提交题目答案接口
   * @param {QuizSubmitAnswerReqDTO} reqDTO reqDTO
   */
  submitQuizAnswer(
    reqDTO: QuizSubmitAnswerReqDTO,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<string>>
}

/**
 * 问小寻
 */
export interface AnsweringQuestionChatRecordController {
  /**
   * 批量插入
   * @param {Array<AnsweringQuestionChatRecordReqDTO>} dtoList dtoList
   */
  batchInsert(
    dtoList?: Array<AnsweringQuestionChatRecordReqDTO>,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<Record<string, unknown>>>

  /**
   * 查询
   * @param {AnsweringQuestionChatRecordQueryDTO} dto dto
   */
  list(
    dto: AnsweringQuestionChatRecordQueryDTO,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<Array<AnsweringQuestionChatRecordRespDTO>>>
}

/**
 * 测试接口
 */
export interface TestController {
  /**
   * /copyOSSPaperImgFile
   * @param {string} path path
   */
  // copyOSSPaperImgFile(path?: string, axiosConfig?: AxiosRequestConfig): Promise<Result<string>>

  /**
   * /getAiPrompt
   * @param {number} type type
   */
  getAiPrompt(type?: number, axiosConfig?: AxiosRequestConfig): Promise<string>

  /**
   * /refreshTag

   */
  refreshTag(axiosConfig?: AxiosRequestConfig): Promise<void>

  /**
   * testAICorrect
   * @param {string} uid uid
   * @param {boolean} root root
   */
  testAICorrect(
    uid?: string,
    root?: boolean,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<CorrectQuizAIResult>>

  /**
   * testAIPaper
   * @param {string} practiceRecordUid practiceRecordUid
   */
  testAIPaper(
    practiceRecordUid?: string,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<PaperAiCallbackDTO>>

  /**
   * testAIStream

   */
  testAIStream(axiosConfig?: AxiosRequestConfig): Promise<void>

  /**
   * /testBuildTag

   */
  testBuildTag(axiosConfig?: AxiosRequestConfig): Promise<void>
}

/**
 * 用户操作关联相关接口
 */
export interface UserManagerController {
  /**
   * 用户签到

   */
  addUserSign(axiosConfig?: AxiosRequestConfig): Promise<Result<void>>

  /**
   * 添加用户订阅信息
   * @param {UserSubjectTagAddReqDTO} req req
   */
  addUserSubscribe(
    req: UserSubjectTagAddReqDTO,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<void>>

  /**
   * 【首页】获取用户签到数、刷题数、错题数等信息

   */
  getUserHeaderCount(axiosConfig?: AxiosRequestConfig): Promise<Result<UserHeaderCountResDTO>>

  /**
   * 获取当前用户所订阅的科目信息列表

   */
  getUserSubjectInfoList(
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<UserSubjectTagInfoResDTO>>

  /**
   * 获取当前用户所订阅的科目标签列表

   */
  getUserSubjectTagList(axiosConfig?: AxiosRequestConfig): Promise<Result<UserSubjectTagResDTO>>

  /**
   * 【首页】快捷获取用户最后一次未完成的刷题记录（用于快捷跳转继续刷题）

   */
  quickUserPracticeRecordUid(axiosConfig?: AxiosRequestConfig): Promise<Result<string>>

  /**
   * 移除用户订阅信息
   * @param {UserSubjectTagRemoveReqDTO} req req
   */
  removeUserSubscribe(
    req: UserSubjectTagRemoveReqDTO,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<void>>
}

/**
 * 试卷试题管理相关接口
 */
export interface VolQuizManagerController {
  /**
   * 获取单/多道题目列表
   * @param {QuizQueryReqDTO} req req
   */
  findQuizzes(
    req: QuizQueryReqDTO,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<QuizQueryRespDTO>>

  /**
   * 获取考试体系+考试局 标签上的统计数值及试卷年份信息
   * @param {ExamBoardTagNumberReqDTO} req req
   */
  getExamBoardTagNumber(
    req: ExamBoardTagNumberReqDTO,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<ExamBoardTagNumberResDTO>>

  /**
   * 获取课程体系（考试体系）

   */
  getExamList(axiosConfig?: AxiosRequestConfig): Promise<Result<SubjectTagResDTO>>

  /**
   * 获取刷题选择标签列表
   * @param {ExamSubjectTagReqDTO} req req
   */
  getExamSubjectTagList(
    req: ExamSubjectTagReqDTO,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<ExamSubjectTagResDTO>>

  /**
   * 分页方式获取练习中某一道题目详情（含学生作答信息）
   * @param {PagePracticeAnswerDetailReqVO} req req
   */
  getPracticeAnswerDetail(
    req: PagePracticeAnswerDetailReqVO,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<PagePracticeAnswerDetailRespVO>>

  /**
   * 获取刷题记录列表（试卷维度）
   * @param {PracticeListQueryReqDTO} req req
   */
  getPracticeList(
    req: PracticeListQueryReqDTO,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<VolListQueryResDTO>>

  /**
   * 获取真题试卷题目列表(含学生作答信息，继续刷题，查看结果等 历史刷题查这个)
   * @param {ListVolQuizDetailReqVO} req req
   */
  getVolAnswerDetailList(
    req: ListVolQuizDetailReqVO,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<ListVolAnswerDetailRespVO>>

  /**
   * 获取真题试卷列表（含置顶）
   * @param {VolListQueryReqDTO} req req
   */
  getVolList(
    req: VolListQueryReqDTO,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<VolListQueryResDTO>>

  /**
   * 获取真题试卷题目列表（不含作答信息，开始作答，重新刷题等 首次刷题查这个）
   * @param {ListVolQuizzesReqVO} req req
   */
  getVolQuizList(
    req: ListVolQuizzesReqVO,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<ListVolQuizzesRespVO>>

  /**
   * 获取考试体系+考试局+科目下真题试卷所属标签（Paper、年份、月份）列表
   * @param {VolTagReqDTO} req req
   */
  getVolTagList(req: VolTagReqDTO, axiosConfig?: AxiosRequestConfig): Promise<Result<VolTagResDTO>>
}

/**
 * 错题本答题
 */
export interface WrongBookAnswerController {
  /**
   * 错题本分类统计
   * @param {WrongBookStatsReqDTO} dto dto
   */
  categoryStats(
    dto: WrongBookStatsReqDTO,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<Array<WrongBookCategoryStatsRespDTO>>>

  /**
   * 移除错题
   * @param {WrongBookBaseReq} reqDTO reqDTO
   */
  removeWrongBook(
    reqDTO: WrongBookBaseReq,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<boolean>>

  /**
   * 错题本科目统计
   * @param {WrongBookStatsReqDTO} dto dto
   */
  subjectStats(
    dto: WrongBookStatsReqDTO,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<WrongBookSubjectStatsRespDTO>>

  /**
   * 提交题目答案接口
   * @param {SubmitWrongBookAnswerReqDTO} reqDTO reqDTO
   */
  wrongBookSubmitQuizAnswer(
    reqDTO: SubmitWrongBookAnswerReqDTO,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<string>>

  /**
   * 错题本分页查询
   * @param {QuizWrongBookPageReqDTO} dto dto
   */
  wrongBookPage(
    dto: QuizWrongBookPageReqDTO,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<PageResult<TVolQuizDataVO>>>
}

/**
 * 唯小寻问答
 */
export interface VXiaoXunController {
  /**
   * 唯小寻问答
   * @param {VXiaoXunAskReq} req req
   */
  xiaoXunAsk(
    req: VXiaoXunAskReq,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<VXiaoXunAskResp>>
}
export interface ServiceHandler {
  PracticeResultQueryController: PracticeResultQueryController
  QuizAnswerProcessController: QuizAnswerProcessController
  AnsweringQuestionChatRecordController: AnsweringQuestionChatRecordController
  TestController: TestController
  UserManagerController: UserManagerController
  VolQuizManagerController: VolQuizManagerController
  WrongBookAnswerController: WrongBookAnswerController
  VXiaoXunController: VXiaoXunController
}
