<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { getImageUrl } from '@/utils/img'
import { languagePageNames } from '@/router/modules/language'
import { useGlobal, useLoading } from '@/hooks'
import { formatTime } from '@/utils/time'
import type {
  SectionAnswerRecordsReqVO,
  SectionAnswerRecordsRespVO
} from 'va.study.service.homework'
import { formatNumTimes } from '@/utils/num'

const { startLoading, endLoading } = useLoading()
const { globalProperties } = useGlobal()

const router = useRouter()
const routes = useRoute()

const props = withDefaults(
  defineProps<{
    activeTab: string
  }>(),
  {
    activeTab: ''
  }
)

const gradientColor = {
  '0%': '#9B69FF',
  '50%': '#4B67FC',
  '100%': '#9D69FF'
}

const text = computed(() => {
  return `${formatNumTimes(historyInfo.value.overallAccuracyRate || 0)}%`
})

const answerIcon = getImageUrl('language/answered-icon.png')
const historyInfo = ref<Partial<SectionAnswerRecordsRespVO>>({})

const getHistoryList = async () => {
  try {
    startLoading()
    const params: SectionAnswerRecordsReqVO = {
      volGroupUid: routes.query.volGroupUid as string,
      groupUid: routes.query.groupUid as string
    }
    const res = await globalProperties.$api.LanguageTest.studentsSectionRecords(params)
    historyInfo.value = res || {}
  } finally {
    endLoading()
  }
}

const jumpResultPage = (sectionUid: string) => {
  router.replace({
    name: languagePageNames.LanguageResult,
    query: {
      title: routes.query?.title,
      languageType: routes.query.languageType,
      sectionUid
    }
  })
}

watch(
  () => props.activeTab,
  (newVal: string) => {
    if (newVal === 'PracticeHistory') {
      getHistoryList()
    }
  },
  { immediate: true }
)
</script>
<template>
  <div class="result-detail box-content bg-white text-[14px] pb-[160px]">
    <div class="summary px-[15px] py-[20px] mb-[10px] box-content">
      <div class="content rounded-[10px] py-[20px] pl-[6px] bg-white">
        <div class="up flex items-center mb-[16px]">
          <img :src="answerIcon" alt="icon" class="inline-block h-[68px] w-[68px] mr-[8px]" />
          <span class="text-[18px] text-text-grey mr-[20px] inline-flex items-center"
            ><span>共作答</span
            ><span class="text-[30px] font-bold px-[5px] leading-[30px]">{{
              historyInfo.answerCount || 0
            }}</span
            ><span>次</span></span
          >
          <div class="circle flex-1 flex justify-end pr-[10px]">
            <van-circle
              :current-rate="formatNumTimes(historyInfo.overallAccuracyRate || 0)"
              :text="text"
              layer-color="rgba(0,112,255,0.18)"
              :color="gradientColor"
              :stroke-width="70"
              size="106"
            >
              <div class="circle-inner flex flex-col justify-center items-center h-[100%]">
                <div class="text-[18px] text-va-blue font-semibold">{{ text }}</div>
                <div class="text-[#898E96] text-[12px]">总正确率</div>
              </div>
            </van-circle>
          </div>
        </div>
        <div class="bot text-text-grey pl-[18px]">
          <span class="score mr-[12px]">最佳成绩:</span>
          <span class="right-rate mr-[20px]"
            >正确率{{ formatNumTimes(historyInfo.bestAccuracyRate || 0) }}%</span
          >
          <span class="right-detail"
            >答对{{ historyInfo.bestCorrectAnswerCount || 0 }}/{{
              historyInfo.totalQuestionCount || 0
            }}</span
          >
        </div>
      </div>
    </div>
    <div class="histroy-list px-[15px]">
      <div class="histroy-item" v-for="(item, index) in historyInfo.recordList" :key="index">
        <div
          class="title mb-[10px] relative flex justify-between text-text-grey text-[15px] font-bold pl-[10px] before:absolute before:content-[''] before:left-0 before:top-[50%] before:h-[14px] before:w-[5px] before:translate-y-[-50%] before:rounded-[4px]"
        >
          <div>
            <span class="mr-[10px]">正确率{{ formatNumTimes(item.accuracyRate || 0) }}%</span>
            <span class="rate"
              >答对{{ item.correctAnswerCount }}/{{ item.totalQuestionCount }}</span
            >
          </div>
          <div
            @click="jumpResultPage(item.sectionUid)"
            class="btn text-[12px] text-va-blue px-[6px] py-[2px] bg-[#DDE4FC] rounded-[12px] font-normal"
          >
            查看详情
          </div>
        </div>
        <div class="sub-title text-[#898E96] text-[10px] mb-[15px]">
          <span class="mr-[25px]">答题用时: {{ formatTime(item.timeConsume || 0) }}</span>
          <span class="">提交时间: {{ item.createTime }}</span>
        </div>
        <div class="quiz-list flex flex-wrap">
          <div
            class="quiz-item mb-[20px] rounded-[6px] border border-[#ddd] overflow-hidden w-[40px] flex-shrink-0 box-border"
            v-for="(quizItem, quizIndex) in item.answerList"
            :key="quizIndex"
          >
            <div
              class="no text-center text-[12px] text-text-grey h-[20px]"
              :class="[quizItem.answerStatus === 1 ? 'bg-[#C0FDE1]' : 'bg-[#FFEAEA]']"
            >
              {{ quizIndex + 1 }}
            </div>
            <div class="result h-[30px] flex justify-center items-center">
              <span
                class="iconfont"
                :class="[
                  quizItem.answerStatus === 1
                    ? 'iconduigouwuquan text-[#41B981]'
                    : 'iconcuowuwuquan text-[#EF6A6D]'
                ]"
              ></span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.summary {
  height: 205px;
  background: url('@/assets/images/language/practice-history-bg.png') no-repeat;
  background-size: 100% 205px;
  .content {
    background: linear-gradient(180deg, #ffffff 0%, #ffffff 100%);
    box-shadow:
      0px 2px 12px 0px rgba(100, 130, 238, 0.5),
      inset 2px -2px 4px 0px #c7ddff;
  }
  .bot {
    .score {
      position: relative;
      &::before {
        position: absolute;
        content: '';
        width: 66px;
        height: 10px;
        left: -2px;
        bottom: 0;
        background: linear-gradient(
            133deg,
            rgb(72, 102, 251, 0.19) 0%,
            rgb(160, 105, 255, 0.19) 100%
          )
          no-repeat;
        border-radius: 5px;
      }
    }
  }
}
.quiz-list {
  // gap: 20px;
  .quiz-item {
    margin-right: 20px;
  }
}
.histroy-item {
  .title {
    &::before {
      background: linear-gradient(133deg, #4866fb 0%, #a069ff 100%);
    }
  }
}
</style>
