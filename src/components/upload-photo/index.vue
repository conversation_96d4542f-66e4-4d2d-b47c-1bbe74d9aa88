<script setup lang="ts">
import { getImageUrl } from '@/utils/img'
defineOptions({
  name: 'UploadPhoto'
})

interface Props {
  pic?: string
}

const emit = defineEmits(['select', 'remove'])
const props = withDefaults(defineProps<Props>(), { pic: '' })
</script>
<template>
  <div
    class="photo-wrap rounded-[10px] bg-white border-dashed border-va-grey-text border-[2px] h-[88px] overflow-hidden flex justify-center items-center"
  >
    <template v-if="props.pic">
      <div class="relative w-full h-full" v-preview-image>
        <img :src="props.pic" lazy-load class="w-full h-full object-cover" />
        <div class="absolute right-[10px] bottom-[10px] flex items-center">
          <div
            class="icon-container h-[36px] w-[36px] flex items-center justify-center rounded-[6px]"
          >
            <span
              class="iconfont iconshanchu-mian text-white inline-block text-[24px]"
              @click="emit('remove')"
            ></span>
          </div>
          <div
            class="icon-container h-[36px] w-[36px] flex items-center justify-center ml-[20px] rounded-[6px]"
          >
            <span
              class="iconfont iconzhongpai text-white inline-block text-[24px]"
              @click="emit('select')"
            ></span>
          </div>
        </div>
      </div>
    </template>
    <div
      v-else
      class="plache-holder flex flex-col justify-center items-center w-full h-full"
      @click="emit('select')"
    >
      <img :src="getImageUrl('practice/photo-icon.png')" class="mb-[4px] h-[32px] w-[32px]" />
      <span class="text-[#8F94A8] text-[12px] font-[600]">拍照上传答题结果</span>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.icon-container {
  background-color: rgba(60, 66, 88, 0.5);
}
</style>
