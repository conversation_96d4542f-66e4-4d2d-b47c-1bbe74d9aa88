import BlankFilling from '@/components/quiz-template/BlankFilling.vue'
import MultiChoose from '@/components/quiz-template/MultiChoose.vue'
import SingleChoose from '@/components/quiz-template/SingleChoose.vue'
import OtherQuiz from '@/components/quiz-template/OtherQuiz.vue'

import DoBlankFilling from '@/components/do-quiz-template/DoBlankFilling.vue'
import DoMultiChoose from '@/components/do-quiz-template/DoMultiChoose.vue'
import DoSingleChoose from '@/components/do-quiz-template/DoSingleChoose.vue'
import DoOtherQuiz from '@/components/do-quiz-template/DoOtherQuiz.vue'
import type { ComponentInstance } from 'vue'

export function useGetComp(
  quizType: number | string,
  isDoQuiz: boolean = false,
  chooseByName: boolean = false
): ComponentInstance<any> {
  const QuizCompList = [
    { quizType: 1, comp: SingleChoose },
    { quizType: 5, comp: BlankFilling },
    { quizType: 6, comp: MultiChoose }
  ]
  const DoQuizCompList = [
    { quizType: 1, comp: DoSingleChoose },
    { quizType: 5, comp: DoBlankFilling },
    { quizType: 6, comp: DoMultiChoose }
  ]
  if (isDoQuiz) {
    return DoQuizCompList.find((item) => item.quizType === quizType)?.comp || DoOtherQuiz
  }
  return QuizCompList.find((item) => item.quizType === quizType)?.comp || OtherQuiz
}
