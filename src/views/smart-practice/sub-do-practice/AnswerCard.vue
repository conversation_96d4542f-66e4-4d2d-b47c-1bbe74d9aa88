<script setup lang="ts">
import CardItem from './CardItem.vue'

interface AnswerCardProps {
  show?: boolean
  volName?: string
  currentQuizIdx?: number
  quizList?: Array<{
    index?: number
    quizUid?: string
    hasAnswer?: boolean
    isHalf?: boolean
  }>
}

const emit = defineEmits(['update:show', 'submit-test', 'save-progress', 'click-quiz-item'])
const props = withDefaults(defineProps<AnswerCardProps>(), {
  show: false,
  volName: ''
})

const submitTest = () => {
  emit('submit-test')
  emit('update:show', false)
}
const saveQuiz = () => {
  emit('save-progress')
  emit('update:show', false)
}
const toggleQuizItem = (index: number) => {
  emit('click-quiz-item', index - 1)
  emit('update:show', false)
}

const handleClose = () => {
  emit('update:show', false)
}
</script>
<template>
  <div>
    <van-popup
      position="bottom"
      round
      :show="props.show"
      :close-on-click-overlay="false"
      :style="{ height: '80vh' }"
      class="answer-card-wrapper"
    >
      <div class="container px-[16px] h-[100%] flex flex-col flex-1">
        <div
          class="title-wrap relative w-[100%] p-[20px] pb-[16px] flex items-center justify-center"
        >
          <span class="text-[18px] font-semibold text-va-grey-text">答题卡</span>
          <span class="text-va-primary-text text-[16px] absolute right-[12px]" @click="handleClose"
            >关闭</span
          >
        </div>
        <div class="warning text-va-grey-text-2 text-[12px] pb-[16px]">
          点击题号，前往对应题目：
        </div>
        <div class="quizs w-[100%] flex-1 px-[18px] bg-white rounded-[10px]">
          <div class="title text-[14px] text-va-grey-text pt-[20px] pb-[24px] text-center">
            {{ props.volName }}
          </div>
          <div class="quizs-wrap flex flex-wrap mb-[24px] justify-start mr-[-10px]">
            <CardItem
              v-for="item in quizList"
              :key="item.quizUid"
              :text="item.index as number"
              class="mr-[10px] mb-[12px]"
              :class="{ selected: props.currentQuizIdx === item.index }"
              :finish="item.hasAnswer"
              :is-half="item.isHalf"
              @click="toggleQuizItem(item.index as number)"
            />
          </div>
        </div>
        <div class="btns flex justify-between px-[16px] pt-[36px] pb-[12px] items-center">
          <div
            class="w-[140px] text-[14px] text-center text-va-primary-text py-[10px]"
            @click="saveQuiz"
          >
            保存进度，下次继续
          </div>
          <VaBaseButton text="交卷并查看结果" class="w-[150px] submit-btn" @click="submitTest" />
        </div>
      </div>
    </van-popup>
  </div>
</template>

<style lang="scss" scoped>
:deep(.answer-card-wrapper) {
  border-radius: 20px 20px 0 0;
  border: 1px solid #fff;
  box-shadow: 0px -4px 16px 0px rgba(50, 110, 255, 0.2);
  background-color: #eef3ff;
  box-sizing: border-box;
  .submit-btn {
    font-size: 14px;
    border-radius: 100px;
    padding-block: 8px;
  }
}
</style>
