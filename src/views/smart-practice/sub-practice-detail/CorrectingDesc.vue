<script setup lang="ts">
defineOptions({
  name: 'CorrectingDesc'
})
const props = withDefaults(defineProps<{ desc?: string }>(), { desc: '批改中...' })
const emit = defineEmits(['refresh'])

const handleRefreshInfo = () => {
  emit('refresh')
}
</script>
<template>
  <div class="inline-flex text-[14px] items-center">
    <span class="text-va-grey-text-2 mr-[10px]">{{ props.desc }}</span>
    <span
      class="iconfont iconshuaxin text-va-primary-text text-[18px]"
      @click="handleRefreshInfo"
    ></span>
  </div>
</template>

<style lang="scss" scoped></style>
