<script setup lang="ts">
import { computed } from 'vue'
import type { ToolType, SubMenuItem, ColorOption, BrushSize } from './types'

defineOptions({
  name: 'SubMenu'
})

interface Props {
  toolType: ToolType
  subMenuItems: SubMenuItem[]
  currentConfig: {
    brushSize: number
    color: string
  }
}

interface Emits {
  (e: 'update-config', config: { brushSize?: number; color?: string }): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 预定义的颜色选项
const defaultColors: ColorOption[] = [
  { value: '#000000', label: '黑色' },
  { value: '#ff0000', label: '红色' },
  { value: '#00ff00', label: '绿色' },
  { value: '#0000ff', label: '蓝色' },
  { value: '#ffff00', label: '黄色' }
]

// 预定义的画笔大小选项
const defaultBrushSizes: BrushSize[] = [
  { value: 2, label: '细' },
  { value: 5, label: '小' },
  { value: 10, label: '中' },
  { value: 15, label: '大' },
  { value: 20, label: '粗' }
]

// 预定义的文字大小选项
const defaultFontSizes: BrushSize[] = [
  { value: 12, label: '小' },
  { value: 16, label: '中' },
  { value: 20, label: '大' },
  { value: 24, label: '特大' },
  { value: 32, label: '标题' }
]

// 计算当前工具的配置选项
const colorOptions = computed(() => {
  const colorItem = props.subMenuItems.find((item) => item.type === 'color')
  return colorItem && colorItem.options.length > 0
    ? (colorItem.options as ColorOption[])
    : defaultColors
})

const sizeOptions = computed(() => {
  const sizeItem = props.subMenuItems.find((item) => item.type === 'size')
  // 如果是文字工具，使用字体大小选项
  if (props.toolType === 'text') {
    return sizeItem && sizeItem.options.length > 0
      ? (sizeItem.options as BrushSize[])
      : defaultFontSizes
  }
  // 否则，使用默认的画笔大小选项
  return sizeItem && sizeItem.options.length > 0
    ? (sizeItem.options as BrushSize[])
    : defaultBrushSizes
})

// 是否显示颜色选择
const showColors = computed(() => {
  return props.subMenuItems.some((item) => item.type === 'color')
})

// 是否显示大小选择
const showSizes = computed(() => {
  return props.subMenuItems.some((item) => item.type === 'size')
})

// 选择颜色
const selectColor = (color: string) => {
  emit('update-config', { color })
}

// 选择大小
const selectSize = (size: number) => {
  emit('update-config', { brushSize: size })
}
</script>

<template>
  <div class="sub-menu">
    <!-- 三角箭头指向工具 -->
    <div class="menu-arrow"></div>

    <!-- 颜色选择 -->
    <div v-if="showColors" class="menu-section">
      <div class="color-grid">
        <div
          v-for="color in colorOptions.slice(0, 5)"
          :key="color.value"
          class="color-item"
          :class="{ active: currentConfig.color === color.value }"
          :style="{ backgroundColor: color.value }"
          :title="color.label"
          @click.stop="selectColor(color.value)"
        ></div>
      </div>
    </div>

    <!-- 大小选择 -->
    <div v-if="showSizes" class="menu-section">
      <div class="size-grid">
        <div
          v-for="size in sizeOptions.slice(0, 5)"
          :key="size.value"
          class="size-item"
          :class="{ active: currentConfig.brushSize === size.value }"
          :title="`${size.label} (${size.value}px)`"
          @click.stop="selectSize(size.value)"
        >
          <div
            class="size-dot"
            :style="{
              width: Math.max(Math.min(size.value, 16), 2) + 'px',
              height: Math.max(Math.min(size.value, 16), 2) + 'px',
              backgroundColor: toolType === 'eraser' ? '#808080' : currentConfig.color
            }"
          ></div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.sub-menu {
  position: absolute;
  top: 40px;
  left: 50%;
  transform: translateX(-50%);
  width: 200px;
  background: #ffffff;
  border: 1px solid #dcdcdc;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 12px;
  z-index: 1000;

  .menu-arrow {
    position: absolute;
    top: -6px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-bottom: 6px solid #ffffff;
    z-index: 1001;

    &::before {
      content: '';
      position: absolute;
      top: 1px;
      left: -7px;
      width: 0;
      height: 0;
      border-left: 7px solid transparent;
      border-right: 7px solid transparent;
      border-bottom: 7px solid #dcdcdc;
      z-index: -1;
    }
  }

  .menu-section {
    margin-bottom: 10px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .color-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 8px;
    place-items: center;

    .color-item {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      cursor: pointer;
      border: 2px solid transparent;

      &:hover {
        border-color: #999;
      }

      &.active {
        border-color: #0066ff;
      }
    }
  }

  .size-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 8px;
    place-items: center;

    .size-item {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      cursor: pointer;
      border: 2px solid transparent;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;

      &.active {
        border-color: #0066ff;
      }

      .size-dot {
        border-radius: 50%;
        transition: all 0.2s ease;
      }
    }
  }
}
</style>
