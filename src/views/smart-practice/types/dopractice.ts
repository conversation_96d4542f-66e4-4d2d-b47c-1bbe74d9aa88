export interface QuizAnswer {
  /**
   * 题目类型
   */
  quizType?: number

  /**
   * 题目uid
   */
  quizUid?: string

  /**
   * 题目版本
   */
  quizVersion?: number

  /**
   * 用户答案列表
   */
  userAnswerList?: string[]

  quizSeqNo?: number
}

export interface DoQuizItemType {
  quizSeqNo?: number | string
  quizUid?: string
  quizType?: number
  quizVersion?: number
  userAnswerList?: any[]
  score?: number
  options?: any[]
}

export interface QuizAnswerMapType {
  [quizUid: string]: QuizAnswer
}
