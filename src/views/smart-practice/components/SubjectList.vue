<template>
  <div class="flex flex-wrap gap-4 px-4">
    <div
      v-for="subject in subjects"
      :key="subject.id"
      class="w-[calc(50%-8px)] bg-white rounded-[10px] p-4 flex flex-col items-center cursor-pointer"
      @click="$emit('select', subject)"
    >
      <div
        class="w-[60px] h-[60px] bg-[#F1F4F7] rounded-full flex items-center justify-center mb-2"
      >
        <img v-if="subject.icon" :src="subject.icon" class="w-[40px] h-[40px]" alt="subject icon" />
      </div>
      <span class="text-[16px] font-medium">{{ subject.name }}</span>
      <span class="text-[12px] text-gray-500 mt-1">{{ subject.description }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Subject {
  id: number | string
  name: string
  description?: string
  icon?: string
}

defineProps<{
  subjects: Subject[]
}>()

defineEmits<{
  (e: 'select', subject: Subject): void
}>()
</script>
