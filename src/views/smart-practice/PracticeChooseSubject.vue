<script setup lang="ts">
import config from '@/config'
import { useLoading } from '@/hooks'
import { useGlobal } from '@/hooks/useGlobal'
import { buriedPointsMap } from '@/model/buried-point'
import type {
  DictTagData,
  ExamSubjectTagReqDTO,
  UserSubjectTagAddReqDTO,
  UserSubjectTagInfo
} from '@/services/practice/types'
import SubjectItem from '@/views/smart-practice/components/SubjectItem.vue'
import { computed, nextTick, onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
const { startLoading, endLoading } = useLoading()
const { globalProperties } = useGlobal()
const router = useRouter()

// 科目数据结构
interface SubjectData {
  title: string
  value: number
  childrenList?: SubjectData[]
  selected?: number
  volYear?: number
}

interface SubjectsData {
  dataList: SubjectData[]
}

const emit = defineEmits<{
  (e: 'update:show', value: boolean): void
  (e: 'confirm'): void
}>()

const activeTab = ref(0)
const selectedSubjects = ref<string[]>([])

// 使用 Map 保存每个 tab 下的选中状态
const tabSelections = ref(
  new Map<
    number,
    {
      selectedSubject: number
      subjects: SubjectsData
    }
  >()
)

// examList
const examList = ref<DictTagData[]>([])

// Get current year
const currentYear = new Date().getFullYear()

const toggleSubjectSelection = (boardName: string, subjectName: string) => {
  if (!subjects.value.dataList) return

  // 找到父科目
  const parentSubject = subjects.value.dataList.find((item) => item.title === boardName)
  if (!parentSubject || !parentSubject.childrenList) return

  // 找到子科目
  const childSubject = parentSubject.childrenList.find((item) => item.title === subjectName)
  if (!childSubject) return

  // 切换选中状态
  childSubject.selected = childSubject.selected === 1 ? 0 : 1

  // 更新当前tab的缓存数据
  tabSelections.value.set(activeTab.value, {
    selectedSubject: selectedSubject.value,
    subjects: subjects.value
  })
}

const closePopup = () => {
  // 返回上一页，使用goBack函数
  goBack()
}

// 保存数据到服务器
const saveSubjects = async () => {
  try {
    // 收集所有tab下的选中科目
    const selectedSubjects: UserSubjectTagInfo[] = []

    // 遍历所有tab的数据
    tabSelections.value.forEach((tabData, tabIndex) => {
      const examValue = examList.value[tabIndex]?.value
      if (!examValue) return

      tabData.subjects.dataList?.forEach((item) => {
        item.childrenList?.forEach((child: any) => {
          if (child.selected === 1) {
            selectedSubjects.push({
              exam: Number(examValue),
              examBoard: Number(item.value),
              subject: Number(child.value)
            })
          }
        })
      })
    })

    // 还需要处理当前tab的数据（可能尚未保存到tabSelections中）
    if (!tabSelections.value.has(activeTab.value)) {
      const currentExamValue = getCurrentExamValue.value
      getCurrentSubjects.value.forEach((item) => {
        item.childrenList?.forEach((child: any) => {
          if (child.selected === 1) {
            selectedSubjects.push({
              exam: Number(currentExamValue),
              examBoard: Number(item.value),
              subject: Number(child.value)
            })
          }
        })
      })
    }

    const params: UserSubjectTagAddReqDTO = {
      subscribeList: selectedSubjects
    }
    await globalProperties.$http.Practice.UserManagerController.addUserSubscribe(params)
    // 返回上一页
    closePopup()
  } catch (error) {
    console.error('保存科目数据失败:', error)
  }
}

const handleConfirm = () => {
  // 保存数据到服务器
  saveSubjects()
}

// Add a new ref to track the selected board
const selectedBoard = ref<number>(0)

// Refs for scrolling and board elements
const rightAreaRef = ref<HTMLElement | null>(null)
const leftAreaRef = ref<HTMLElement | null>(null)

// 当前选中的考试类别 (Alevel, GCSE, IB, AP)
const getCurrentExamType = computed(() => {
  if (!examList.value || examList.value.length === 0) {
    return ''
  }
  return examList.value[activeTab.value]?.title || ''
})

//
const getCurrentExamValue = computed(() => {
  if (!examList.value || examList.value.length === 0) {
    return ''
  }
  return examList.value[activeTab.value]?.value || ''
})

const handleRightAreaScroll = () => {
  if (!rightAreaRef.value) return

  const scrollTop = rightAreaRef.value.scrollTop
  const subjects = getCurrentSubjects.value

  // 找到当前可见的科目
  for (const subject of subjects) {
    const element = document.querySelector(`[data-subject-title="${subject.title}"]`) as HTMLElement
    if (!element) continue

    const elementTop = element.offsetTop
    const elementBottom = elementTop + element.offsetHeight

    // 如果科目在可视区域内
    if (elementTop <= scrollTop && elementBottom > scrollTop) {
      // 更新选中的科目
      selectedSubject.value = subject.value
      break
    }
  }
}

const handleLeftAreaScroll = () => {
  if (!leftAreaRef.value || !rightAreaRef.value) return

  const scrollTop = leftAreaRef.value.scrollTop
  rightAreaRef.value.scrollTo({
    top: scrollTop,
    behavior: 'smooth'
  })
}

const selectedSubject = ref<number>(0)

// Refs for scrolling and subject elements

// 滚动到指定科目
const scrollToSubject = (subjectTitle: string) => {
  nextTick(() => {
    if (!rightAreaRef.value) return

    // 找到对应标题的元素
    const targetElement = document.querySelector(
      `[data-subject-title="${subjectTitle}"]`
    ) as HTMLElement

    if (targetElement) {
      rightAreaRef.value.scrollTo({
        top: targetElement.offsetTop,
        behavior: 'smooth'
      })
    }
  })
}

// 处理科目选择
const selectSubject = (subject: SubjectData) => {
  selectedSubject.value = subject.value
  scrollToSubject(subject.title)

  // 更新缓存
  const currentCache = tabSelections.value.get(activeTab.value)
  if (currentCache) {
    currentCache.selectedSubject = selectedSubject.value
    tabSelections.value.set(activeTab.value, currentCache)
  } else {
    tabSelections.value.set(activeTab.value, {
      selectedSubject: selectedSubject.value,
      subjects: subjects.value
    })
  }
}

// 格式化科目名称
const formatSubjectName = (name: string) => {
  return name
}

// 获取科目已选中的子科目数量
const getSelectedChildrenCount = (subject: SubjectData): number => {
  if (!subject.childrenList) return 0

  return subject.childrenList.filter((child) => child.selected === 1).length
}

// get exam boards
const getExamList = async () => {
  try {
    startLoading()
    const response = await globalProperties.$http.Practice.VolQuizManagerController.getExamList()
    if (response.data) {
      // 转换为自定义类型
      examList.value = response.data.dataList || []

      // 初始化选中第一个tab和第一个考试局
      if (examList.value && examList.value.length > 0) {
        activeTab.value = 0
        const firstExamType = examList.value[0]

        if (firstExamType && firstExamType.value !== undefined) {
          // 设置初始selectedBoard
          selectedBoard.value = Array.isArray(firstExamType.value)
            ? firstExamType.value[0]
            : firstExamType.value

          // 获取科目列表
          await getSubjects()

          // 设置第一个科目为选中状态
          if (getCurrentSubjects.value.length > 0) {
            selectedSubject.value = getCurrentSubjects.value[0].value
            // 更新缓存
            tabSelections.value.set(activeTab.value, {
              selectedSubject: selectedSubject.value,
              subjects: subjects.value
            })
          }
        }
      }
    }
  } catch (error) {
    console.error('Error fetching exam boards:', error)
  } finally {
    endLoading()
  }
}

// 根据exam boards获取科目列表
const getSubjects = async () => {
  try {
    const params: ExamSubjectTagReqDTO = {
      exam: selectedBoard.value
    }
    const response =
      await globalProperties.$http.Practice.VolQuizManagerController.getExamSubjectTagList(params)
    if (response.data) {
      subjects.value = response.data as SubjectsData

      // 设置第一个科目为选中状态
      if (getCurrentSubjects.value.length > 0) {
        selectedSubject.value = getCurrentSubjects.value[0].value
      }

      // 缓存数据
      tabSelections.value.set(activeTab.value, {
        selectedSubject: selectedSubject.value,
        subjects: subjects.value
      })
    }
  } catch (error) {
    console.error('获取科目列表失败:', error)
  }
}

// 当Tab切换时，更新selectedBoard并获取科目列表
const handleTabChange = async (index: number) => {
  activeTab.value = index
  const boardValue = examBoardsList.value[index]?.value
  selectedBoard.value = typeof boardValue === 'number' ? boardValue : 0

  // 强制重新加载数据
  const params: ExamSubjectTagReqDTO = {
    exam: selectedBoard.value
  }
  const response =
    await globalProperties.$http.Practice.VolQuizManagerController.getExamSubjectTagList(params)
  if (response.data) {
    subjects.value = response.data as SubjectsData

    // 设置第一个科目为选中状态
    if (getCurrentSubjects.value.length > 0) {
      selectedSubject.value = getCurrentSubjects.value[0].value
    }

    // 更新缓存
    tabSelections.value.set(activeTab.value, {
      selectedSubject: selectedSubject.value,
      subjects: subjects.value
    })
  }
}

// 创建一个计算属性来获取examBoards的dataList，避免在模板中直接访问value属性
const examBoardsList = computed(() => {
  return examList.value || []
})

// 添加科目列表状态
const subjects = ref<SubjectsData>({ dataList: [] })

// 获取当前选中考试类型下的所有科目
const getCurrentSubjects = computed(() => {
  if (!subjects.value.dataList) return []

  // 直接返回科目列表，使用接口返回的 volYear
  return subjects.value.dataList
})

// 每个科目类型显示的初始数量
const INITIAL_SUBJECT_COUNT = 10

// 跟踪每个科目类型的展开状态
const expandedSubjects = ref<Record<string, boolean>>({})

// 切换科目展开状态
const toggleSubjectExpand = (subjectTitle: string) => {
  expandedSubjects.value[subjectTitle] = !expandedSubjects.value[subjectTitle]
}

// 获取是否应该显示"更多"按钮
const shouldShowMore = (childrenList: SubjectData[] | undefined) => {
  return childrenList && childrenList.length > INITIAL_SUBJECT_COUNT
}

// 获取要显示的子科目列表
const getVisibleChildSubjects = (subject: SubjectData) => {
  if (!subject.childrenList) return []

  const isExpanded = expandedSubjects.value[subject.title]
  if (isExpanded) {
    return subject.childrenList
  }
  return subject.childrenList.slice(0, INITIAL_SUBJECT_COUNT)
}

// 获取用户已订阅的科目
const loadUserSubscribedSubjects = async () => {
  try {
    // 获取用户已订阅的科目列表
    const response =
      await globalProperties.$http.Practice.UserManagerController.getUserSubjectInfoList()
    const subscribedSubjects = response?.data?.dataList || []
    // 预先加载所有tab的数据
    for (let i = 0; i < examList.value.length; i++) {
      // 如果还没有加载过这个tab的数据，先加载
      if (!tabSelections.value.has(i)) {
        const examValue = examList.value[i]?.value
        if (!examValue) continue

        const params: ExamSubjectTagReqDTO = {
          exam: Number(examValue)
        }
        const subjectsResponse =
          await globalProperties.$http.Practice.VolQuizManagerController.getExamSubjectTagList(
            params
          )

        if (subjectsResponse.data) {
          const subjectsData = subjectsResponse.data as SubjectsData

          // 将用户已订阅的科目标记为选中
          subjectsData.dataList?.forEach((board) => {
            board.childrenList?.forEach((subject) => {
              // 检查该科目是否在用户订阅列表中
              const isSubscribed = subscribedSubjects.some(
                (item) =>
                  item.exam === Number(examValue) &&
                  item.examBoard === Number(board.value) &&
                  item.subject === Number(subject.value)
              )

              // 如果已订阅，标记为选中
              if (isSubscribed) {
                subject.selected = 1
              }
            })
          })

          // 缓存数据
          tabSelections.value.set(i, {
            selectedSubject: 0, // 默认值
            subjects: subjectsData
          })
        }
      }
    }
  } catch (error) {
    console.error('获取用户订阅科目失败:', error)
  }
}

onMounted(async () => {
  globalProperties.$aplusPush.pushEvent({ eventCode: buriedPointsMap['practice_choose-subject'] })
  // Initialize data if the popup is already shown when mounted
  await getExamList()
  // 加载用户已订阅的科目
  await loadUserSubscribedSubjects()
})

const paddingTop = computed(() => {
  const appInfoStr = localStorage.getItem(config.appInfoKey)
  if (!appInfoStr) return '44px'
  const appInfo = JSON.parse(appInfoStr)
  return `${Number(appInfo.safeAreaTop || 10) + 44}px`
})

const containerStyle = computed(() => {
  return {
    paddingTop: paddingTop.value
  }
})

const goBack = () => {
  router.go(-1)
}
</script>

<template>
  <div class="flex flex-col h-full relative" :style="containerStyle">
    <!-- 头部背景图 -->
    <div class="header-bg"></div>

    <common-nav :leftArrow="true" :showBackground="false" title="选择科目" @back="goBack" />

    <!-- tab区域固定在顶部 -->
    <div class="tab-container relative z-10 mb-0">
      <van-tabs v-model:active="activeTab" swipeable @change="handleTabChange">
        <van-tab v-for="(item, index) in examBoardsList" :key="index">
          <template #title>
            <span class="artfont">{{ item.title || '' }}</span>
          </template>
        </van-tab>
      </van-tabs>
    </div>

    <!-- 内容区域单独滚动 -->
    <div class="content-container overflow-hidden">
      <div class="flex h-full">
        <!-- left area with independent scroll -->
        <div
          ref="leftAreaRef"
          class="board-list w-[96px] md:w-[148px] overflow-y-auto h-full bg-[#F8F9FC]"
          @scroll="handleLeftAreaScroll"
        >
          <div
            v-for="subject in getCurrentSubjects"
            :key="subject.value"
            class="board-item flex items-center justify-between cursor-pointer min-h-[48px] h-auto px-[17px] py-2"
            :class="{ 'active bg-white text-[#326EFF]': selectedSubject === subject.value }"
            @click="selectSubject(subject)"
          >
            <div class="flex items-center relative">
              <div
                v-if="getSelectedChildrenCount(subject) > 0"
                class="absolute -top-1 -left-1 flex items-center justify-center w-[21px] h-[12px] bg-[#326EFF] text-white rounded-full text-[10px]"
              >
                {{ getSelectedChildrenCount(subject) }}
              </div>
              <span
                class="artfont text-[14px] font-[500] md:whitespace-nowrap md:overflow-hidden md:text-ellipsis break-words md:break-normal board-name"
                :class="{ 'active font-bold': selectedSubject === subject.value }"
                >{{ subject.title }}</span
              >
            </div>
          </div>
        </div>

        <!-- right area with independent scroll -->
        <div
          ref="rightAreaRef"
          class="flex-1 overflow-y-auto h-full bg-white md:pl-[16px]"
          @scroll="handleRightAreaScroll"
        >
          <div
            v-for="subject in getCurrentSubjects"
            :key="subject.value"
            :data-subject-title="subject.title"
            class="mb-4"
          >
            <div class="flex items-center pt-[25px] pl-[13px] justify-between md:pr-[34px]">
              <span class="artfont text-[16px] font-bold text-[#3C4258]">{{
                formatSubjectName(subject.title)
              }}</span>
              <span class="text-[11px] text-[#888] mr-[16px]">
                已更新至{{ subject.volYear }}年
              </span>
            </div>

            <div class="flex flex-wrap gap-[10px] pt-[13px] px-[13px] relative">
              <SubjectItem
                v-for="childSubject in getVisibleChildSubjects(subject)"
                :key="childSubject.value"
                :subject="{
                  name: childSubject.title,
                  updated: currentYear + '年',
                  selected: childSubject.selected === 1
                }"
                @select="toggleSubjectSelection(subject.title, childSubject.title)"
              />

              <!-- 显示更多按钮容器 -->
              <div
                v-if="shouldShowMore(subject.childrenList)"
                class="w-full flex justify-center mt-2"
              >
                <div
                  class="more-subjects-btn inline-flex items-center justify-center px-4 py-[6px] rounded-[4px] cursor-pointer"
                  :class="expandedSubjects[subject.title] ? 'text-[#326EFF]' : 'text-[#999]'"
                  @click="toggleSubjectExpand(subject.title)"
                >
                  <span class="text-[14px]">{{
                    expandedSubjects[subject.title] ? '收起' : '更多科目'
                  }}</span>
                  <van-icon
                    :name="expandedSubjects[subject.title] ? 'arrow-up' : 'arrow-down'"
                    class="ml-1 text-[12px]"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部区域 -->
    <div
      class="flex justify-between items-center w-full h-[60px] bg-white px-4 border-t border-[#F0F1F7]"
    >
      <span class="text-[16px]" @click="closePopup">取消</span>
      <button
        class="w-[200px] h-[40px] bg-[#326EFF] text-white rounded-[40px] flex items-center justify-center shadow-[0px_4px_10px_0px_rgba(25,93,255,0.2002),inset_0px_-3px_0px_0px_#1358fd]"
        @click="handleConfirm"
      >
        <span class="text-[18px]">确定</span>
      </button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
/* 仅保留需要覆盖的 Vant 组件样式 */
:deep(.van-tabs) {
  .van-tabs__wrap {
    .van-tabs__nav {
      background: transparent;
      padding-left: 16px !important;
      justify-content: flex-start !important;
    }

    .van-tab {
      padding: 0 16px !important;
      color: #3c4258;
      font-size: 16px;
      font-weight: 700;
      margin-right: 0 !important;
      flex: none !important;
    }

    .van-tab--active {
      color: #0256ff !important;
      font-weight: bold;
    }

    .van-tabs__line {
      background-color: #0256ff;
      width: 20px !important;
      height: 4px;
      border-radius: 2px;
    }
  }

  .van-tabs__content {
    padding-bottom: 0 !important;
  }
}

/* 左侧栏文本换行样式 */
.board-name {
  display: inline-block;
  width: 62px;
  word-wrap: break-word;
  overflow-wrap: break-word;
  word-break: break-all;
  position: relative;
}

/* 使用 Tailwind 的 md: 断点(768px)处理响应式 */
@media (min-width: 768px) {
  .board-name {
    width: auto;
    max-width: 120px;
  }
}

/* 更多科目按钮样式 - 使用特定的类名避免样式污染 */
.more-subjects-btn {
  transition: all 0.3s ease;

  &:hover {
    opacity: 0.8;
  }

  :deep(.van-icon) {
    display: flex;
    align-items: center;
  }
}
</style>

<style scoped>
.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 140px;
  background-image: url('@/assets/images/practice/top_bg_2.png');
  background-size: 100% 100%;
  background-position: top center;
  background-repeat: no-repeat;
  z-index: 1;
}

.tab-container {
  position: relative;
  z-index: 10;
}

.content-container {
  background-color: #f1f4f7;
  margin-top: 0;
  position: relative;
  z-index: 10;
  flex: 1;
}

.board-item {
  transition: all 0.3s ease;
}

.board-item.active {
  position: relative;
}

.board-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 16px;
  background-color: #326eff;
  border-radius: 0 2px 2px 0;
}

.board-item {
  background-color: transparent;
  color: #3c4258;
  border-color: rgba(60, 66, 88, 0.3);
}

.board-item.active {
  background-color: white;
  color: #3c4258;
  border-color: white;
  font-weight: bold;
}

.subject-container {
  background-color: white;
}
</style>
