# global-h5-v3

all in vue3 global H5 and vscode extensions recomand list as below

- Vue-Official
- Tailwind CSS IntelliSense

### TODOs

- [x] router
- [x] pinia for state manangement and persistence
- [x] permission
- [x] vant
- [x] rem for Responsive design (tailwindcss lib already surpport rem)
- [ ] international i18
- [x] tailwindcss css framework
- [x] axios for request
- [x] vue-use for hooks
- [x] eslint 、prettier、 commitlint、husky、lint-staged
- [x] mitt for event bus
- [x] vconsole for debug
- [ ] message with flutter
- [ ] 兼容合约 - jwt retry

## WebBridge

- 发送信息flutter 必须要有回复 否则 页面一直处于等待状态
- flutter调用web（event for app useage）

### 使用示例：

```ts
import { webBridgeInstance } from '@/utils/webBridge'
// web send msg to flutter
const res = await webBridgeInstance.sendMsg({ type: 'getToken' })
console.log('res', res)

// 推荐方式：使用 useFlutterEvent hooks（更简洁）
import { useFlutterEvent } from '@/hooks'

export default {
  setup() {
    const { on, FLUTTER_EVENTS } = useFlutterEvent()

    // 注册事件监听器（自动清理）
    on(FLUTTER_EVENTS.getSinglePhoto, (data) => {
      console.log('处理照片:', data)
    })

    return {}
  }
}
```
