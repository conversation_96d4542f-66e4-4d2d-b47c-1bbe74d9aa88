<script setup lang="ts">
import { useGlobal, useLoading } from '@/hooks'
import { ref } from 'vue'
import { useRoute } from 'vue-router'

import { getImageUrl } from '@/utils/img'

const { startLoading, endLoading } = useLoading()
const { globalProperties } = useGlobal()

const routes = useRoute()

const comfirmLetterInfo = ref<any>(null)

const getConfirmDetail = async () => {
  try {
    startLoading()

    const res = await globalProperties.$api.SelectionConfirmLetter.confirmLetterByUid({
      uid: routes?.query?.uid as string
    })

    comfirmLetterInfo.value = res
  } finally {
    endLoading()
  }
}

const formatterApplyBatchEndDate = (val: string) => {
  return val?.includes('fuck#') ? val.split('fuck#')[1] : val
}

getConfirmDetail()
</script>

<template>
  <div class="oak-oasis-confirm-letter-container">
    <div v-if="comfirmLetterInfo" class="main-content">
      <div class="header-logo">
        <img :src="getImageUrl('oak/logo-horizontal.png')" alt="" />
      </div>
      <div class="title-header">
        <img :src="getImageUrl('oak/title-icon-left.png')" alt="" />
        <span class="label">选校确认函</span>
        <img :src="getImageUrl('oak/title-icon-right.png')" alt="" />
      </div>
      <p class="part">甲方/监护人：{{ comfirmLetterInfo.guardian }}</p>
      <p class="part part-tip">
        若甲方未满18周岁，请甲方监护人填写以下信息，并由此甲方监护人签署本协议：
      </p>
      <p class="part">乙方： 上海橡沐教育科技有限公司</p>
      <p class="part">
        本选校协议作为甲方与乙方，于{{ comfirmLetterInfo.signedYear }}年{{
          comfirmLetterInfo.signedMonth
        }}月签订的《留学申请服务合同》的附件，与原合同同具法律效力。
      </p>
      <p class="part">经双方协商一致，现决定由乙方为甲方申请如下院校和专业：</p>
      <div class="sub-title">申请项目</div>
      <div
        v-for="apply in comfirmLetterInfo.studentApplyList"
        :key="apply.applyRecordUid"
        class="school-item"
      >
        <table class="apply-school-table">
          <caption>
            {{
              apply.schoolName
            }}
          </caption>
          <tbody>
            <tr>
              <td :rowspan="apply.applyMajorList.some((item: any) => item.majorTag) ? 3 : 2">
                <van-image
                  :src="
                    apply.schoolLogo ||
                    apply.schoolLogeUrl ||
                    'https://js-picture.oss-cn-hangzhou.aliyuncs.com/college-library/ddd7394a-faf3-4487-89c7-cd7011992a5b.jpg'
                  "
                  lazy-load
                  fit="cover"
                />
              </td>
              <td>{{ apply.studyDirectionTitle || apply.studyDirectionName }}</td>
              <td>{{ apply.degreeTypeTitle }}</td>
              <td>{{ `${apply.entranceYear}(${apply.entranceSeason})` }}</td>
              <td>{{ formatterApplyBatchEndDate(apply.applyBatchEndDate) }}</td>
            </tr>
            <tr>
              <td colspan="4">
                <div v-for="major in apply.applyMajorList" :key="major.majorUid">
                  <p>{{ major.majorName }}</p>
                </div>
                <template v-if="apply.applyConnect && apply.applyConnect.length">
                  {{ `、${apply.applyConnect.map((major: any) => major.majorName).join('、')}` }}
                </template>
              </td>
            </tr>
            <tr v-if="apply.applyMajorList.some((item: any) => item.majorTag)">
              <td colspan="4">
                <div v-for="major in apply.applyMajorList" :key="major.majorUid">
                  <p v-if="major.majorTag">
                    {{ major.majorTag.tagAliasLine }}
                  </p>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <p class="part important-tip">
        特殊需求：加州大学申请系统为统一系统，加州大学体系学校共计一个申请名额。
      </p>
      <p class="part">
        综上所述，经双方协商一致，乙方累计申请
        <strong>{{ comfirmLetterInfo.cumulativeApplyCount }}</strong> 所，申请院校数量变更
        <strong>{{ comfirmLetterInfo.changeApplyCount }}</strong>
        所，根据原合同的规定，服务费累计增加金额人民币
        <strong>{{ comfirmLetterInfo.serviceCharge }}</strong> 元。
      </p>
      <p class="part">
        本选校协议一经签订，将严格按照本协议中所列学校开始申请工作，如在申请过程中，有任何一方需要变更其中的任何一所学校都需要双方协商一致后另行签订《选校协议》就所调整的学校进行明确规定及调整，并双方签字确认。
      </p>
      <p class="part important-tip">
        特别提醒，文书写作过程中，根据申请院校要求，强烈建议不要使用AI工具。如因使用AI工具导致权威检测机构Turnitin检测存在AI，须由学生自行降低AI率。因学生坚持递交使用AI工具生成或修改的文书导致的任何不利后果，由学生和家长（监护人）自行承担。
      </p>
      <p class="part important-tip">
        本确认函一经签署，代表文书指导开启，因调整申请方案导致额外费用的产生或任何不利后果，由学生和家长（监护人）自行承担。
      </p>
      <p class="part">本协议自双方签字确认后即生效，之前签署的选校协议均失效。</p>
      <p class="part">本协议传真有效。</p>
      <div class="bottom-logo">
        <img :src="getImageUrl('oak/logo-vertical.png')" alt="" />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.oak-oasis-confirm-letter-container {
  width: 100vw;
  height: 100vh;
  overflow-y: auto;
  .main-content {
    padding: 14px 15px;
    box-sizing: border-box;
  }
  .header-logo {
    width: 150px;
    margin: 0 auto;
    img {
      width: 100%;
    }
  }
  .title-header {
    display: flex;
    margin: 25px 0;
    justify-content: center;
    align-items: center;
    .label {
      padding: 0 12px;
      font-size: 24px;
      font-weight: 600;
      line-height: 38px;
      color: #003764;
    }
    img {
      width: 42px;
      height: 14px;
    }
  }
  .sub-title {
    position: relative;
    width: max-content;
    margin: 15px auto;
    font-size: 20px;
    font-weight: 600;
    line-height: 50px;
    color: #000;
    text-align: center;
    &::before,
    &::after {
      position: absolute;
      top: 18px;
      width: 14px;
      height: 14px;
      background: linear-gradient(180deg, #24dc96 0%, #197250 100%);
      border-radius: 2px;
      content: '';
    }
    &::before {
      left: -34px;
    }
    &::after {
      right: -34px;
    }
  }
  .part {
    margin: 0;
    margin-bottom: 10px;
    font-size: 14px;
    line-height: 26px;
    color: #333;
    word-break: break-all;
    &.part-tip {
      color: #197250;
    }
    &.important-tip {
      color: #ff8300;
    }
  }
  .school-item {
    margin-bottom: 20px;
    overflow: hidden;
    border-radius: 6px;
    .apply-school-table {
      width: 100%;
      font-family: sans-serif;
      letter-spacing: 2px;
      border: 2px solid #cbdeff;
      border-collapse: collapse;
      box-shadow: 0 0 6px 0 rgba(85, 139, 237, 0.2);
      caption {
        width: 100%;
        padding: 8px 10px;
        font-size: 14px;
        font-weight: bold;
        color: #000;
        text-align: left;
        border: 2px solid #cbdeff;
        border-bottom: none;
        box-sizing: border-box;
        caption-side: top;
      }
      th,
      td {
        padding: 5px 8px;
        text-align: center;
        border: 2px solid #cbdeff;
      }
      tbody tr {
        td {
          font-size: 12px;
          font-weight: 400;
          line-height: 30px;
          color: #333;
          &:nth-child(1) {
            width: 94px;
          }
          &:nth-child(2) {
            width: 121px;
          }
          &:nth-child(3) {
            width: 121px;
          }
        }
      }
    }
  }
  .bottom-logo {
    margin: 30px 0 15px;
    display: flex;
    justify-content: flex-end;
    img {
      width: 80px;
    }
  }
}
</style>
