<script setup lang="ts">
import { useGlobal, useLoading } from '@/hooks'
import type { TQuizOutData, VolGroupUidVO } from 'va.study.service.volquiz'
import { showToast } from 'vant'
import { onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import { previewImg } from '@/utils/img'
const routes = useRoute()
const quizs = ref<TQuizOutData[]>([])
const { globalProperties } = useGlobal()
const { startLoading, endLoading } = useLoading()

onMounted(() => {
  getQuizList()
})

const getQuizList = async () => {
  startLoading()
  try {
    const params: VolGroupUidVO = {
      volGroupUid: routes.query.volGroupUid as string,
      groupUid: routes.query.groupUid as string
    }
    const res = await globalProperties.$api.VolQuiz.getQuizList(params)
    // quizs.value = (res?.quizs || []).filter((item) => item.quizType === 7) || []
    quizs.value = res?.quizs || []
  } catch (e) {
    showToast({
      message: '网络繁忙',
      duration: 1000
    })
  } finally {
    endLoading()
  }
}
</script>
<template>
  <div class="quiz-wrap bg-white h-[100%] px-[15px] py-[10px] text-[14px]">
    <div
      v-html="item.content"
      @click="previewImg"
      v-for="(item, index) in quizs"
      :key="index"
    ></div>
  </div>
</template>

<style lang="scss" scoped></style>
