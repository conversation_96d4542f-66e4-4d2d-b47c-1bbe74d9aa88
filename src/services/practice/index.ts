/* eslint-disable no-unused-vars */
/* eslint-disable camelcase */
/* eslint-disable @typescript-eslint/no-unused-vars */
import type {
  AnsweringQuestionChatRecordQueryDTO,
  AnsweringQuestionChatRecordReqDTO,
  AnsweringQuestionChatRecordRespDTO,
  CorrectQuizAIResult,
  DictTagData,
  ExamBoardTagNumberData,
  ExamBoardTagNumberParam,
  ExamBoardTagNumberReqDTO,
  ExamBoardTagNumberResDTO,
  ExamSubjectDictTagData,
  ExamSubjectTagReqDTO,
  ExamSubjectTagResDTO,
  ListVolAnswerDetailRespVO,
  ListVolQuizDetailReqVO,
  ListVolQuizzesReqVO,
  ListVolQuizzesRespVO,
  PagePracticeAnswerDetailReqVO,
  PagePracticeAnswerDetailRespVO,
  PageResult,
  PaperAiCallbackDTO,
  PracticeAnswerResultItem,
  PracticeAnswerResultResp,
  PracticeAnswerStateItem,
  PracticeListQueryReqDTO,
  PracticeRecordBaseReqDTO,
  PracticeResultAnalysisQueryReqDTO,
  PracticeResultAnalysisQueryResDTO,
  PracticeStartReqDTO,
  PracticeStartResDTO,
  PracticeSubmitReqDTO,
  QuizAnalysisVO,
  QuizAnswerVO,
  QuizAttachmentVO,
  QuizKnowledgeData,
  QuizKnowledgeVO,
  QuizOptionVO,
  QuizOutDTO,
  QuizQueryReqDTO,
  QuizQueryRespDTO,
  QuizSubmitAnswerDetailReqDTO,
  QuizSubmitAnswerReqDTO,
  QuizUidVersionVO,
  QuizWrongBookPageReqDTO,
  Result,
  SubjectTagResDTO,
  SubmitWrongBookAnswerReqDTO,
  TQuizAnalysisData,
  TQuizAnswerData,
  TQuizAnswerDataVO,
  TQuizAttachmentData,
  TQuizOptionData,
  TVolQuizDataVO,
  TVolQuizOutData,
  TagData,
  UserHeaderCountResDTO,
  UserPracticeReportCalcReqDTO,
  UserPracticeReportCalcRespDTO,
  UserSubjectTagAddReqDTO,
  UserSubjectTagData,
  UserSubjectTagInfo,
  UserSubjectTagInfoData,
  UserSubjectTagInfoResDTO,
  UserSubjectTagRemoveReqDTO,
  UserSubjectTagResDTO,
  VXiaoXunAskReq,
  VXiaoXunAskResp,
  VolListData,
  VolListQueryReqDTO,
  VolListQueryResDTO,
  VolTagReqDTO,
  VolTagResDTO,
  WrongBookBaseReq,
  WrongBookCategoryStatsRespDTO,
  WrongBookStatsReqDTO,
  WrongBookSubjectStatsRespDTO,
  PracticeResultQueryController,
  QuizAnswerProcessController,
  AnsweringQuestionChatRecordController,
  TestController,
  UserManagerController,
  VolQuizManagerController,
  WrongBookAnswerController,
  VXiaoXunController,
  ServiceHandler
} from './types'
import $http from '../request'
import type { AxiosRequestConfig } from 'axios'
import type { BaseRequestDTO } from '../types'

export * from './types'

const basePath = ''

export const practiceResultQueryController: PracticeResultQueryController = {
  async calcUserPracticeReport(
    req: UserPracticeReportCalcReqDTO,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<UserPracticeReportCalcRespDTO>> {
    const path = `${basePath}/api/practice/ks/calcUserPracticeReport`
    const payload: UserPracticeReportCalcReqDTO = req
    const ret = await $http.run<
      UserPracticeReportCalcReqDTO,
      Result<UserPracticeReportCalcRespDTO>
    >(path, 'POST', payload, axiosConfig)
    return ret
  },

  async queryAnswerResult(
    reqDTO: PracticeRecordBaseReqDTO,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<PracticeAnswerResultResp>> {
    const path = `${basePath}/api/practice/ks/queryAnswerResult`
    const payload: PracticeRecordBaseReqDTO = reqDTO
    const ret = await $http.run<PracticeRecordBaseReqDTO, Result<PracticeAnswerResultResp>>(
      path,
      'POST',
      payload,
      axiosConfig
    )
    return ret
  },

  async queryAnswerState(
    reqDTO: PracticeRecordBaseReqDTO,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<Array<PracticeAnswerStateItem>>> {
    const path = `${basePath}/api/practice/ks/queryAnswerState`
    const payload: PracticeRecordBaseReqDTO = reqDTO
    const ret = await $http.run<PracticeRecordBaseReqDTO, Result<Array<PracticeAnswerStateItem>>>(
      path,
      'POST',
      payload,
      axiosConfig
    )
    return ret
  },

  async queryResultAnalysis(
    reqDTO: PracticeResultAnalysisQueryReqDTO,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<PracticeResultAnalysisQueryResDTO>> {
    const path = `${basePath}/api/practice/ks/queryResultAnalysis`
    const payload: PracticeResultAnalysisQueryReqDTO = reqDTO
    const ret = await $http.run<
      PracticeResultAnalysisQueryReqDTO,
      Result<PracticeResultAnalysisQueryResDTO>
    >(path, 'POST', payload, axiosConfig)
    return ret
  }
}

export const quizAnswerProcessController: QuizAnswerProcessController = {
  async deletePractice(
    reqDTO: PracticeRecordBaseReqDTO,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<boolean>> {
    const path = `${basePath}/api/practice/ks/deletePractice`
    const payload: PracticeRecordBaseReqDTO = reqDTO
    const ret = await $http.run<PracticeRecordBaseReqDTO, Result<boolean>>(
      path,
      'POST',
      payload,
      axiosConfig
    )
    return ret
  },

  async startPractice(
    reqDTO: PracticeStartReqDTO,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<PracticeStartResDTO>> {
    const path = `${basePath}/api/practice/ks/startPractice`
    const payload: PracticeStartReqDTO = reqDTO
    const ret = await $http.run<PracticeStartReqDTO, Result<PracticeStartResDTO>>(
      path,
      'POST',
      payload,
      axiosConfig
    )
    return ret
  },

  async submitPractice(
    reqDTO: PracticeSubmitReqDTO,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<boolean>> {
    const path = `${basePath}/api/practice/ks/submitPractice`
    const payload: PracticeSubmitReqDTO = reqDTO
    const ret = await $http.run<PracticeSubmitReqDTO, Result<boolean>>(
      path,
      'POST',
      payload,
      axiosConfig
    )
    return ret
  },

  async submitQuizAnswer(
    reqDTO: QuizSubmitAnswerReqDTO,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<string>> {
    const path = `${basePath}/api/practice/ks/submitQuizAnswer`
    const payload: QuizSubmitAnswerReqDTO = reqDTO
    const ret = await $http.run<QuizSubmitAnswerReqDTO, Result<string>>(
      path,
      'POST',
      payload,
      axiosConfig
    )
    return ret
  }
}

export const answeringQuestionChatRecordController: AnsweringQuestionChatRecordController = {
  async batchInsert(
    dtoList?: Array<AnsweringQuestionChatRecordReqDTO>,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<Record<string, unknown>>> {
    const path = `${basePath}/api/practice/public/vsChat/batchInsert`
    const payload: Array<AnsweringQuestionChatRecordReqDTO> | undefined = dtoList
    const ret = await $http.run<
      Array<AnsweringQuestionChatRecordReqDTO>,
      Result<Record<string, unknown>>
    >(path, 'POST', payload, axiosConfig)
    return ret
  },

  async list(
    dto: AnsweringQuestionChatRecordQueryDTO,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<Array<AnsweringQuestionChatRecordRespDTO>>> {
    const path = `${basePath}/api/practice/public/vsChat/list`
    const payload: AnsweringQuestionChatRecordQueryDTO = dto
    const ret = await $http.run<
      AnsweringQuestionChatRecordQueryDTO,
      Result<Array<AnsweringQuestionChatRecordRespDTO>>
    >(path, 'POST', payload, axiosConfig)
    return ret
  }
}

export const testController: TestController = {
  // async copyOSSPaperImgFile(
  //   path?: string,
  //   axiosConfig?: AxiosRequestConfig
  // ): Promise<Result<string>> {
  //   const path = `${basePath}/api/practice/test/copyOSSPaperImgFile`
  //   const payload: BaseRequestDTO = { path }
  //   const ret = await $http.run<BaseRequestDTO, Result<string>>(path, 'GET', payload, axiosConfig)
  //   return ret
  // },

  async getAiPrompt(type?: number, axiosConfig?: AxiosRequestConfig): Promise<string> {
    const path = `${basePath}/api/practice/test/getAiPrompt`
    const payload: BaseRequestDTO = { type }
    const ret = await $http.run<BaseRequestDTO, string>(path, 'GET', payload, axiosConfig)
    return ret
  },

  async refreshTag(axiosConfig?: AxiosRequestConfig): Promise<void> {
    const path = `${basePath}/api/practice/test/refreshTag`
    const payload = null
    const ret = await $http.run<null, void>(path, 'GET', payload, axiosConfig)
    return ret
  },

  async testAICorrect(
    uid?: string,
    root?: boolean,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<CorrectQuizAIResult>> {
    const path = `${basePath}/api/practice/test/testAICorrect`
    const payload: BaseRequestDTO = {
      uid,
      root
    }
    const ret = await $http.run<BaseRequestDTO, Result<CorrectQuizAIResult>>(
      path,
      'GET',
      payload,
      axiosConfig
    )
    return ret
  },

  async testAIPaper(
    practiceRecordUid?: string,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<PaperAiCallbackDTO>> {
    const path = `${basePath}/api/practice/test/testAIPaper`
    const payload: BaseRequestDTO = { practiceRecordUid }
    const ret = await $http.run<BaseRequestDTO, Result<PaperAiCallbackDTO>>(
      path,
      'GET',
      payload,
      axiosConfig
    )
    return ret
  },

  async testAIStream(axiosConfig?: AxiosRequestConfig): Promise<void> {
    const path = `${basePath}/api/practice/test/testAIStream`
    const payload = null
    const ret = await $http.run<null, void>(path, 'GET', payload, axiosConfig)
    return ret
  },

  async testBuildTag(axiosConfig?: AxiosRequestConfig): Promise<void> {
    const path = `${basePath}/api/practice/test/testBuildTag`
    const payload = null
    const ret = await $http.run<null, void>(path, 'GET', payload, axiosConfig)
    return ret
  }
}

export const userManagerController: UserManagerController = {
  async addUserSign(axiosConfig?: AxiosRequestConfig): Promise<Result<void>> {
    const path = `${basePath}/api/practice/user/addUserSign`
    const payload = null
    const ret = await $http.run<null, Result<void>>(path, 'POST', payload, axiosConfig)
    return ret
  },

  async addUserSubscribe(
    req: UserSubjectTagAddReqDTO,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<void>> {
    const path = `${basePath}/api/practice/user/addUserSubscribe`
    const payload: UserSubjectTagAddReqDTO = req
    const ret = await $http.run<UserSubjectTagAddReqDTO, Result<void>>(
      path,
      'POST',
      payload,
      axiosConfig
    )
    return ret
  },

  async getUserHeaderCount(
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<UserHeaderCountResDTO>> {
    const path = `${basePath}/api/practice/user/getUserHeaderCount`
    const payload = null
    const ret = await $http.run<null, Result<UserHeaderCountResDTO>>(
      path,
      'POST',
      payload,
      axiosConfig
    )
    return ret
  },

  async getUserSubjectInfoList(
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<UserSubjectTagInfoResDTO>> {
    const path = `${basePath}/api/practice/user/getUserSubjectInfoList`
    const payload = null
    const ret = await $http.run<null, Result<UserSubjectTagInfoResDTO>>(
      path,
      'POST',
      payload,
      axiosConfig
    )
    return ret
  },

  async getUserSubjectTagList(
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<UserSubjectTagResDTO>> {
    const path = `${basePath}/api/practice/user/getUserSubjectTagList`
    const payload = null
    const ret = await $http.run<null, Result<UserSubjectTagResDTO>>(
      path,
      'POST',
      payload,
      axiosConfig
    )
    return ret
  },

  async quickUserPracticeRecordUid(axiosConfig?: AxiosRequestConfig): Promise<Result<string>> {
    const path = `${basePath}/api/practice/user/quickUserPracticeRecordUid`
    const payload = null
    const ret = await $http.run<null, Result<string>>(path, 'POST', payload, axiosConfig)
    return ret
  },

  async removeUserSubscribe(
    req: UserSubjectTagRemoveReqDTO,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<void>> {
    const path = `${basePath}/api/practice/user/removeUserSubscribe`
    const payload: UserSubjectTagRemoveReqDTO = req
    const ret = await $http.run<UserSubjectTagRemoveReqDTO, Result<void>>(
      path,
      'POST',
      payload,
      axiosConfig
    )
    return ret
  }
}

export const volQuizManagerController: VolQuizManagerController = {
  async findQuizzes(
    req: QuizQueryReqDTO,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<QuizQueryRespDTO>> {
    const path = `${basePath}/api/practice/vol/findQuizzes`
    const payload: QuizQueryReqDTO = req
    const ret = await $http.run<QuizQueryReqDTO, Result<QuizQueryRespDTO>>(
      path,
      'POST',
      payload,
      axiosConfig
    )
    return ret
  },

  async getExamBoardTagNumber(
    req: ExamBoardTagNumberReqDTO,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<ExamBoardTagNumberResDTO>> {
    const path = `${basePath}/api/practice/vol/getExamBoardTagNumber`
    const payload: ExamBoardTagNumberReqDTO = req
    const ret = await $http.run<ExamBoardTagNumberReqDTO, Result<ExamBoardTagNumberResDTO>>(
      path,
      'POST',
      payload,
      axiosConfig
    )
    return ret
  },

  async getExamList(axiosConfig?: AxiosRequestConfig): Promise<Result<SubjectTagResDTO>> {
    const path = `${basePath}/api/practice/vol/getExamList`
    const payload = null
    const ret = await $http.run<null, Result<SubjectTagResDTO>>(path, 'POST', payload, axiosConfig)
    return ret
  },

  async getExamSubjectTagList(
    req: ExamSubjectTagReqDTO,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<ExamSubjectTagResDTO>> {
    const path = `${basePath}/api/practice/vol/getExamSubjectTagList`
    const payload: ExamSubjectTagReqDTO = req
    const ret = await $http.run<ExamSubjectTagReqDTO, Result<ExamSubjectTagResDTO>>(
      path,
      'POST',
      payload,
      axiosConfig
    )
    return ret
  },

  async getPracticeAnswerDetail(
    req: PagePracticeAnswerDetailReqVO,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<PagePracticeAnswerDetailRespVO>> {
    const path = `${basePath}/api/practice/vol/getPracticeAnswerDetail`
    const payload: PagePracticeAnswerDetailReqVO = req
    const ret = await $http.run<
      PagePracticeAnswerDetailReqVO,
      Result<PagePracticeAnswerDetailRespVO>
    >(path, 'POST', payload, axiosConfig)
    return ret
  },

  async getPracticeList(
    req: PracticeListQueryReqDTO,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<VolListQueryResDTO>> {
    const path = `${basePath}/api/practice/vol/getPracticeList`
    const payload: PracticeListQueryReqDTO = req
    const ret = await $http.run<PracticeListQueryReqDTO, Result<VolListQueryResDTO>>(
      path,
      'POST',
      payload,
      axiosConfig
    )
    return ret
  },

  async getVolAnswerDetailList(
    req: ListVolQuizDetailReqVO,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<ListVolAnswerDetailRespVO>> {
    const path = `${basePath}/api/practice/vol/getVolAnswerDetailList`
    const payload: ListVolQuizDetailReqVO = req
    const ret = await $http.run<ListVolQuizDetailReqVO, Result<ListVolAnswerDetailRespVO>>(
      path,
      'POST',
      payload,
      axiosConfig
    )
    return ret
  },

  async getVolList(
    req: VolListQueryReqDTO,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<VolListQueryResDTO>> {
    const path = `${basePath}/api/practice/vol/getVolList`
    const payload: VolListQueryReqDTO = req
    const ret = await $http.run<VolListQueryReqDTO, Result<VolListQueryResDTO>>(
      path,
      'POST',
      payload,
      axiosConfig
    )
    return ret
  },

  async getVolQuizList(
    req: ListVolQuizzesReqVO,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<ListVolQuizzesRespVO>> {
    const path = `${basePath}/api/practice/vol/getVolQuizList`
    const payload: ListVolQuizzesReqVO = req
    const ret = await $http.run<ListVolQuizzesReqVO, Result<ListVolQuizzesRespVO>>(
      path,
      'POST',
      payload,
      axiosConfig
    )
    return ret
  },

  async getVolTagList(
    req: VolTagReqDTO,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<VolTagResDTO>> {
    const path = `${basePath}/api/practice/vol/getVolTagList`
    const payload: VolTagReqDTO = req
    const ret = await $http.run<VolTagReqDTO, Result<VolTagResDTO>>(
      path,
      'POST',
      payload,
      axiosConfig
    )
    return ret
  }
}

export const wrongBookAnswerController: WrongBookAnswerController = {
  async categoryStats(
    dto: WrongBookStatsReqDTO,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<Array<WrongBookCategoryStatsRespDTO>>> {
    const path = `${basePath}/api/practice/wrongBook/categoryStats`
    const payload: WrongBookStatsReqDTO = dto
    const ret = await $http.run<WrongBookStatsReqDTO, Result<Array<WrongBookCategoryStatsRespDTO>>>(
      path,
      'POST',
      payload,
      axiosConfig
    )
    return ret
  },

  async removeWrongBook(
    reqDTO: WrongBookBaseReq,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<boolean>> {
    const path = `${basePath}/api/practice/wrongBook/removeWrongBook`
    const payload: WrongBookBaseReq = reqDTO
    const ret = await $http.run<WrongBookBaseReq, Result<boolean>>(
      path,
      'POST',
      payload,
      axiosConfig
    )
    return ret
  },

  async subjectStats(
    dto: WrongBookStatsReqDTO,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<WrongBookSubjectStatsRespDTO>> {
    const path = `${basePath}/api/practice/wrongBook/subjectStats`
    const payload: WrongBookStatsReqDTO = dto
    const ret = await $http.run<WrongBookStatsReqDTO, Result<WrongBookSubjectStatsRespDTO>>(
      path,
      'POST',
      payload,
      axiosConfig
    )
    return ret
  },

  async wrongBookSubmitQuizAnswer(
    reqDTO: SubmitWrongBookAnswerReqDTO,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<string>> {
    const path = `${basePath}/api/practice/wrongBook/submitQuizAnswer`
    const payload: SubmitWrongBookAnswerReqDTO = reqDTO
    const ret = await $http.run<SubmitWrongBookAnswerReqDTO, Result<string>>(
      path,
      'POST',
      payload,
      axiosConfig
    )
    return ret
  },

  async wrongBookPage(
    dto: QuizWrongBookPageReqDTO,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<PageResult<TVolQuizDataVO>>> {
    const path = `${basePath}/api/practice/wrongBook/wrongBookPage`
    const payload: QuizWrongBookPageReqDTO = dto
    const ret = await $http.run<QuizWrongBookPageReqDTO, Result<PageResult<TVolQuizDataVO>>>(
      path,
      'POST',
      payload,
      axiosConfig
    )
    return ret
  }
}

export const vXiaoXunController: VXiaoXunController = {
  async xiaoXunAsk(
    req: VXiaoXunAskReq,
    axiosConfig?: AxiosRequestConfig
  ): Promise<Result<VXiaoXunAskResp>> {
    const path = `${basePath}/api/practice/xiaoxun/ask`
    const payload: VXiaoXunAskReq = req
    const ret = await $http.run<VXiaoXunAskReq, Result<VXiaoXunAskResp>>(
      path,
      'POST',
      payload,
      axiosConfig
    )
    return ret
  }
}
export const serviceHandler: ServiceHandler = {
  PracticeResultQueryController: practiceResultQueryController,
  QuizAnswerProcessController: quizAnswerProcessController,
  AnsweringQuestionChatRecordController: answeringQuestionChatRecordController,
  TestController: testController,
  UserManagerController: userManagerController,
  VolQuizManagerController: volQuizManagerController,
  WrongBookAnswerController: wrongBookAnswerController,
  VXiaoXunController: vXiaoXunController
}
