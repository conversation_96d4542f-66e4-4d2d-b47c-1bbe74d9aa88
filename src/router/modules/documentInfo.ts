import type { RouteRecordRaw } from 'vue-router'

export enum documentInfoPageNames {
  documentInfo = 'documentInfo',
  ukConfirmLetterDetail = 'ukConfirmLetterDetail',
  oakConfirmLetterDetail = 'oakConfirmLetterDetail'
}

export default [
  {
    path: '/documentInfo/index',
    name: 'documentInfo',
    meta: {
      title: '论文文书'
    },
    component: () => import('@/views/documentInfo/index.vue')
  },
  {
    path: '/documentInfo/ukConfirmLetterDetail',
    name: 'ukConfirmLetterDetail',
    meta: {
      title: '选校确认函'
    },
    component: () => import('@/views/documentInfo/ukConfirmLetterDetail.vue')
  },
  {
    path: '/documentInfo/oakConfirmLetterDetail',
    name: 'oakConfirmLetterDetail',
    meta: {
      title: '选校确认函'
    },
    component: () => import('@/views/documentInfo/oakConfirmLetterDetail.vue')
  }
] as RouteRecordRaw[]
