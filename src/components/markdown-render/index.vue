<script setup lang="ts">
import { ref, watch } from 'vue'
import MarkdownIt from 'markdown-it'
import MdKatex from '@vscode/markdown-it-katex'
import MdLinkAttributes from 'markdown-it-link-attributes'
import hljs from 'highlight.js'
import 'katex/dist/katex.min.css'
import 'katex/contrib/mhchem'
import mdPlugin from './md-plugin'

interface Props {
  content?: string
  markedOptions?: Record<string, any>
}

const props = withDefaults(defineProps<Props>(), {
  content: '',
  markedOptions: () => ({})
})

// 初始化markdown解析器（单例模式）
const mdi = new MarkdownIt({
  html: false,
  linkify: true,
  breaks: true,
  typographer: true,
  highlight: (str, lang) => {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return `<pre><code class="hljs language-${lang}">${hljs.highlight(str, { language: lang }).value}</code></pre>`
      } catch (e) {
        console.error(e)
      }
    }
    return `<pre><code class="hljs">${hljs.highlightAuto(str).value}</code></pre>`
  }
})
  .use(MdLinkAttributes, { attrs: { target: '_blank', rel: 'noopener' } })
  .use(MdKatex, { strict: false })
  .use(mdPlugin, {
    delimiters: [
      { left: '\\[', right: '\\]', display: true },
      { left: '\\(', right: '\\)', display: false },
      { left: '$', right: '$', display: false },
      { left: '$$', right: '$$', display: true }
    ]
  })

// 响应式渲染内容
const renderedContent = ref('')
watch(
  () => props.content,
  (newContent) => {
    try {
      renderedContent.value = mdi.render(newContent)
    } catch (error) {
      console.log(error)
    }
  },
  { immediate: true }
)
</script>
<template>
  <div class="markdown-container" v-html="renderedContent" v-preview-image></div>
</template>
<style lang="scss" scoped>
:deep(.katex-display) {
  // overflow-x: auto;
  // overflow-y: hidden;
  margin: 1em 0;
  padding: 0.5em 0;
  border-top: 1px solid #eee;
  border-bottom: 1px solid #eee;
}

:deep(.katex) {
  font-size: 1.1em;
  line-height: 1.2;
  * {
    max-width: 100%;
  }
}

:deep(.katex-error) {
  color: #e74c3c;
  background-color: #fdf3f2;
  padding: 0.2em 0.4em;
  border-radius: 4px;
}

// 响应式样式
@media (max-width: 768px) {
  .markdown-container {
    :deep(.katex) {
      font-size: 1em;
    }

    :deep(pre) {
      margin-left: -16px;
      margin-right: -16px;
      border-radius: 0;
    }

    :deep(table) {
      display: block;
      overflow-x: auto;
      -webkit-overflow-scrolling: touch;
    }
  }
}
</style>
