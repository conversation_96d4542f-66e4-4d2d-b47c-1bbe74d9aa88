import { pushPv } from '@/utils/aplus_push'
import { createRouter, createWebHistory, type RouteRecordRaw } from 'vue-router'
const modules: Record<string, any> = import.meta.glob(['./modules/**/*.ts'], {
  eager: true
})

/** 原始静态路由（未做任何处理） */
const moduleRoutes: RouteRecordRaw[] = []

Object.keys(modules).forEach((key) => {
  moduleRoutes.push(...modules[key].default)
})

export const extraRoutes = moduleRoutes

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/practice/home'
    },
    ...moduleRoutes,
    {
      path: '/not-found',
      name: 'NotFound',
      meta: {
        title: 'not-found'
      },
      // route level code-splitting
      component: () => import('@/views/not-found/NotFound.vue')
    },
    {
      path: '/:pathMatch(.*)',
      redirect: '/not-found'
    }
  ],
  scrollBehavior(to, from, savedPosition) {
    // always scroll to top
    return { top: 0 }
  }
})

router.afterEach(() => {
  pushPv()
})

export default router
