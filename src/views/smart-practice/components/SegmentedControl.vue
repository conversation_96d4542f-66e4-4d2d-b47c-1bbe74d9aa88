<template>
  <div class="flex rounded-[6px] overflow-hidden bg-[#E7EBF3] p-[2px]">
    <div
      v-for="(item, index) in options"
      :key="index"
      class="px-[14px] py-[6px] text-[12px]"
      :class="[
        selectedValue === item.value
          ? 'bg-white text-[#0256FF] font-medium rounded-[5px]'
          : 'text-[#3C4258]'
      ]"
      @click="handleSelect(item.value)"
    >
      {{ item.label }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, onMounted, watch } from 'vue'

interface SegmentOption {
  label: string
  value: string
}

const props = defineProps<{
  selectedValue: string
  options: SegmentOption[]
}>()

const emit = defineEmits<{
  (e: 'update:selectedValue', value: string): void
  (e: 'change', value: string): void
}>()

const handleSelect = (value: string) => {
  emit('update:selectedValue', value)
  emit('change', value)
}

// 组件挂载时，如果没有选中值且有选项，则默认选中第一个
onMounted(() => {
  if (!props.selectedValue && props.options.length > 0) {
    handleSelect(props.options[0].value)
  }
})

// 监听options变化，如果options变化且当前没有选中值，则默认选中第一个
watch(
  () => props.options,
  (newOptions) => {
    if (newOptions.length > 0 && !props.selectedValue) {
      handleSelect(newOptions[0].value)
    }
  },
  { deep: true }
)
</script>
