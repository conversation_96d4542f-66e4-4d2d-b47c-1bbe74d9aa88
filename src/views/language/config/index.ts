export const icons = {
  writing:
    'https://va-papers.oss-accelerate.aliyuncs.com/oss-platform/1b4b/c9d4/c3c7/e47495f3-7c3c-4d9c-b4b1-016ccf3b67fa',
  reading:
    'https://va-papers.oss-accelerate.aliyuncs.com/oss-platform/9e98/6bd4/d685/9c826953-586d-4db6-89e9-274da5528587',
  listening:
    'https://va-papers.oss-accelerate.aliyuncs.com/oss-platform/907a/5dd4/5533/3a88c118-3355-4dd5-a709-f2c3210fb805',
  speaking:
    'https://va-papers.oss-accelerate.aliyuncs.com/oss-platform/3379/26f4/d66e/9cb05086-e66d-4f62-9733-8ec60e07f477'
}

export const enum LanguageType {
  'Reading' = 'Reading',
  'Writing' = 'Writing',
  'Listening' = 'Listening',
  'Speaking' = 'Speaking'
}

export const urlMapingWithLanguageType = {
  Reading: 'reading',
  Writing: 'writing',
  Listening: 'listening',
  Speaking: 'speaking'
}

export const languageIcons = [
  {
    subjectCode: 11,
    icon: icons.listening,
    type: LanguageType.Listening,
    eventCode: 'click_listen_Tab'
  },
  { subjectCode: 12, icon: icons.reading, type: LanguageType.Reading, eventCode: 'click_read_Tab' },
  {
    subjectCode: 13,
    icon: icons.speaking,
    type: LanguageType.Speaking,
    eventCode: 'click_speak_Tab'
  },
  { subjectCode: 14, icon: icons.writing, type: LanguageType.Writing, eventCode: 'click_write_Tab' }
]
