<template>
  <div class="wrap flex flex-col items-center text-center pt-[50px]">
    <div>
      <van-empty :image="emptyPic" description="糟糕~! 页面走丢了" />
      <van-button type="primary" @click="handleBack">回到首页</van-button>
      <footer class="pt-[20px]">
        <p class="footer-p text-[15px] text-gray-400">唯寻国际教育</p>
      </footer>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { extraRoutes } from '@/router'

const router = useRouter()
const emptyPic = ref(
  'https://va-papers.oss-accelerate.aliyuncs.com/oss-platform/442b/fdc4/ef42/65b7981f-24fe-4cdf-b244-d06086a5c0e2'
)
const handleBack = () => {
  router.replace({
    path: '/'
  })
}
</script>
<style lang="scss" scoped>
.wrap {
  height: 100%;
  min-height: 100vh;
}
</style>
