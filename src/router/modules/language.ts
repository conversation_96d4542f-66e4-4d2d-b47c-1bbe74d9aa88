import type { RouteRecordRaw } from 'vue-router'

export enum languagePageNames {
  LanguageHome = 'LanguageHome',
  LanguageQuizList = 'LanguageQuizList',
  LanguageDoQuiz = 'LanguageDoQuiz',
  LanguageResult = 'LanguageResult'
}

export default [
  {
    path: '/language/home',
    name: 'LanguageHome',
    meta: {
      title: '语培真题'
    },
    component: () => import('@/views/language/LanguageHome.vue')
  },
  {
    path: '/language/quiz-list',
    name: 'LanguageQuizList',
    meta: {
      title: '语培真题'
    },
    component: () => import('@/views/language/LanguageQuizList.vue')
  },
  {
    path: '/language/doquiz',
    name: 'LanguageDoQuiz',
    meta: {
      title: '语培真题'
    },
    component: () => import('@/views/language/LanguageDoQuiz.vue')
  },
  {
    path: '/language/result',
    name: 'LanguageResult',
    meta: {
      title: '语培真题'
    },
    component: () => import('@/views/language/LanguageResult.vue')
  }
] as RouteRecordRaw[]
