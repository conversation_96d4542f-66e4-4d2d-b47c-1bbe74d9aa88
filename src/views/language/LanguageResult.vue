<script setup lang="ts">
import { onBeforeMount, onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import ResultDetail from './sub-components/ResultDetail.vue'
import OriginContent from './sub-components/OriginContent.vue'
import { useLoading, useGlobal } from '@/hooks'
import type {
  SectionAnswerDetailsReqVO,
  SectionAnswerDetailsRespVO
} from 'va.study.service.homework'
import { LanguageType } from './config'
const tabs = [
  { label: '结果', value: 'ResultDetail', comp: ResultDetail },
  { label: '原文', value: 'OriginContent', comp: OriginContent }
]
const activeTab = ref('ResultDetail')
const resultInfo = ref<Partial<SectionAnswerDetailsRespVO & { attachments: { url: string[] }[] }>>(
  {}
)
const { loading, startLoading, endLoading } = useLoading()
const { globalProperties } = useGlobal()
const routes = useRoute()

onBeforeMount(() => {
  if (routes.query?.title) {
    document.title = routes.query?.title as string
  }
})

onMounted(() => {
  getResultInfo()
})

const getResultInfo = async () => {
  startLoading()
  try {
    const params: SectionAnswerDetailsReqVO = {
      sectionUid: routes.query.sectionUid as string
    }
    const res = await globalProperties.$api.LanguageTest.sectionAnswerDetails(params)
    resultInfo.value = res || {}
  } catch (e) {
    showToast({
      message: '网络繁忙',
      duration: 1000
    })
  } finally {
    endLoading()
  }
}
</script>
<template>
  <div class="language-result-wrap pb-[150px]">
    <van-tabs v-model:active="activeTab" class="custom-tab" sticky>
      <van-tab :title="item.label" :name="item.value" v-for="item in tabs" :key="item.value">
        <ResultDetail
          v-if="item.value === 'ResultDetail'"
          :courseName="routes.query.title as string"
          :quizs="resultInfo.children || []"
          :sec="resultInfo.timeConsume || 0"
          :right-qty="resultInfo.correctAnswerCount || 0"
          :total-qty="resultInfo.totalQuestionCount || 0"
          :accuracy-rate="resultInfo.accuracyRate || 0"
        />
        <OriginContent v-if="item.value === 'OriginContent'" :article="resultInfo.content" />
      </van-tab>
    </van-tabs>
    <AudioPlayer
      v-if="
        LanguageType.Listening === routes.query.languageType && resultInfo?.attachments?.[0]?.url
      "
      :url="resultInfo?.attachments?.[0]?.url as unknown as string"
      class="fixed left-0 bottom-0 z-10"
      :showSubmitBtn="false"
    />
  </div>
</template>

<style lang="scss" scoped>
.custom-tab {
  :deep(.van-tabs__line) {
    width: 16px;
    height: 4px;
    background-color: #6481ee;
  }
}
</style>
