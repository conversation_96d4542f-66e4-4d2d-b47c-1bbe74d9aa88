<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
export interface QuizProps {
  title: string
  tag?: string
  info: {
    content: string
    answers: any[]
    options: any[]
    studentAnswerList: any[]
  }
}
const props = withDefaults(defineProps<QuizProps>(), {
  tag: '单选题'
})

const renderIconClass = (aoVal: string, answers: any[], studentAnswerList: string[]) => {
  if (answers?.some((v) => v?.content === aoVal)) {
    return 'bg-[#41B981] text-[#FFF]'
  }
  if (studentAnswerList?.includes(aoVal) && !answers?.some((v) => v?.content === aoVal)) {
    return 'bg-[#EF6A6D] text-[#FFF]'
  }
  return 'bg-[#eee] text-[#646B77]'
}
</script>
<template>
  <div class="blank-filling-wrap bg-white px-[15px] pt-[15px] pb-[20px] box-border">
    <div class="title flex items-center mb-[15px]">
      <span class="label text-text-grey text-[16px] font-semibold mr-[14px]">{{
        props.title
      }}</span>
      <van-tag
        color="rgba(100, 129, 238, 0.22)"
        text-color="#6481EE"
        class="text-[12px] leading-[12px] rounded-[4px] py-[2px]"
        >{{ props.tag }}</van-tag
      >
    </div>
    <div v-html="props.info.content" class="text-[14px] mb-[15px]"></div>
    <div
      v-for="(item, index) in props.info.options"
      :key="index"
      class="mb-[10px] last:mb-0 flex items-center"
    >
      <span
        class="inline-block h-[20px] w-[20px] leading-[20px] rounded-[50%] text-center text-[14px] mr-[10px]"
        :class="[renderIconClass(item.aoVal, props.info.answers, props.info.studentAnswerList)]"
        >{{ item.aoVal }}</span
      >
      <div v-html="item.content" class="text-[14px] mr-[20px] flex-1"></div>
      <template v-if="props.info?.studentAnswerList?.includes(item.aoVal)">
        <span
          class="iconfont text-[16px]"
          :class="
            props.info.answers?.some((v) => v.content === item.aoVal)
              ? 'text-[#41B981] iconduigou'
              : 'iconcuowu text-[#EF6A6D]'
          "
        ></span>
      </template>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>
