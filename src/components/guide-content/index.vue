<script setup lang="ts">
import VaBaseButton from '@/components/base-ui/VaBaseButton.vue'
import { getImageUrl } from '@/utils/img'
defineEmits(['close'])
</script>

<template>
  <div class="guide-content">
    <span>点击后上下拖动, 可以拉伸答题区域</span>
    <div class="guide-content__bottom relative flex justify-end w-full mt-[30px]">
      <img
        :src="getImageUrl('practice/guide-do-practice.png')"
        alt="guide"
        class="guide-content__image absolute bottom-[-50px] left-[20px]"
      />
      <VaBaseButton class="got-btn" text="知道了" @click="$emit('close')" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.guide-content {
  display: flex;
  flex-direction: column;

  &__image {
    display: inline-block;
    width: 84px;
    height: 89px;
  }

  .got-btn {
    padding: 4px 24px;
    font-size: 14px;
    border-radius: 20px;
  }
}
</style>
