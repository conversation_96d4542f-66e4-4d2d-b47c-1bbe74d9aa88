<script setup lang="ts">
import { ref, watch } from 'vue'
import { getImageUrl } from '@/utils/img'
import { useRouter, useRoute } from 'vue-router'
import { useGlobal, useLoading } from '@/hooks'
const { globalProperties } = useGlobal()
const { startLoading, endLoading } = useLoading()
import { Step as VanStep, Steps as VanSteps } from 'vant'
import { documentInfoPageNames } from '@/router/modules/documentInfo'
import type { DocumentTutoringProgressRespDTO } from 'va.biz.service.collegePlan'
const jinduIcon = getImageUrl('document-info/jindu.png')
const emailIcon = getImageUrl('document-info/email.png')
const timeIcon = getImageUrl('document-info/time.png')
const empty = getImageUrl('document-info/empty.png')
const isshow = ref(true)
const router = useRouter()
const routes = useRoute()

const confirmationList: any = ref([])
const ProgressList = ref<DocumentTutoringProgressRespDTO[]>([])

const viewto = (ishow: boolean) => {
  isshow.value = ishow
}

const goto = (item: any) => {
  router.push({
    name:
      item.departmentValue === 1
        ? documentInfoPageNames.ukConfirmLetterDetail
        : documentInfoPageNames.oakConfirmLetterDetail,
    query: { uid: item.uid }
  })
}
// 确认函
const getconfirmationList = async () => {
  try {
    startLoading()
    const res = await globalProperties.$api.SelectionConfirmLetter.confirmationList({
      studentCode: routes.query.studentCode as string
    })
    confirmationList.value = res
  } finally {
    endLoading()
  }
}
// 文书进度
const getDocumentTutoringProgress = async () => {
  try {
    startLoading()
    const res = await globalProperties.$api.DocumentTutoring.getDocumentTutoringProgress({
      studentCode: routes.query.studentCode as string
    })
    const arr = []
    ProgressList.value = res
  } finally {
    endLoading()
  }
}

const getdeta = (dateStr: any) => {
  if (!dateStr) {
    return
  }
  return dateStr.split(' ')[0]
}

watch(
  () => isshow.value,
  (newVal: boolean) => {
    if (newVal) {
      getDocumentTutoringProgress()
    } else {
      getconfirmationList()
    }
  },
  { immediate: true }
)
</script>
<template>
  <div class="mian">
    <div class="documentinfo">
      <div class="box mb-[14px]">
        <div class="box-left" @click="viewto(true)">
          <img :src="jinduIcon" alt="icon" class="h-[36px] w-[36px] mb-[10px]" />
          <span>写作进度</span>
        </div>
        <div class="box-right" @click="viewto(false)">
          <img :src="emailIcon" alt="icon" class="h-[36px] w-[36px] mb-[10px]" />
          <span>确认函</span>
        </div>
      </div>
      <div class="box1" v-if="isshow">
        <template v-if="ProgressList.length">
          <div v-for="(items, indexs) in ProgressList" :key="indexs">
            <div class="progresstitle"><span class="inlie"></span>{{ items.documentName }}</div>
            <div class="progress">
              <van-steps direction="vertical" class="vansteps">
                <van-step class="jilubox">
                  <div class="jilutitle">
                    <span>确认确认函</span>
                    <span>{{ getdeta(items.initialCreateDate) }}</span>
                  </div>
                  <p>更新签署日期：{{ items.updatedSigningListDate }}</p>
                  <p>首次签署日期：{{ items.initialSigningDate }}</p>
                </van-step>
                <van-step
                  class="jilubox"
                  v-for="(item, index) in items.tutoringProgressList"
                  :key="index"
                >
                  <div class="jilutitle">
                    <span>{{ item.stageName }}</span>
                    <span>{{ getdeta(item.completionTime) }}</span>
                  </div>
                  <p :class="{ status: true, 'is-sign': item.progressStatus === 3 }">
                    {{ item.progressStatusTitle }}
                  </p>
                  <p>论文文书顾问：{{ item.docTeacherName || '--' }}</p>
                  <p>学术导师：{{ item.prTeacherName || '--' }}</p>
                </van-step>
              </van-steps>
            </div>
          </div>
        </template>
        <div v-else class="empty-content">
          <img :src="empty" />
          <p>暂无数据~</p>
        </div>
      </div>
      <div class="box2" v-else>
        <template v-if="confirmationList.length">
          <div
            class="box2-item"
            @click="goto(item)"
            v-for="(item, index) in confirmationList"
            :key="index"
          >
            <div>
              <p>
                <img :src="timeIcon" class="h-[15px] w-[15px] ml-[16px] mr-[10px]" />签署时间：{{
                  item.confirmTime || '--'
                }}
              </p>
              <p>
                <img :src="timeIcon" class="h-[15px] w-[15px] ml-[16px] mr-[10px]" />生成时间：{{
                  item.createTime || '--'
                }}
              </p>
            </div>
            <div :class="{ status: true, 'is-sign': item.confirmStatus }">
              {{ item.confirmStatusTitle }}
            </div>
          </div>
        </template>
        <div v-else class="empty-content">
          <img :src="empty" />
          <p>暂无数据~</p>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.mian {
  width: 100%;
  height: 100%;
  background: #f2f3f5;
}
.documentinfo {
  width: 100%;
  height: 100%;
  background: url('@/assets/images/document-info/bg.png') 0 0 no-repeat;
  background-size: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  .box {
    width: 90%;
    height: 95px;
    margin-top: 20px;
    background: #fff;
    border-radius: 10px;
    display: flex;
    justify-content: space-around;
    .box-left,
    .box-right {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      span {
        font-weight: 400;
        font-size: 14px;
        color: #666666;
        line-height: 14px;
        text-align: left;
        font-style: normal;
      }
    }
  }
  .box1 {
    width: 90%;
    flex: 1;
    overflow: auto;
    padding-bottom: 20px;
    .progresstitle {
      font-weight: 600;
      font-size: 15px;
      color: #3f454f;
      line-height: 21px;
      text-align: left;
      font-style: normal;
      margin: 15px 0 10px 0;
      .inlie {
        width: 4px;
        height: 16px;
        background: linear-gradient(180deg, #65b7ff 0%, #3581ff 100%);
        border-radius: 3px;
        display: inline-block;
        margin-right: 10px;
      }
    }
    .progress {
      width: 100%;
      background: #ffffff;
      border-radius: 8px;
      min-height: 200px;
      display: flex;
    }
  }
  .box2 {
    width: 90%;
    flex: 1;
    overflow: auto;
    padding-bottom: 20px;
    .box2-item {
      width: 100%;
      background: #ffffff;
      border-radius: 8px;
      margin-bottom: 14px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      font-size: 14px;
      padding: 15px 0 15px 0;
      box-sizing: border-box;
      p {
        line-height: 35px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        img {
          display: inline-block;
        }
      }
    }
  }
}

:deep(.van-step__icon--active),
:deep(.van-step__circle) {
  width: 12px;
  height: 12px;
  background: linear-gradient(180deg, #998cff 0%, #6481ee 100%) !important;
  border-radius: 7px;
  border: 2px solid #cad5ff;
  box-shadow: 0 0 0 1px #d8d8d8;
}
:deep(.van-icon-checked:before) {
  content: '';
}
:deep(.van-step__line) {
  background-image: linear-gradient(
    to bottom,
    rgba(216, 216, 216, 0.6) 50%,
    rgba(255, 255, 255, 0.8) 0
  );
  background-position: left;
  background-size: 1px 5px;
  background-repeat: repeat-y;
}
:deep(.van-hairline:after) {
  border: none !important;
}
.jilutitle {
  font-weight: 600;
  font-size: 15px;
  color: #3f454f;
  line-height: 30px;
  text-align: left;
  font-style: normal;
  width: 100%;
  display: flex;
  justify-content: space-between;
}
.status {
  font-weight: 600;
  font-size: 14px;
  color: #ff9e6d;
  line-height: 14px;
  text-align: right;
  font-style: normal;
  margin-right: 15px;
  &.is-sign {
    color: #10c156;
  }
}
.jilubox p {
  font-weight: 400;
  font-size: 14px;
  color: #666666;
  line-height: 30px;
  text-align: left;
  font-style: normal;
}
.vansteps {
  width: 100%;
  border-radius: 8px;
}
.empty-content {
  width: 100%;
  padding-top: 200px;
  display: flex;
  align-items: center;
  flex-direction: column;
  img {
    width: 154px;
    height: 120px;
  }
  p {
    font-size: 18px;
    line-height: 40px;
    color: #999;
  }
}
</style>
