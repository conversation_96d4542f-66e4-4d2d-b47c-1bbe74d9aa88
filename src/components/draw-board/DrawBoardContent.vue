<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, watch, nextTick } from 'vue'
import Konva from 'konva'
import html2canvas from 'html2canvas'
import type { ToolType, ToolConfig } from './types'

defineOptions({
  name: 'DrawBoardContent'
})

// 定义事件
const emit = defineEmits<{
  touchModeChange: [mode: 'auto' | 'none']
}>()

interface Props {
  questionHeight?: number
  initialAnswerHeight?: number
  handleWidth?: number
  questionWrapRef?: HTMLElement | null
  currentTool?: ToolType
  toolConfigs?: Record<ToolType, ToolConfig>
  minAnswerHeight?: number
  gridSpacing?: number
  globalTouchMode?: 'auto' | 'none'
}

const props = withDefaults(defineProps<Props>(), {
  questionHeight: 300,
  initialAnswerHeight: 200,
  handleWidth: 40,
  questionWrapRef: null,
  currentTool: 'brush',
  minAnswerHeight: 100,
  gridSpacing: 40,
  globalTouchMode: 'auto',
  toolConfigs: () => ({
    brush: { brushSize: 5, color: '#000000' },
    eraser: { brushSize: 10, color: '#ffffff' },
    circle: { brushSize: 2, color: '#000000' },
    rectangle: { brushSize: 2, color: '#000000' },
    'background-line': { brushSize: 1, color: '#cccccc' },
    grid: { brushSize: 1, color: '#666' },
    'history-back': { brushSize: 1, color: '#000000' },
    'history-forward': { brushSize: 1, color: '#000000' },
    clear: { brushSize: 1, color: '#000000' },
    export: { brushSize: 1, color: '#000000' },
    text: { brushSize: 16, color: '#000000' }
  })
})

// 使用传入的工具状态，而不是内部hooks
const currentTool = computed(() => props.currentTool)
const toolConfigs = computed(() => props.toolConfigs)

// DOM引用
const containerRef = ref<HTMLElement>()
const canvasContainerRef = ref<HTMLElement>()
const resizeHandleRef = ref<HTMLElement>()

// Konva相关
const stage = ref<Konva.Stage>()
const layer = ref<Konva.Layer>()
const drawingLayer = ref<Konva.Layer>()
const backgroundLayer = ref<Konva.Layer>()
const transformer = ref<Konva.Transformer>()

// 画板状态
const canvasWidth = ref(800)
const canvasHeight = ref(600)
const answerHeight = ref(Math.max(props.initialAnswerHeight, props.minAnswerHeight)) // 确保初始高度不小于最小值
const realQuestionHeight = ref(props.questionHeight) // 真实题目高度
const isDrawing = ref(false)
const currentPath = ref<Konva.Line | null>(null)
const isPaint = ref(false)

// 形状绘制相关
const startPoint = ref<{ x: number; y: number } | null>(null)
const tempShape = ref<Konva.Shape | null>(null)
type TextInputElement = HTMLTextAreaElement & { _cleanup?: () => void }
const textInput = ref<TextInputElement | null>(null)
const editingTextNode = ref<Konva.Text | null>(null)
const styleTag = ref<HTMLStyleElement | null>(null)

// 文本编辑按钮相关
const textEditButtons = ref<HTMLElement | null>(null)
const selectedTextNode = ref<Konva.Text | null>(null)

// Touch 模式状态 - 响应全局状态
const touchMode = ref<'auto' | 'none'>(props.globalTouchMode)
const touchModeHandler = ref<((e: PointerEvent) => void) | null>(null)

// 历史记录
const history = ref<string[]>([])
const historyStep = ref(-1)

// 触摸和手势相关
const isDragging = ref(false)
const dragStartY = ref(0)
const startAnswerHeight = ref(0)

// 橡皮擦相关
const eraserPath = ref<{ x: number; y: number }[]>([])

// 计算总高度
const totalHeight = computed(() => realQuestionHeight.value + answerHeight.value)
// 计算画板实际绘制宽度（减去右侧手柄宽度）
const drawingWidth = computed(() => canvasWidth.value)
// 计算实际作答区域高度
const actualAnswerHeight = computed(() => totalHeight.value - realQuestionHeight.value)

// 背景图案状态
const currentBackgroundType = ref<'none' | 'line' | 'grid'>('none')

// 计算真实题目高度
const calculateQuestionHeight = () => {
  const questionElement = props.questionWrapRef
  if (questionElement) {
    // 先重置高度为auto，获取真实的内容高度
    questionElement.style.height = 'auto'
    const rect = questionElement.getBoundingClientRect()
    realQuestionHeight.value = rect.height

    // 然后设置question-content-wrap高度等于画板总高度
    questionElement.style.height = totalHeight.value + 'px'
  }
}

// 创建文本编辑按钮
const createTextEditButtons = (textNode: Konva.Text) => {
  if (textEditButtons.value) {
    containerRef.value?.removeChild(textEditButtons.value)
    textEditButtons.value = null
  }

  const buttonsContainer = document.createElement('div')
  buttonsContainer.className = 'text-edit-buttons'

  // 更新按钮位置的函数
  const updateButtonPosition = () => {
    const textPosition = textNode.absolutePosition()
    const nodeWidth = textNode.width() * textNode.scaleX()
    const nodeHeight = textNode.height() * textNode.scaleY()

    // 计算文本节点的水平中心点
    const textCenterX = textPosition.x + nodeWidth / 2

    // 容器估算宽度：两个按钮(32px each) + gap(6px) + padding左右(16px) = 86px
    const estimatedContainerWidth = 86

    // 居中计算：容器左边缘 = 文本中心点 - 容器宽度的一半
    const leftPosition = textCenterX - estimatedContainerWidth / 2

    buttonsContainer.style.left = `${leftPosition}px`
    buttonsContainer.style.top = `${textPosition.y - 50}px`

    console.log('按钮位置调试:', { textCenterX, leftPosition, estimatedContainerWidth })
  }

  // 初始设置样式
  Object.assign(buttonsContainer.style, {
    position: 'absolute',
    zIndex: '10002',
    display: 'flex',
    flexDirection: 'row', // 水平排列
    gap: '6px',
    padding: '8px',
    backgroundColor: '#ffffff', // 白色背景
    borderRadius: '8px',
    border: '1px solid #e5e5e5', // 灰色边框
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)', // 淡化阴影
    transition: 'none' // 去除动画效果
  })

  // 设置初始位置
  updateButtonPosition()

  // 编辑按钮
  const editBtn = document.createElement('button')
  editBtn.innerHTML = `
    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
      <path d="m18.5 2.5 a2.1 2.1 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
    </svg>
  `
  editBtn.title = '编辑文本 (双击)'
  Object.assign(editBtn.style, {
    width: '32px',
    height: '32px',
    border: 'none',
    borderRadius: '6px',
    backgroundColor: 'transparent',
    color: '#666666', // 深灰色图标
    cursor: 'pointer',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    transition: 'all 0.2s ease',
    outline: 'none',
    userSelect: 'none'
  })

  editBtn.addEventListener('mouseenter', () => {
    editBtn.style.backgroundColor = '#f0f0f0'
    editBtn.style.color = '#333333'
  })

  editBtn.addEventListener('mouseleave', () => {
    editBtn.style.backgroundColor = 'transparent'
    editBtn.style.color = '#666666'
  })

  editBtn.addEventListener('click', (e) => {
    e.stopPropagation()
    editText(textNode)
    hideTextEditButtons()
  })

  // 删除按钮
  const deleteBtn = document.createElement('button')
  deleteBtn.innerHTML = `
    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <polyline points="3,6 5,6 21,6"></polyline>
      <path d="m19,6v14a2,2,0,0,1-2,2H7a2,2,0,0,1-2-2V6m3,0V4a2,2,0,0,1,2-2h4a2,2,0,0,1,2,2V6"></path>
      <line x1="10" y1="11" x2="10" y2="17"></line>
      <line x1="14" y1="11" x2="14" y2="17"></line>
    </svg>
  `
  deleteBtn.title = '删除文本 (Delete键)'
  Object.assign(deleteBtn.style, {
    width: '32px',
    height: '32px',
    border: 'none',
    borderRadius: '6px',
    backgroundColor: 'transparent',
    color: '#666666', // 深灰色图标
    cursor: 'pointer',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    transition: 'all 0.2s ease',
    outline: 'none',
    userSelect: 'none'
  })

  deleteBtn.addEventListener('mouseenter', () => {
    deleteBtn.style.backgroundColor = '#fee2e2' // 淡红色背景
    deleteBtn.style.color = '#dc2626' // 红色图标
  })

  deleteBtn.addEventListener('mouseleave', () => {
    deleteBtn.style.backgroundColor = 'transparent'
    deleteBtn.style.color = '#666666'
  })

  deleteBtn.addEventListener('click', (e) => {
    e.stopPropagation()
    deleteTextNode(textNode)
    hideTextEditButtons()
  })

  buttonsContainer.appendChild(editBtn)
  buttonsContainer.appendChild(deleteBtn)

  containerRef.value?.appendChild(buttonsContainer)
  textEditButtons.value = buttonsContainer
  selectedTextNode.value = textNode

  // 拖拽开始：保持按钮显示
  const handleDragStart = () => {
    if (textEditButtons.value && selectedTextNode.value === textNode) {
      textEditButtons.value.style.display = 'flex'
    }
  }

  // 拖拽结束：显示按钮并更新位置
  const handleDragEnd = () => {
    if (textEditButtons.value && selectedTextNode.value === textNode) {
      updateButtonPosition()
      textEditButtons.value.style.display = 'flex'
    }
  }

  // 变换事件：更新按钮位置
  const handleTransform = () => {
    if (textEditButtons.value && selectedTextNode.value === textNode) {
      updateButtonPosition()
    }
  }

  const handleDragMove = () => {
    console.log('move')
    updateButtonPosition()
  }

  textNode.on('dragstart', handleDragStart)
  textNode.on('dragmove', handleDragMove)
  textNode.on('dragend', handleDragEnd)
  textNode.on('transform', handleTransform)

  // 存储清理函数
  buttonsContainer.dataset.cleanup = 'true'
  ;(buttonsContainer as any)._cleanup = () => {
    textNode.off('dragstart', handleDragStart)
    textNode.off('dragmove', handleDragMove)
    textNode.off('dragend', handleDragEnd)
    textNode.off('transform', handleTransform)
  }
}

// 隐藏文本编辑按钮
const hideTextEditButtons = () => {
  if (textEditButtons.value) {
    // 执行清理函数
    if ((textEditButtons.value as any)._cleanup) {
      ;(textEditButtons.value as any)._cleanup()
    }
    containerRef.value?.removeChild(textEditButtons.value)
    textEditButtons.value = null
  }
  selectedTextNode.value = null
  transformer.value?.nodes([])
}

// 删除文本节点
const deleteTextNode = (textNode: Konva.Text) => {
  textNode.destroy()
  drawingLayer.value?.batchDraw()
  saveState()
}

// 检查橡皮擦是否与文本节点相交
const checkEraserCollision = (eraserPoints: { x: number; y: number }[], brushSize: number) => {
  if (!drawingLayer.value || eraserPoints.length === 0) return

  const textNodes = drawingLayer.value.find('.text-node') as Konva.Text[]
  const eraserRadius = brushSize / 2

  textNodes.forEach((textNode) => {
    const textRect = textNode.getClientRect()

    // 检查橡皮擦路径是否与文本框相交
    const isColliding = eraserPoints.some((point) => {
      // 检查点是否在文本框内或附近
      return (
        point.x >= textRect.x - eraserRadius &&
        point.x <= textRect.x + textRect.width + eraserRadius &&
        point.y >= textRect.y - eraserRadius &&
        point.y <= textRect.y + textRect.height + eraserRadius
      )
    })

    if (isColliding) {
      // 如果当前选中的文本节点被擦除，隐藏编辑按钮
      if (selectedTextNode.value === textNode) {
        hideTextEditButtons()
      }
      textNode.destroy()
    }
  })
}

// 处理设备旋转和窗口大小变化
const handleResize = async () => {
  if (!stage.value || !canvasContainerRef.value) return

  await nextTick()

  // 重新计算容器尺寸
  const container = canvasContainerRef.value
  const rect = container.getBoundingClientRect()

  const newWidth = rect.width
  const newHeight = totalHeight.value

  // 只有当尺寸真正发生变化时才更新
  if (newWidth !== canvasWidth.value || newHeight !== canvasHeight.value) {
    canvasWidth.value = newWidth
    canvasHeight.value = newHeight

    // 更新Konva Stage尺寸
    stage.value.width(canvasWidth.value)
    stage.value.height(canvasHeight.value)

    // 更新背景图层的尺寸
    updateBackgroundAreas()

    // 重新绘制所有图层
    backgroundLayer.value?.batchDraw()
    layer.value?.batchDraw()
    drawingLayer.value?.batchDraw()

    console.log('画板尺寸已更新:', {
      width: canvasWidth.value,
      height: canvasHeight.value,
      questionHeight: realQuestionHeight.value,
      answerHeight: actualAnswerHeight.value
    })
  }

  forceEnableTouchScrolling()
}

// 初始化画板
const initKonva = async () => {
  await nextTick()

  if (!canvasContainerRef.value) return

  const container = canvasContainerRef.value
  const rect = container.getBoundingClientRect()

  // 画板宽度 = 容器宽度，容器已经通过CSS设置为 calc(100% - 手柄宽度)
  canvasWidth.value = rect.width
  canvasHeight.value = totalHeight.value

  // 设置question-content-wrap高度等于画板总高度
  const questionWrapElement = props.questionWrapRef
  if (questionWrapElement) {
    questionWrapElement.style.height = totalHeight.value + 'px'
  }

  // 创建舞台，使用容器的实际宽度
  stage.value = new Konva.Stage({
    container: container as any,
    width: canvasWidth.value,
    height: canvasHeight.value,
    draggable: false
  })

  // 创建图层
  backgroundLayer.value = new Konva.Layer()
  layer.value = new Konva.Layer()
  drawingLayer.value = new Konva.Layer()

  stage.value.add(backgroundLayer.value)
  stage.value.add(layer.value)
  stage.value.add(drawingLayer.value)

  transformer.value = new Konva.Transformer({
    nodes: [],
    keepRatio: false,
    enabledAnchors: [
      'top-left',
      'top-right',
      'bottom-left',
      'bottom-right',
      'middle-left',
      'middle-right'
    ],
    rotateEnabled: false
  })
  drawingLayer.value.add(transformer.value)

  // 添加背景 - 题目区域完全透明，作答区域白色
  const questionBackground = new Konva.Rect({
    x: 0,
    y: 0,
    width: canvasWidth.value,
    height: realQuestionHeight.value,
    fill: 'transparent', // 题目区域完全透明
    listening: false
  })
  backgroundLayer.value.add(questionBackground)

  // 确保作答区域有明显的白色背景
  const answerBackground = new Konva.Rect({
    x: 0,
    y: realQuestionHeight.value,
    width: canvasWidth.value,
    height: Math.max(actualAnswerHeight.value, 10), // 确保至少有10px高度
    fill: '#ffffff', // 使用明确的白色
    listening: false
  })
  backgroundLayer.value.add(answerBackground)

  // 立即渲染背景层
  backgroundLayer.value.draw()

  // 强制重绘所有图层确保显示
  backgroundLayer.value.batchDraw()
  layer.value.batchDraw()
  drawingLayer.value.batchDraw()

  // 绑定事件
  bindDrawingEvents()

  // 监听不同类型的输入设备，动态切换触摸模式
  touchModeHandler.value = (e: PointerEvent) => {
    if (e.pointerType === 'touch') {
      // 手指触摸 - 允许滚动
      if (touchMode.value !== 'auto') {
        touchMode.value = 'auto'
      }
    } else if (e.pointerType === 'pen') {
      // 手写笔 - 禁止滚动，允许绘画
      if (touchMode.value !== 'none') {
        touchMode.value = 'none'
      }
    }
  }
  container.addEventListener('pointerdown', touchModeHandler.value)

  // Debug: Check the actual DOM structure and styles
  console.log('Canvas container:', container)
  console.log('Container computed styles:', window.getComputedStyle(container))

  // 保存初始状态
  saveState()

  // 点击事件处理，用于选择文本
  stage.value.on('click tap', (e) => {
    // If we clicked on an empty area, deselect all
    if (e.target === stage.value) {
      transformer.value?.nodes([])
      hideTextEditButtons()
      return
    }

    // If we clicked on a text node, select it and show edit buttons
    if (e.target.hasName('text-node')) {
      const textNode = e.target as Konva.Text
      transformer.value?.nodes([textNode])
      createTextEditButtons(textNode)
    } else {
      // if we clicked on something else, deselect
      transformer.value?.nodes([])
      hideTextEditButtons()
    }
  })
}

// 绑定绘画事件
const bindDrawingEvents = () => {
  if (!stage.value || !drawingLayer.value) return

  // This listener now only needs to handle pen and mouse.
  // Touch events are handled by turning listening on/off.
  stage.value.on('pointerdown', (e) => {
    const evt = e.evt as PointerEvent

    if (evt.pointerType === 'pen') {
      handleDrawStart(e)
      return
    }

    if (evt.pointerType === 'mouse' && currentTool.value === 'text') {
      handleDrawStart(e)
    }
  })

  stage.value.on('pointermove', (e) => {
    const evt = e.evt as PointerEvent
    if (evt.pointerType === 'pen') {
      handleDrawMove(e)
    }
  })

  stage.value.on('pointerup', (e) => {
    const evt = e.evt as PointerEvent
    if (evt.pointerType === 'pen') {
      handleDrawEnd()
    }
  })
}

// 开始绘画
const handleDrawStart = (e?: Konva.KonvaEventObject<MouseEvent | PointerEvent>) => {
  if (currentTool.value === 'text') {
    // If the click is on an existing text node, do nothing here.
    // The 'click/tap' and 'dblclick' handlers will manage selection and editing.
    if (e?.target.hasName('text-node')) {
      return
    }

    // If there's already an active textarea from creating new text, finalize it.
    if (textInput.value) {
      finalizeText()
      return
    }
    // 阻止事件继续传播，防止立即触发blur
    e?.evt.preventDefault()

    const pos = stage.value?.getPointerPosition()
    if (pos) {
      createTextarea(pos)
    }
    return
  }
  // 调试信息
  if (e?.evt instanceof PointerEvent) {
    console.log('手写笔事件触发:', e.evt.pointerType, '压力:', e.evt.pressure)
  } else if (e?.evt instanceof MouseEvent) {
    console.log('鼠标事件触发')
  }

  if (
    currentTool.value === 'export' ||
    currentTool.value === 'clear' ||
    currentTool.value === 'history-back' ||
    currentTool.value === 'history-forward' ||
    currentTool.value === 'background-line' ||
    currentTool.value === 'grid'
  ) {
    // 背景工具不在这里处理，由工具栏的handleToolbarAction处理
    return
  }

  // 隐藏文本编辑按钮
  hideTextEditButtons()

  isDrawing.value = true
  isPaint.value = true

  const pos = stage.value!.getPointerPosition()
  if (!pos) return

  const config = toolConfigs.value[currentTool.value]

  if (currentTool.value === 'brush' || currentTool.value === 'eraser') {
    // 初始化橡皮擦路径
    if (currentTool.value === 'eraser') {
      eraserPath.value = [{ x: pos.x, y: pos.y }]
    }

    currentPath.value = new Konva.Line({
      stroke: currentTool.value === 'eraser' ? '#ffffff' : config.color,
      strokeWidth: config.brushSize,
      globalCompositeOperation: currentTool.value === 'eraser' ? 'destination-out' : 'source-over',
      lineCap: 'round',
      lineJoin: 'round',
      points: [pos.x, pos.y, pos.x, pos.y]
    })
    drawingLayer.value!.add(currentPath.value as any)
  } else if (currentTool.value === 'circle' || currentTool.value === 'rectangle') {
    // 记录起始点
    startPoint.value = { x: pos.x, y: pos.y }
  }
}

// 绘画移动
const handleDrawMove = (e: Konva.KonvaEventObject<MouseEvent | PointerEvent>) => {
  if (!isPaint.value || !isDrawing.value) return

  e.evt.preventDefault()

  const pos = stage.value!.getPointerPosition()
  if (!pos) return

  if (currentTool.value === 'brush' || currentTool.value === 'eraser') {
    if (currentPath.value) {
      const newPoints = currentPath.value.points().concat([pos.x, pos.y])
      currentPath.value.points(newPoints)

      // 如果是橡皮擦，记录路径用于碰撞检测
      if (currentTool.value === 'eraser') {
        eraserPath.value.push({ x: pos.x, y: pos.y })
      }
    }
  } else if (
    (currentTool.value === 'circle' || currentTool.value === 'rectangle') &&
    startPoint.value
  ) {
    // 清除之前的临时形状
    if (tempShape.value) {
      tempShape.value.destroy()
    }

    const config = toolConfigs.value[currentTool.value]
    const startX = startPoint.value.x
    const startY = startPoint.value.y

    if (currentTool.value === 'circle') {
      const radius = Math.sqrt(Math.pow(pos.x - startX, 2) + Math.pow(pos.y - startY, 2))
      tempShape.value = new Konva.Circle({
        x: startX,
        y: startY,
        radius: radius,
        stroke: config.color,
        strokeWidth: config.brushSize,
        fill: 'transparent'
      })
    } else if (currentTool.value === 'rectangle') {
      const width = pos.x - startX
      const height = pos.y - startY
      tempShape.value = new Konva.Rect({
        x: width >= 0 ? startX : pos.x,
        y: height >= 0 ? startY : pos.y,
        width: Math.abs(width),
        height: Math.abs(height),
        stroke: config.color,
        strokeWidth: config.brushSize,
        fill: 'transparent'
      })
    }

    if (tempShape.value) {
      drawingLayer.value!.add(tempShape.value as any)
      drawingLayer.value!.batchDraw()
    }
  }
}

// 结束绘画
const handleDrawEnd = () => {
  if (!isPaint.value) return

  isDrawing.value = false
  isPaint.value = false

  // 如果是橡皮擦，检查与文本的碰撞
  if (currentTool.value === 'eraser' && eraserPath.value.length > 0) {
    const config = toolConfigs.value[currentTool.value]
    checkEraserCollision(eraserPath.value, config.brushSize)
    eraserPath.value = []
  }

  // 对于形状工具，临时形状已经添加到layer中，不需要额外处理
  if (currentTool.value === 'circle' || currentTool.value === 'rectangle') {
    startPoint.value = null
    tempShape.value = null
  }

  currentPath.value = null
  saveState()
}

const createTextarea = (pos: { x: number; y: number }) => {
  if (textInput.value) return

  const textarea = document.createElement('textarea')
  containerRef.value?.appendChild(textarea)
  textInput.value = textarea

  const config = toolConfigs.value.text
  const fontSize = config?.brushSize || 16
  const fontFamily = 'Arial, sans-serif'
  const lineHeight = 1.2
  const padding = 8

  // 计算合适的初始宽度
  const minWidth = 120
  const maxWidth = Math.min(400, canvasWidth.value - pos.x - 20)
  const initialWidth = Math.max(minWidth, Math.min(maxWidth, 200))

  Object.assign(textarea.style, {
    position: 'absolute',
    left: `${pos.x}px`,
    top: `${pos.y}px`,
    width: `${initialWidth}px`,
    minWidth: `${minWidth}px`,
    maxWidth: `${maxWidth}px`,
    border: '2px solid #3b82f6',
    borderRadius: '8px',
    padding: `${padding}px`,
    fontSize: `${fontSize}px`,
    fontFamily: fontFamily,
    lineHeight: lineHeight.toString(),
    color: config?.color || '#000000',
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    backdropFilter: 'blur(8px)',
    minHeight: `${fontSize * lineHeight + padding * 2}px`,
    zIndex: '10001',
    resize: 'none',
    overflow: 'hidden',
    outline: 'none',
    boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15)',
    transition: 'all 0.2s ease',
    wordWrap: 'break-word',
    whiteSpace: 'pre-wrap'
  })

  textarea.placeholder = '输入文本...'
  textarea.focus()

  // 自动调整高度
  const autoResize = () => {
    textarea.style.height = 'auto'
    textarea.style.height =
      Math.max(textarea.scrollHeight, fontSize * lineHeight + padding * 2) + 'px'
  }

  textarea.addEventListener('input', autoResize)
  autoResize()

  const handleKeyDown = (e: KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      finalizeText()
    } else if (e.key === 'Escape') {
      cancelText()
    }
  }

  textarea.addEventListener('keydown', handleKeyDown)
  textarea.addEventListener('blur', finalizeText)

  // 存储清理函数
  textInput.value!._cleanup = () => {
    textarea.removeEventListener('keydown', handleKeyDown)
    textarea.removeEventListener('blur', finalizeText)
    textarea.removeEventListener('input', autoResize)
  }
}

const finalizeText = () => {
  if (!textInput.value) return

  // 执行清理
  if (textInput.value._cleanup) {
    textInput.value._cleanup()
  }

  const text = textInput.value.value.trim()
  const textarea = textInput.value
  const x = parseFloat(textarea.style.left || '0')
  const y = parseFloat(textarea.style.top || '0')

  // 获取输入框的实际宽度
  const textareaWidth = textarea.getBoundingClientRect().width
  const textareaHeight = textarea.getBoundingClientRect().height

  if (text) {
    // 如果是编辑状态，更新旧节点
    if (editingTextNode.value) {
      editingTextNode.value.text(text)
      // 保持原有的宽度设置
      editingTextNode.value.show()
      drawingLayer.value?.batchDraw()
      editingTextNode.value = null
      saveState()
    } else {
      const config = toolConfigs.value.text
      const fontSize = config?.brushSize || 16
      const fontFamily = 'Arial, sans-serif'
      const lineHeight = 1.2
      const padding = 8

      // 创建临时canvas来测量文本宽度
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')!
      ctx.font = `${fontSize}px ${fontFamily}`

      // 计算文本行数和最大行宽度
      const lines = text.split('\n')
      let maxLineWidth = 0
      lines.forEach((line) => {
        const lineWidth = ctx.measureText(line).width
        maxLineWidth = Math.max(maxLineWidth, lineWidth)
      })

      // 设置文本宽度为实际需要的宽度加上一些边距
      const textWidth = Math.max(maxLineWidth + padding * 2, 60)

      const textNode = new Konva.Text({
        x,
        y,
        text,
        fontSize,
        fontFamily,
        fill: config?.color || '#000000',
        padding: 5,
        draggable: currentTool.value !== 'eraser', // 橡皮擦工具时禁用拖拽
        name: 'text-node',
        width: textWidth, // 设置固定宽度
        lineHeight: lineHeight,
        wrap: 'word',
        ellipsis: false
      })

      // 优化双击编辑响应
      let clickTimeout: ReturnType<typeof setTimeout> | null = null
      let clickCount = 0

      textNode.on('click', () => {
        clickCount++
        if (clickCount === 1) {
          clickTimeout = setTimeout(() => {
            clickCount = 0
          }, 300)
        } else if (clickCount === 2) {
          if (clickTimeout) {
            clearTimeout(clickTimeout)
            clickTimeout = null
          }
          clickCount = 0
          editText(textNode)
        }
      })

      // 保留原有的双击事件作为备用
      textNode.on('dblclick', () => {
        editText(textNode)
      })

      // 添加到Transformer的功能
      textNode.on('transform', () => {
        const scaleX = textNode.scaleX()
        const scaleY = textNode.scaleY()

        textNode.setAttrs({
          width: Math.max(textNode.width() * scaleX, 20),
          height: Math.max(textNode.height() * scaleY, 20),
          scaleX: 1,
          scaleY: 1
        })
      })

      drawingLayer.value?.add(textNode)
      saveState()
    }
  } else if (editingTextNode.value) {
    // 如果文本为空且在编辑模式，则删除该节点
    editingTextNode.value.destroy()
    drawingLayer.value?.batchDraw()
    editingTextNode.value = null
    saveState()
  }

  containerRef.value?.removeChild(textarea)
  textInput.value = null
}

const cancelText = () => {
  if (!textInput.value) return

  // 如果是编辑状态，恢复显示
  if (editingTextNode.value) {
    editingTextNode.value.show()
    drawingLayer.value?.batchDraw()
    editingTextNode.value = null
  }

  if (textInput.value._cleanup) {
    textInput.value._cleanup()
  }
  containerRef.value?.removeChild(textInput.value)
  textInput.value = null
}

const editText = (node: Konva.Text) => {
  if (textInput.value) return

  // 隐藏编辑按钮
  hideTextEditButtons()

  // 隐藏原文本，显示输入框
  editingTextNode.value = node
  node.hide()
  transformer.value?.nodes([])
  drawingLayer.value?.batchDraw()

  const textarea = document.createElement('textarea')
  containerRef.value?.appendChild(textarea)
  textInput.value = textarea

  // 设置输入框位置和样式
  const textPosition = node.absolutePosition()
  const nodeWidth = node.width() * node.scaleX()
  const nodeHeight = node.height() * node.scaleY()
  const fontSize = node.fontSize()
  const fontFamily = node.fontFamily()
  const lineHeight = node.lineHeight()
  const padding = 8

  Object.assign(textarea.style, {
    position: 'absolute',
    left: `${textPosition.x}px`,
    top: `${textPosition.y}px`,
    width: `${Math.max(nodeWidth, 120)}px`,
    minWidth: '120px',
    maxWidth: `${Math.min(400, canvasWidth.value - textPosition.x - 20)}px`,
    border: '2px solid #3b82f6',
    borderRadius: '8px',
    padding: `${padding}px`,
    fontSize: `${fontSize}px`,
    fontFamily: fontFamily,
    lineHeight: lineHeight.toString(),
    color: node.fill(),
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    backdropFilter: 'blur(8px)',
    zIndex: '10001',
    resize: 'none',
    overflow: 'hidden',
    outline: 'none',
    boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15)',
    transition: 'all 0.2s ease',
    wordWrap: 'break-word',
    whiteSpace: 'pre-wrap'
  })

  textarea.value = node.text()
  textarea.focus()

  // 自动调整高度
  const autoResize = () => {
    textarea.style.height = 'auto'
    textarea.style.height =
      Math.max(textarea.scrollHeight, fontSize * lineHeight + padding * 2) + 'px'
  }

  textarea.addEventListener('input', autoResize)
  autoResize()

  // 选中所有文本
  textarea.select()

  const handleKeyDown = (e: KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      finalizeText()
    } else if (e.key === 'Escape') {
      cancelText()
    }
  }

  textarea.addEventListener('blur', finalizeText)
  textarea.addEventListener('keydown', handleKeyDown)

  textInput.value._cleanup = () => {
    textarea.removeEventListener('blur', finalizeText)
    textarea.removeEventListener('keydown', handleKeyDown)
    textarea.removeEventListener('input', autoResize)
  }
}

// 添加背景线条 - 使用props.gridSpacing间距，只在作答区域
const addBackgroundLines = () => {
  if (!backgroundLayer.value) return

  // 清除现有背景图案
  clearBackgroundPatterns()

  const config = toolConfigs.value['background-line']
  const lineSpacing = props.gridSpacing // 使用prop控制间距
  const startY = realQuestionHeight.value + 10 // 从题目区域下方开始，留少量间距
  const endY = totalHeight.value - 10 // 到画板底部，留少量间距

  // 确保有足够的作答区域高度来绘制线条
  if (actualAnswerHeight.value < 20) {
    return
  }

  // 绘制横线 - 只在作答区域
  let lineCount = 0
  for (let y = startY; y < endY; y += lineSpacing) {
    const line = new Konva.Line({
      points: [10, y, canvasWidth.value - 10, y],
      stroke: config.color,
      strokeWidth: config.brushSize,
      opacity: 0.8,
      listening: false // 不响应事件
    })
    backgroundLayer.value.add(line as any)
    lineCount++
  }

  backgroundLayer.value.batchDraw()
  currentBackgroundType.value = 'line'
}

// 添加网格 - 使用props.gridSpacing间距，只在作答区域
const addGrid = () => {
  if (!backgroundLayer.value) return

  // 清除现有背景图案
  clearBackgroundPatterns()

  const config = toolConfigs.value.grid
  const gridSpacing = props.gridSpacing // 使用prop控制间距
  const startY = realQuestionHeight.value + 10 // 从题目区域下方开始，留少量间距
  const endY = totalHeight.value - 10 // 到画板底部，留少量间距
  const startX = 10
  const endX = canvasWidth.value - 10

  // 确保有足够的作答区域高度来绘制网格
  if (actualAnswerHeight.value < 20) {
    return
  }

  let horizontalLineCount = 0
  let verticalLineCount = 0

  // 绘制横线 - 只在作答区域
  for (let y = startY; y < endY; y += gridSpacing) {
    const horizontalLine = new Konva.Line({
      points: [startX, y, endX, y],
      stroke: config.color,
      strokeWidth: config.brushSize,
      listening: false // 不响应事件
    })
    backgroundLayer.value.add(horizontalLine as any)
    horizontalLineCount++
  }

  // 绘制竖线 - 只在作答区域，使用相同颜色
  for (let x = startX; x < endX; x += gridSpacing) {
    const verticalLine = new Konva.Line({
      points: [x, startY, x, endY],
      stroke: config.color, // 使用相同的颜色
      strokeWidth: config.brushSize,
      listening: false // 不响应事件
    })
    backgroundLayer.value.add(verticalLine as any)
    verticalLineCount++
  }

  backgroundLayer.value.batchDraw()
  currentBackgroundType.value = 'grid'
}

// 清除背景图案
const clearBackgroundPatterns = () => {
  if (!backgroundLayer.value) return

  // 保留前两个背景矩形（题目区域和作答区域），移除其他图案
  const children = backgroundLayer.value.children.slice()
  children.forEach((child, index) => {
    if (index > 1) {
      // 跳过前两个背景矩形
      child.destroy()
    }
  })

  // 强制重绘
  backgroundLayer.value.batchDraw()
  currentBackgroundType.value = 'none'
}

// 更新背景区域尺寸
const updateBackgroundAreas = () => {
  if (!backgroundLayer.value) return

  // 更新题目区域背景（高度不变）
  const questionBg = backgroundLayer.value.children[0] as Konva.Rect
  if (questionBg) {
    questionBg.width(drawingWidth.value)
    questionBg.height(realQuestionHeight.value) // 题目高度保持不变
  }

  // 更新作答区域背景（高度根据总高度变化）
  const answerBg = backgroundLayer.value.children[1] as Konva.Rect
  if (answerBg) {
    answerBg.x(0)
    answerBg.y(realQuestionHeight.value)
    answerBg.width(drawingWidth.value)
    answerBg.height(actualAnswerHeight.value) // 使用计算出的作答区域高度
  }
}

// 保存状态到历史记录 - 只保存绘画层内容
const saveState = () => {
  if (!drawingLayer.value) return

  historyStep.value++
  history.value.length = historyStep.value

  // 只保存绘画层的JSON数据，不包括背景层
  const drawingData = {
    type: 'Layer',
    attrs: drawingLayer.value.getAttrs(),
    children: drawingLayer.value.children.map((child) => child.toObject())
  }

  history.value.push(JSON.stringify(drawingData))
}

// 撤销
const undo = () => {
  if (historyStep.value > 0) {
    historyStep.value--
    restoreState()
  }
}

// 重做
const redo = () => {
  if (historyStep.value < history.value.length - 1) {
    historyStep.value++
    restoreState()
  }
}

// 恢复状态 - 只恢复绘画层内容
const restoreState = () => {
  if (!drawingLayer.value || historyStep.value < 0 || historyStep.value >= history.value.length)
    return

  try {
    const json = history.value[historyStep.value]
    const layerData = JSON.parse(json)

    // 清空当前绘画层
    drawingLayer.value.destroyChildren()

    // 恢复绘画内容
    if (layerData.children && Array.isArray(layerData.children)) {
      layerData.children.forEach((childData: any) => {
        try {
          const node = Konva.Node.create(childData)
          drawingLayer.value!.add(node)
        } catch (error) {
          console.warn('恢复绘画元素失败:', error, childData)
        }
      })
    }

    // 重新绘制绘画层
    drawingLayer.value.batchDraw()
  } catch (error) {
    console.error('恢复历史状态失败:', error)
  }
}

// 清空画板 - 只清空绘画层，保留背景图案
const clearCanvas = () => {
  if (drawingLayer.value) {
    drawingLayer.value.destroyChildren()
    saveState()
  }
}

// 导出画板 - 基础版本，兼容iPad
const exportCanvasOnly = () => {
  if (!stage.value) return

  try {
    const dataURL = stage.value.toDataURL({
      pixelRatio: 2,
      mimeType: 'image/png',
      quality: 1
    })

    // 桌面端：使用下载链接
    const link = document.createElement('a')
    link.download = `drawing_${Date.now()}.png`
    link.href = dataURL
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  } catch (error) {
    console.error('导出失败:', error)
    alert('导出失败，请重试')
  }
}

// 导出题目+画板组合 - 修正合并逻辑，兼容iPad
const exportWithQuestion = async () => {
  try {
    // 1. 获取题目元素
    const questionElement = props.questionWrapRef
    if (!questionElement) {
      console.warn('未找到题目元素，使用画板导出')
      exportCanvasOnly()
      return
    }

    // 2. 截取题目DOM
    const questionCanvas = await html2canvas(questionElement, {
      useCORS: true,
      allowTaint: true
    })

    // 3. 获取画板内容
    const drawingDataURL = stage.value!.toDataURL({
      pixelRatio: 2,
      mimeType: 'image/png',
      quality: 1
    })
    const drawingImg = new Image()

    drawingImg.onload = () => {
      // 4. 创建合成画布
      const combinedCanvas = document.createElement('canvas')
      const ctx = combinedCanvas.getContext('2d')!

      // 设置合成画布尺寸为题目和画板的最大宽度和总高度
      const maxWidth = Math.max(questionCanvas.width, drawingImg.width)
      const totalHeight = Math.max(questionCanvas.height, drawingImg.height)

      combinedCanvas.width = maxWidth
      combinedCanvas.height = totalHeight

      // 5. 先绘制题目DOM作为底层
      ctx.drawImage(questionCanvas, 0, 0)

      // 6. 再绘制画板内容作为上层，保持相同的坐标位置
      ctx.drawImage(drawingImg, 0, 0)

      // 7. 导出合成图片 - iPad兼容
      const finalDataURL = combinedCanvas.toDataURL('image/png', 1.0)

      // 桌面端：使用下载链接
      const link = document.createElement('a')
      link.download = `question_answer_${Date.now()}.png`
      link.href = finalDataURL
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }

    drawingImg.onerror = () => {
      console.error('画板图片加载失败')
      alert('导出失败，请重试')
    }

    drawingImg.src = drawingDataURL
  } catch (error) {
    console.error('导出题目+答案失败:', error)
    alert('导出失败，请重试')
    // 降级到仅导出画板
    exportCanvasOnly()
  }
}

// 监听工具变化，更新已有文本节点的draggable属性
watch(currentTool, (newTool) => {
  if (!drawingLayer.value) return

  // 获取所有文本节点
  const textNodes = drawingLayer.value.find('.text-node') as Konva.Text[]

  // 更新每个文本节点的draggable属性
  textNodes.forEach((textNode) => {
    textNode.draggable(newTool !== 'eraser')
  })

  // 如果切换到橡皮擦工具，隐藏文本编辑按钮
  if (newTool === 'eraser') {
    hideTextEditButtons()
  }
})

// 监听全局touchMode变化，同步更新本地状态
watch(
  () => props.globalTouchMode,
  (newMode) => {
    if (newMode) {
      touchMode.value = newMode
    }
  },
  { immediate: true }
)

// 监听本地touchMode变化，通知父组件
watch(touchMode, (newMode) => {
  emit('touchModeChange', newMode)

  // 更新canvas的touchAction样式
  if (canvasContainerRef.value) {
    const konvaContent = canvasContainerRef.value.querySelector(
      '.konvajs-content'
    ) as HTMLElement | null
    if (konvaContent) {
      konvaContent.style.setProperty('touch-action', newMode, 'important')
    }
  }
})

// 处理工具栏动作 - 修复背景图案功能
const handleToolbarAction = (action: string) => {
  switch (action) {
    case 'clear':
      clearCanvas()
      break
    case 'export':
      exportWithQuestion()
      break
    case 'history-back':
      undo()
      break
    case 'history-forward':
      redo()
      break
    case 'background-line':
      if (currentBackgroundType.value === 'line') {
        clearBackgroundPatterns()
      } else {
        addBackgroundLines()
      }
      break
    case 'grid':
      if (currentBackgroundType.value === 'grid') {
        clearBackgroundPatterns()
      } else {
        addGrid()
      }
      break
  }
}

// 拖拽调整高度相关 - 支持触摸和鼠标
let scrollTimeout: number | null = null

const handleResizeStart = (e: MouseEvent | TouchEvent) => {
  e.preventDefault()
  e.stopPropagation()

  isDragging.value = true
  dragStartY.value = 'touches' in e ? e.touches[0].clientY : e.clientY
  startAnswerHeight.value = answerHeight.value

  // 支持触摸和鼠标事件
  document.addEventListener('mousemove', handleResizeMove, { passive: false })
  document.addEventListener('mouseup', handleResizeEnd, { passive: false })
  document.addEventListener('touchmove', handleResizeMove, { passive: false })
  document.addEventListener('touchend', handleResizeEnd, { passive: false })

  // 添加视觉反馈
  if (resizeHandleRef.value) {
    resizeHandleRef.value.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
  }
}

const handleResizeMove = (e: MouseEvent | TouchEvent) => {
  if (!isDragging.value) return

  e.preventDefault()

  const currentY = 'touches' in e ? e.touches[0].clientY : e.clientY
  const deltaY = currentY - dragStartY.value

  // 优化：使用requestAnimationFrame提高流畅性
  requestAnimationFrame(() => {
    // 调整拖拽灵敏度，提高响应性
    const adjustedDelta = deltaY * 0.8 // 从0.5提高到0.8
    const newHeight = Math.max(props.minAnswerHeight, startAnswerHeight.value + adjustedDelta)

    answerHeight.value = newHeight

    // 设置question-content-wrap高度等于画板总高度，但题目内容高度不变
    const questionWrapElement = props.questionWrapRef
    if (questionWrapElement) {
      const totalCanvasHeight = realQuestionHeight.value + newHeight
      questionWrapElement.style.height = totalCanvasHeight + 'px'
    }

    // 画板最小高度为题目高度 + 最小作答区域高度
    const minTotalHeight = realQuestionHeight.value + props.minAnswerHeight
    const currentTotalHeight = Math.max(minTotalHeight, totalHeight.value)

    if (stage.value) {
      stage.value.height(currentTotalHeight)
      canvasHeight.value = currentTotalHeight

      // 优化：只在必要时更新背景区域
      updateBackgroundAreas()

      // 优化：减少重绘频率
      if (currentBackgroundType.value === 'line') {
        addBackgroundLines()
      } else if (currentBackgroundType.value === 'grid') {
        addGrid()
      }

      // 使用batchDraw提高性能
      backgroundLayer.value?.batchDraw()
      drawingLayer.value?.batchDraw()
    }

    // 优化滚动逻辑 - 使用防抖和更智能的滚动
    if (scrollTimeout) {
      clearTimeout(scrollTimeout)
    }

    scrollTimeout = window.setTimeout(() => {
      const windowHeight = window.innerHeight
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop
      const documentHeight = document.documentElement.scrollHeight
      const viewportBottom = scrollTop + windowHeight

      // 只有当内容超出视窗底部一定距离时才滚动
      const threshold = 100 // 100px的缓冲区
      if (documentHeight > viewportBottom + threshold) {
        const targetScroll = Math.min(
          documentHeight - windowHeight,
          scrollTop + (documentHeight - viewportBottom) * 0.5 // 滚动一半的距离
        )

        window.scrollTo({
          top: targetScroll,
          behavior: 'smooth'
        })
      }
    }, 150) // 150ms防抖
  })
}

const handleResizeEnd = () => {
  isDragging.value = false

  // 清除滚动定时器
  if (scrollTimeout) {
    clearTimeout(scrollTimeout)
    scrollTimeout = null
  }

  // 恢复手柄样式
  if (resizeHandleRef.value) {
    resizeHandleRef.value.style.background = 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)'
  }

  document.removeEventListener('mousemove', handleResizeMove)
  document.removeEventListener('mouseup', handleResizeEnd)
  document.removeEventListener('touchmove', handleResizeMove)
  document.removeEventListener('touchend', handleResizeEnd)
}

// 监听工具配置变化
watch(
  toolConfigs,
  () => {
    if (currentTool.value === 'background-line') {
      addBackgroundLines()
    } else if (currentTool.value === 'grid') {
      addGrid()
    }
  },
  { deep: true }
)

// 监听高度变化
watch(totalHeight, () => {
  // 同步更新question-content-wrap高度
  const questionWrapElement = props.questionWrapRef
  if (questionWrapElement) {
    questionWrapElement.style.height = totalHeight.value + 'px'
  }

  if (stage.value) {
    stage.value.height(totalHeight.value)
    canvasHeight.value = totalHeight.value

    // 更新背景矩形尺寸
    const backgroundRect = backgroundLayer.value?.children[0] as Konva.Rect
    if (backgroundRect) {
      backgroundRect.height(totalHeight.value)
    }

    if (currentBackgroundType.value === 'line') {
      addBackgroundLines()
    } else if (currentBackgroundType.value === 'grid') {
      addGrid()
    }

    // 重新绘制图层
    backgroundLayer.value?.batchDraw()
    drawingLayer.value?.batchDraw()
  }
})

// 监听画板宽度变化
watch(drawingWidth, () => {
  if (stage.value) {
    stage.value.width(drawingWidth.value)

    // 更新背景矩形尺寸
    const backgroundRect = backgroundLayer.value?.children[0] as Konva.Rect
    if (backgroundRect) {
      backgroundRect.width(drawingWidth.value)
    }

    // 重新绘制图层
    backgroundLayer.value?.batchDraw()
    drawingLayer.value?.batchDraw()
  }
})

const forceEnableTouchScrolling = () => {
  nextTick(() => {
    if (canvasContainerRef.value) {
      const konvaContent = canvasContainerRef.value.querySelector(
        '.konvajs-content'
      ) as HTMLElement | null
      if (konvaContent) {
        // Force the browser to handle touch actions for scrolling.
        konvaContent.style.setProperty('touch-action', 'auto', 'important')
      }
    }
  })
}

// 暴露给父组件的方法
defineExpose({
  handleToolbarAction,
  undo,
  redo,
  clearCanvas,
  exportCanvasOnly,
  exportWithQuestion,
  calculateQuestionHeight,
  handleResize
})

// 生命周期
onMounted(() => {
  // 计算真实题目高度
  calculateQuestionHeight()

  initKonva()

  window.addEventListener('resize', handleResize)
  window.addEventListener('keydown', handleWindowKeyDown)
})

onUnmounted(() => {
  stage.value?.destroy()
  document.removeEventListener('mousemove', handleResizeMove)
  document.removeEventListener('mouseup', handleResizeEnd)
  document.removeEventListener('touchmove', handleResizeMove)
  document.removeEventListener('touchend', handleResizeEnd)
  window.removeEventListener('resize', handleResize)
  window.removeEventListener('keydown', handleWindowKeyDown)

  // 清理触摸模式事件监听器
  if (canvasContainerRef.value && touchModeHandler.value) {
    canvasContainerRef.value.removeEventListener('pointerdown', touchModeHandler.value)
  }
})

const handleWindowKeyDown = (e: KeyboardEvent) => {
  if ((e.key === 'Delete' || e.key === 'Backspace') && transformer.value) {
    const selectedNodes = transformer.value.nodes()
    if (selectedNodes.length > 0) {
      e.preventDefault()
      selectedNodes.forEach((node) => {
        // 如果删除的是当前选中的文本节点，隐藏编辑按钮
        if (selectedTextNode.value && selectedTextNode.value._id === node._id) {
          hideTextEditButtons()
        }
        node.destroy()
      })
      transformer.value.nodes([])
      saveState()
    }
  }
}
</script>

<template>
  <div class="draw-board-content" ref="containerRef">
    <!-- 画板容器 -->
    <div class="canvas-wrapper">
      <!-- 画板区域 -->
      <div
        class="canvas-container"
        ref="canvasContainerRef"
        :style="{
          height: totalHeight + 'px',
          width: `calc(100% - ${handleWidth}px)`,
          left: '0px',
          top: '0px',
          touchAction: touchMode
        }"
      >
        <!-- Konva canvas 会在这里渲染 -->
      </div>

      <!-- 操作区域边框 -->
      <div
        class="answer-area-border"
        :style="{
          top: realQuestionHeight + 'px',
          width: `calc(100% - ${handleWidth}px)`,
          height: actualAnswerHeight + 'px'
        }"
      ></div>

      <!-- 右侧拖拽调整手柄 -->
      <div
        class="resize-handle"
        ref="resizeHandleRef"
        :style="{
          width: handleWidth + 'px',
          right: '0px',
          top: realQuestionHeight + 'px',
          height: actualAnswerHeight + 'px'
        }"
        @mousedown="handleResizeStart"
        @touchstart="handleResizeStart"
      >
        <div class="handle-indicator">
          <!-- 增大的拖拽图标 -->
          <div class="handle-dots">
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
          </div>

          <!-- 更大的箭头指示器 -->
          <div class="handle-arrows">
            <div class="arrow up">▲</div>
            <div class="arrow down">▼</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.draw-board-content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent; // 确保整个画板背景透明
  z-index: 10;

  .canvas-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
  }

  .canvas-container {
    position: absolute;
    left: 0;
    top: 0;
    background: transparent !important; // 强制透明背景，让Konva内容显示
    border: none; // 移除边框，避免遮挡题目
    cursor: crosshair;
    z-index: 10; // 提高z-index确保在边框之上
    overflow: visible; // 确保内容可见

    // 确保pointer事件正常工作
    touch-action: none; // 禁用默认触摸行为，允许pointer事件
    pointer-events: auto; // 确保pointer事件可以触发

    @media (pointer: fine) {
      cursor: crosshair;
    }

    &:global(.konvajs-content) {
      position: relative !important;
    }

    // 确保Konva canvas元素正确显示
    canvas {
      display: block !important;
      background: transparent !important;
      touch-action: none; // 确保canvas也禁用默认触摸行为
      pointer-events: auto; // 确保canvas可以接收pointer事件
    }
  }

  .answer-area-border {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    // border: 1px solid #cccccc;
    pointer-events: none;
    z-index: 5; // 降低z-index确保在canvas之下
  }

  .resize-handle {
    position: absolute;
    right: 0;
    top: 0;
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #a855f7 100%);
    border: none;
    border-radius: 12px 0 0 12px;
    cursor: ns-resize;
    display: flex;
    align-items: center;
    justify-content: center;
    user-select: none;
    z-index: 20;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
      -4px 0 16px rgba(99, 102, 241, 0.3),
      -1px 0 4px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    overflow: hidden;

    // 添加渐变光效
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -50%;
      width: 200%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
      transform: translateX(-100%);
      transition: transform 0.6s ease;
    }

    // 支持触摸拖拽
    touch-action: pan-y;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;

    // 悬停效果
    &:hover {
      box-shadow:
        -6px 0 24px rgba(99, 102, 241, 0.4),
        -2px 0 8px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
      transform: translateX(-4px);

      &::before {
        transform: translateX(100%);
      }
    }

    // 激活状态
    &:active {
      transform: translateX(-2px);
      box-shadow:
        -4px 0 16px rgba(99, 102, 241, 0.5),
        -1px 0 4px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
    }

    .handle-indicator {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 12px;
      padding: 8px;
      color: white;
      font-weight: 500;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }

    .handle-dots {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 4px;
      opacity: 0.9;

      .dot {
        width: 4px;
        height: 4px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.8);
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        transition: all 0.3s ease;
      }
    }

    .handle-arrows {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 4px;
      opacity: 0.9;

      .arrow {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.9);
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        transition: all 0.3s ease;

        &.up {
          transform: translateY(-1px);
        }

        &.down {
          transform: translateY(1px);
        }
      }
    }

    // 增强交互反馈
    &:hover .handle-dots .dot {
      background: rgba(255, 255, 255, 1);
      transform: scale(1.2);
    }

    &:hover .handle-arrows .arrow {
      color: rgba(255, 255, 255, 1);
      transform: scale(1.1);

      &.up {
        transform: translateY(-2px) scale(1.1);
      }

      &.down {
        transform: translateY(2px) scale(1.1);
      }
    }

    // 拖拽状态
    &.dragging {
      cursor: grabbing;
      transform: translateX(-6px);
    }

    // 响应式设计
    @media (max-width: 768px) {
      border-radius: 8px 0 0 8px;

      .handle-indicator {
        gap: 8px;
        padding: 6px;
      }

      .handle-dots {
        gap: 3px;

        .dot {
          width: 3px;
          height: 3px;
        }
      }

      .handle-arrows .arrow {
        font-size: 10px;
      }
    }
  }
}

// 全局样式，用于文本编辑按钮
:global(.text-edit-buttons) {
  /* 确保按钮在所有情况下都能正确显示 */
  pointer-events: auto !important;

  /* 添加动画效果 */
  animation: fadeInUp 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  button {
    /* 确保按钮可点击 */
    pointer-events: auto !important;

    /* 添加点击反馈 */
    &:active {
      transform: scale(0.95) !important;
    }
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px) translateY(-50%);
  }
  to {
    opacity: 1;
    transform: translateY(0) translateY(-50%);
  }
}

// 文本输入框样式
:global(textarea) {
  /* 确保输入框在所有情况下都能正确显示 */
  &[style*='position: absolute'][style*='z-index: 10001'] {
    /* 添加动画效果 */
    animation: fadeInScale 0.2s cubic-bezier(0.4, 0, 0.2, 1);

    /* 确保输入框可以接收焦点 */
    pointer-events: auto !important;

    /* 自定义滚动条 */
    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.1);
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(59, 130, 246, 0.5);
      border-radius: 2px;

      &:hover {
        background: rgba(59, 130, 246, 0.7);
      }
    }
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
</style>
