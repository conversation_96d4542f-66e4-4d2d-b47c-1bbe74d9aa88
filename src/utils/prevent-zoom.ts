/**
 * 防止页面缩放的工具函数
 */

// 防止双指缩放和双击缩放
export function preventZoom() {
  // 防止双指缩放
  document.addEventListener(
    'touchstart',
    function (event) {
      if (event.touches.length > 1) {
        event.preventDefault()
      }
    },
    { passive: false }
  )

  document.addEventListener(
    'touchmove',
    function (event) {
      if (event.touches.length > 1) {
        event.preventDefault()
      }
    },
    { passive: false }
  )

  // 防止双击缩放（移动端）
  let lastTouchEnd = 0
  document.addEventListener(
    'touchend',
    function (event) {
      const now = new Date().getTime()
      if (now - lastTouchEnd <= 300) {
        event.preventDefault()
      }
      lastTouchEnd = now
    },
    { passive: false }
  )

  // 防止鼠标滚轮缩放（PC端）
  document.addEventListener(
    'wheel',
    function (event) {
      if (event.ctrlKey) {
        event.preventDefault()
      }
    },
    { passive: false }
  )

  // 防止键盘快捷键缩放
  document.addEventListener('keydown', function (event) {
    if (
      (event.ctrlKey || event.metaKey) &&
      (event.key === '+' || event.key === '-' || event.key === '=' || event.key === '0')
    ) {
      event.preventDefault()
    }
  })
}

// 禁用浏览器默认的缩放行为
export function disableBrowserZoom() {
  // 禁用浏览器的缩放功能
  document.addEventListener('gesturestart', function (event) {
    event.preventDefault()
  })

  document.addEventListener('gesturechange', function (event) {
    event.preventDefault()
  })

  document.addEventListener('gestureend', function (event) {
    event.preventDefault()
  })
}

// 初始化防缩放
export function initPreventZoom() {
  preventZoom()
  disableBrowserZoom()

  // 确保viewport设置生效
  const metaViewport = document.querySelector('meta[name="viewport"]')
  if (metaViewport) {
    metaViewport.setAttribute(
      'content',
      'width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover'
    )
  }
}
