import { ref, onMounted, onUnmounted } from 'vue'
export function useCustomInterval(fn?: () => void, interval: number = 1000) {
  let timer: ReturnType<typeof setInterval> | null = null
  const sec = ref(0)
  function startTimer() {
    if (timer) {
      clearTimer()
    }
    timer = setInterval(() => {
      sec.value += 1
      fn?.()
    }, interval)
  }

  function clearTimer() {
    if (timer) {
      clearInterval(timer)
      timer = null
    }
  }

  onMounted(() => {
    startTimer()
  })

  onUnmounted(() => {
    clearTimer()
  })

  return {
    startTimer,
    clearTimer,
    sec
  }
}
