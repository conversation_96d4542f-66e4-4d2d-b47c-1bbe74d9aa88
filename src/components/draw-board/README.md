# 画板工具栏组件

## 功能特性

### 主要工具

1. **画笔** - 支持颜色选择和画笔大小调节
2. **橡皮擦** - 支持大小调节
3. **圆形** - 支持线条粗细调节
4. **矩形** - 支持线条粗细调节
5. **背景横线** - 间距固定为30px
6. **田字格** - 格子大小固定为30x30px
7. **历史记录** - 前进/后退功能
8. **清空** - 清空画板
9. **导出** - 导出画板内容

### 二级悬浮菜单

- **画笔**：颜色选择 + 画笔大小
- **橡皮擦**：画笔大小
- **圆形**：画笔大小
- **矩形**：画笔大小

## 组件结构

```
src/components/draw-board/
├── types.ts           # 类型定义
├── SubMenu.vue        # 二级悬浮菜单组件
├── DrawToolBar.vue    # 主工具栏组件
├── DrawBoardContent.vue # 画板内容组件
├── index.vue          # 演示页面
└── README.md         # 说明文档
```

## 使用方法

### 基本使用

```vue
<template>
  <DrawToolBar
    @tool-change="handleToolChange"
    @config-change="handleConfigChange"
    @action="handleAction"
  />
</template>

<script setup>
import DrawToolBar from '@/components/draw-board/DrawToolBar.vue'

const handleToolChange = (toolType) => {
  console.log('工具切换:', toolType)
}

const handleConfigChange = (toolType, config) => {
  console.log('工具配置更新:', toolType, config)
}

const handleAction = (action) => {
  console.log('执行动作:', action)
}
</script>
```

### 事件说明

#### tool-change

工具切换事件

- **参数**: `toolType: ToolType`
- **说明**: 当用户选择不同工具时触发

#### config-change

工具配置更新事件

- **参数**:
  - `toolType: ToolType` - 工具类型
  - `config: { brushSize?: number; color?: string }` - 配置参数
- **说明**: 当用户修改工具的颜色或大小时触发

#### action

动作执行事件

- **参数**: `action: 'clear' | 'export' | 'history-back' | 'history-forward'`
- **说明**: 当用户执行清空、导出、撤销、重做操作时触发

### 工具类型

```typescript
type ToolType =
  | 'brush' // 画笔
  | 'eraser' // 橡皮擦
  | 'circle' // 圆形
  | 'rectangle' // 矩形
  | 'background-line' // 背景横线
  | 'grid' // 田字格
  | 'history-back' // 后退
  | 'history-forward' // 前进
  | 'clear' // 清空
  | 'export' // 导出
```

### 默认配置

每个工具都有独立的配置：

```typescript
const defaultConfigs = {
  brush: { brushSize: 5, color: '#000000' },
  eraser: { brushSize: 10, color: '#ffffff' },
  circle: { brushSize: 2, color: '#000000' },
  rectangle: { brushSize: 2, color: '#000000' },
  'background-line': { brushSize: 1, color: '#e0e0e0' },
  grid: { brushSize: 1, color: '#e0e0e0' }
}
```

## 技术实现

- **框架**: Vue 3 + TypeScript
- **样式**: SCSS
- **画板库**: 预留 KonvaJS 接口
- **响应式**: 支持移动端和桌面端

## 特色功能

1. **工具状态指示器** - 显示当前工具的颜色和大小
2. **悬浮菜单** - 点击工具显示二级配置菜单
3. **独立配置** - 每个工具的颜色和大小相互独立
4. **视觉反馈** - 丰富的hover和active状态
5. **无障碍** - 支持键盘导航和屏幕阅读器

## 扩展性

组件设计具有良好的扩展性：

- 可以通过修改 `toolbarItems` 配置添加新工具
- 支持自定义工具图标和名称
- 二级菜单可以灵活配置不同的选项类型
- 样式可以通过CSS变量进行主题定制
