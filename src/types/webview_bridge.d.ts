export interface IMessage {
  type: string // 消息类型
  params?: Record<string, any>
  timeout?: number // 可选的超时时间（毫秒）
}

// 扩展 Window 接口，添加 FlutterBridge 和 flutter_inappwebview 类型定义
declare global {
  interface Window {
    FlutterBridge?: {
      postMessage: (message: string) => void
    }
    flutter_inappwebview?: {
      callHandler: (handlerName: string, data: string) => Promise<any>
    }
    [key: string]: any // 允许动态添加属性
  }
}

// 定义事件处理器类型
export type EventHandler = (...args: any[]) => any
