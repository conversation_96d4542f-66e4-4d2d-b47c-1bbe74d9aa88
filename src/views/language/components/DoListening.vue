<script setup lang="ts">
import { ref, type ComponentInstance } from 'vue'
import { useRoute } from 'vue-router'

import { useCustomInterval } from '@/hooks'
import { useQuizs } from '../hooks/useQuizs'
import { formatTime } from '@/utils/time'
import AudioPlayer from '@/components/audio-player/index.vue'
import PracticeHistory from '../sub-components/PracticeHistory.vue'
import DoQuiz from '../sub-components/DoQuiz.vue'

const routes = useRoute()
const { currentQuiz, quizs, loading, submitAnswer } = useQuizs()

const comRef = ref<ComponentInstance<typeof DoQuiz> | null>(null)
const { sec } = useCustomInterval()
const tabs = [
  { label: '题目', value: 'DoQuiz', comp: DoQuiz },
  { label: '记录', value: 'PracticeHistory', comp: PracticeHistory }
]
const activeTab = ref('DoQuiz')
</script>
<template>
  <div class="quiz-wrap box-content bg-white h-[100%] min-h-[100vh]" v-show="quizs?.length">
    <van-sticky>
      <div
        class="title flex bg-bg-grey items-center justify-center h-[32px] text-text-grey leading-[32px]"
      >
        <div class="time text-[12px] flex items-center w-[70px]">
          <span class="iconfont icondaojishi text-va-blue"></span>
          <span>{{ formatTime(sec) }}</span>
        </div>
      </div>
      <van-tabs v-model:active="activeTab" class="custom-tab">
        <van-tab :title="item.label" :name="item.value" v-for="item in tabs" :key="item.value" />
      </van-tabs>
    </van-sticky>
    <DoQuiz v-show="'DoQuiz' === activeTab" ref="comRef" :initQuizs="quizs" />
    <PracticeHistory :activeTab="activeTab" v-show="'PracticeHistory' === activeTab" />
    <AudioPlayer
      :url="currentQuiz?.attachments?.[0]?.url"
      :loading="loading"
      class="fixed z-30 bottom-0 left-0 w-full"
      @submit-answers="submitAnswer(comRef?.quizs || [], sec)"
    />
  </div>
</template>

<style lang="scss" scoped>
.custom-tab {
  :deep(.van-tabs__line) {
    width: 16px;
    height: 4px;
    background-color: #6481ee;
  }
}
</style>
