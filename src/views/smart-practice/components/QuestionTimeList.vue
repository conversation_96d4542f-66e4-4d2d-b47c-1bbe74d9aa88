<script setup lang="ts">
import type { TVolQuizDataVO } from '@/services/practice'
import type { QuizWrongBookPageReqDTO } from '@/services/practice'
import { List as VanList, PullRefresh as VanPullRefresh } from 'vant'
import { defineEmits, defineProps, ref, watch } from 'vue'
import { useGlobal } from '@/hooks'

const { globalProperties } = useGlobal()

const props = defineProps<{
  // 接收父组件传入的查询条件
  exam?: number
  examBoard?: number
  subject?: number
  // 可选的初始数据，如果提供则使用，否则自行加载
  initialData?: TVolQuizDataVO[]
}>()

const emit = defineEmits<{
  (e: 'view-detail', question: TVolQuizDataVO, index: number): void
}>()


// 题目列表数据
const timeCategories = ref<TVolQuizDataVO[]>(props.initialData || [])

// 分页相关状态
const loading = ref(false)
const error = ref(false)
const finished = ref(false)
const refreshing = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 递归获取有效题干
interface ValidContent {
  content: string
  type: 'image' | 'text'
}

const getValidContent = (question: TVolQuizDataVO): ValidContent => {
  // 如果当前题目有imgContent，优先返回
  if (question.imgContent) {
    return {
      content: question.imgContent,
      type: 'image'
    }
  }
  // 如果有其他内容，暂存
  const currentContent = question.content || question.htmlContent
  if (currentContent) {
    return {
      content: currentContent,
      type: 'text'
    }
  }

  // 递归遍历所有子题目
  const traverseChildren = (children: TVolQuizDataVO[]): ValidContent | null => {
    for (const child of children) {
      // 优先检查子题目的imgContent
      if (child.imgContent) {
        return {
          content: child.imgContent,
          type: 'image'
        }
      }
      // 如果子题目有其他内容
      const childContent = child.content || child.htmlContent
      if (childContent) {
        return {
          content: childContent,
          type: 'text'
        }
      }
      // 如果当前子题目还有子题目，继续递归
      if (child.children?.length) {
        const content = traverseChildren(child.children)
        if (content) return content
      }
    }
    return null
  }

  // 如果有子题目，开始递归遍历
  if (question.children?.length) {
    const childContent = traverseChildren(question.children)
    if (childContent) return childContent
  }

  // 如果都没有找到内容，返回空字符串
  return {
    content: '',
    type: 'text'
  }
}

// 获取问题ID
const getQuestionId = (question: TVolQuizDataVO, index: number): string => {
  return `第${index + 1}题`
}

// 格式化日期
const formatDate = (timestamp?: string): string => {
  if (!timestamp) return '未知日期'

  try {
    const date = new Date(timestamp)
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
  } catch (error) {
    return '未知日期'
  }
}

// 查看问题详情
const viewQuestionDetail = (question: TVolQuizDataVO, index: number) => {
  emit('view-detail', question, index)
}

// 获取错题列表数据
const getWrongBkTimeList = async (page = 1, isRefresh = false) => {
  // 防止重复请求
  if (loading.value) {
    console.log('已有请求正在进行中，跳过此次请求')
    return
  }

  loading.value = true
  try {
    const params: QuizWrongBookPageReqDTO = {
      examBoard: props.examBoard || 0,
      exam: props.exam || 0,
      subject: props.subject || 0,
      pageNumber: page,
      pageSize: pageSize.value
    }

    const res =
      await globalProperties.$http.Practice.WrongBookAnswerController.wrongBookPage(params)
    console.log('获取按时间分类的列表:', res)

    // 更新分页信息
    const data = res.data as any
    total.value = data.total || 0

    // 直接使用返回的数据，不需要分组
    const records = data.records || []

    // 如果是刷新，则替换数据，否则追加数据
    if (isRefresh) {
      timeCategories.value = records
    } else {
      timeCategories.value = [...timeCategories.value, ...records]
    }

    // 判断是否已加载完所有数据
    finished.value = timeCategories.value.length >= total.value
  } catch (err) {
    console.error('获取按时间分类的列表失败:', err)
    error.value = true
  } finally {
    loading.value = false
  }
}

// 下拉刷新
const onRefresh = async () => {
  // 重置分页状态
  finished.value = false
  currentPage.value = 1
  error.value = false

  // 获取刷新数据
  await getWrongBkTimeList(1, true)

  // 结束刷新状态
  refreshing.value = false
}

// 加载更多
const onLoad = async () => {
  // 如果已经完成或正在加载，跳过
  if (finished.value || loading.value) {
    return
  }

  // 获取下一页数据
  currentPage.value += 1
  await getWrongBkTimeList(currentPage.value, false)
}

// 监听查询条件变化，重新获取数据
watch(
  () => [props.exam, props.examBoard, props.subject],
  (newValues, oldValues) => {
    console.log('科目条件变化检测到:', {
      oldValues: oldValues || ['undefined', 'undefined', 'undefined'],
      newValues,
      examChanged: oldValues ? newValues[0] !== oldValues[0] : true,
      examBoardChanged: oldValues ? newValues[1] !== oldValues[1] : true,
      subjectChanged: oldValues ? newValues[2] !== oldValues[2] : true
    })

    // 清空当前数据
    timeCategories.value = []
    currentPage.value = 1
    finished.value = false
    total.value = 0

    // 加载新数据
    getWrongBkTimeList(1, true)
  },
  { immediate: !props.initialData }
) // 如果没有初始数据，立即执行一次
</script>
<template>
  <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
    <van-list
      v-model:loading="loading"
      v-model:error="error"
      :finished="finished"
      :immediate-check="false"
      finished-text="没有更多了"
      @load="onLoad"
    >
      <div class="flex flex-col gap-[16px]">
        <div
          v-for="(question, index) in timeCategories"
          :key="index"
          class="bg-white rounded-[10px] p-[16px]"
          @click="viewQuestionDetail(question, index)"
        >
          <div class="flex justify-between items-center mb-[10px]">
            <div class="text-[16px] font-medium text-[#333]">
              {{ getQuestionId(question, index) }}
            </div>
            <div class="text-[14px] text-[#8F94A8]">
              {{ formatDate(question.addWrongBookTime) }}
            </div>
          </div>
          <div class="relative w-full h-[140px] rounded-[8px] overflow-hidden p-[10px]">
            <template v-if="getValidContent(question).type === 'image'">
              <img
                :src="getValidContent(question).content"
                alt="题目图片"
                class="w-full h-full object-contain"
              />
            </template>
            <template v-else>
              <div
                v-html="getValidContent(question).content"
                class="w-full h-full text-[14px]"
              ></div>
            </template>
            <div class="absolute inset-0 bg-black opacity-10"></div>
          </div>
        </div>
      </div>
    </van-list>
  </van-pull-refresh>
</template>
<style lang="scss" scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  line-clamp: 2;
  overflow: hidden;
}
</style>
