<script setup lang="ts">
import { computed, type StyleValue } from 'vue'

defineOptions({
  name: 'VaCustomButton'
})

interface Props {
  type?: 'primary' | 'text' | 'plain'
  disabled?: boolean
  loading?: boolean
  icon?: string
  customClass?: string // 唯一的自定义样式方式
  text?: string
}

const props = withDefaults(defineProps<Props>(), {
  type: 'primary',
  disabled: false,
  loading: false,
  icon: '',
  text: '',
  customClass: ''
})

const emit = defineEmits<{
  (e: 'click', event: MouseEvent): void
}>()

// 使用Tailwind样式替代原先的SCSS
const buttonClasses = computed(() => {
  const baseClasses = [
    'inline-flex items-center justify-center',
    'text-xs px-6 py-1.5',
    'rounded-full',
    'select-none',
    'va-button'
  ]

  // 按钮类型样式
  const typeClasses = {
    primary: [
      'bg-[#326eff]',
      'text-white',
      'shadow-[0_3px_0_0_#1358fd,0_4px_10px_0_rgba(25,93,255,0.4)]',
      'active:shadow-none active:translate-y-[3px]'
    ],
    plain: [
      'bg-white',
      'text-[#3c4258]',
      'border border-[#e9eaee]',
      'shadow-[0_2px_0_0_#e9ebf1]',
      'active:shadow-none active:translate-y-[2px]'
    ],
    text: ['bg-transparent', 'text-[#3c4258]', 'active:translate-y-[2px]']
  }

  // 禁用状态样式
  const disabledClasses =
    props.disabled || props.loading
      ? [
          'bg-[#f5f5f5]',
          'text-[#999]',
          'border border-[#e9eaee]',
          'opacity-80',
          'cursor-not-allowed',
          'shadow-none',
          'active:translate-y-0'
        ]
      : []

  return [
    ...baseClasses,
    ...(typeClasses[props.type] || typeClasses['plain']),
    ...disabledClasses,
    props.customClass // 用户传入的自定义类
  ]
})

const handleClick = (event: MouseEvent) => {
  if (props.disabled || props.loading) {
    return
  }
  navigator?.vibrate?.(50)
  emit('click', event)
}
</script>

<template>
  <div :class="buttonClasses" @click="handleClick($event)">
    <slot>
      <i v-if="icon" :class="icon"></i>
      <span v-if="text" class="font-semibold">{{ text }}</span>
    </slot>
  </div>
</template>
