import TransferToFlutter, { TransferType } from '@/utils/msg'
interface PreviewElement extends HTMLElement {
  __previewClickHandler?: (e: Event) => void
}

const previewImageByClick = (images: string[], startPosition: number) => {
  showImagePreview({
    className: 'custom-html-image-preview',
    images,
    closeable: true,
    startPosition,
    onClose: () => {
      TransferToFlutter({
        type: TransferType.handlePopupBack,
        data: {
          title: 'showImagePreview',
          url: window.location.href,
          status: 'show'
        }
      })
    }
  })
}

const handleImageClick = (e: Event, container: PreviewElement) => {
  const target = e.target
  if (!(target instanceof HTMLImageElement)) return

  // 实时获取可见的图片元素
  const visibleImages = Array.from(container.getElementsByTagName('img')).filter(
    (img) =>
      img.src &&
      img.src.trim() !== '' &&
      img.offsetParent !== null &&
      window.getComputedStyle(img).display !== 'none'
  )

  // 获取所有可见图片的URL
  const images = visibleImages.map((img) => img.src)
  if (images.length === 0) return

  // 实时计算当前点击图片的索引
  const currentIndex = visibleImages.findIndex((img) => img === target)
  if (currentIndex === -1) return

  previewImageByClick(images, currentIndex)

  TransferToFlutter({
    type: TransferType.handlePopupBack,
    data: {
      title: 'showImagePreview',
      url: window.location.href,
      status: 'open'
    }
  })
}

export const previewImage = {
  mounted(el: HTMLElement) {
    const container = el as PreviewElement

    // 创建统一的事件处理函数
    container.__previewClickHandler = (e: Event) => handleImageClick(e, container)

    // 为容器添加事件委托
    container.addEventListener('click', container.__previewClickHandler)

    Array.from(container.getElementsByTagName('img')).forEach((img) => {
      img.style.cursor = 'pointer'
    })
  },

  unmounted(el: HTMLElement) {
    const container = el as PreviewElement
    if (container.__previewClickHandler) {
      container.removeEventListener('click', container.__previewClickHandler)
      delete container.__previewClickHandler
    }
  }
}
