export interface AppConfig {
  vconsoleBlackList: string[]
  whiteList?: string[]
  letterList: string[]
  jwtTokenKey: string
  baseUrl: string
  apiHost: string
  clientApiHost: string
  applicationUid: string
  runEnv: string
  buriedPointAppKey: string
  studentTokenKey: string
  cacheStudentInfoKey: string
  appInfoKey: string
  fakeToken: string
  AIchatUrl: string
}

const runEnv = import.meta.env.MODE || 'uat'
const getValueByEnv = (
  mapping: { [key: string]: string },
  env = runEnv,
  defaultValue = ''
): string => {
  return mapping[env] || defaultValue
}

const config: AppConfig = {
  vconsoleBlackList: ['pro'],
  letterList: [
    'A',
    'B',
    'C',
    'D',
    'E',
    'F',
    'G',
    'H',
    'I',
    'J',
    'K',
    'L',
    'M',
    'N',
    'O',
    'P',
    'Q',
    'R',
    'S',
    'T',
    'U',
    'V',
    'W',
    'X',
    'Y',
    'Z'
  ],
  runEnv,
  applicationUid: 'Weixun_ESchool',
  jwtTokenKey: 'WEIXUN_ESCHOOL_M_TOKEN',
  appInfoKey: 'appInfo',
  studentTokenKey: 'template-user-account',
  cacheStudentInfoKey: 'template-user-account' + '-' + runEnv,
  whiteList: [],
  apiHost: getValueByEnv({
    dev: '//u-portal.visioneschool.com',
    test: '//t-portal.visioneschool.com',
    uat: '//u-portal.visioneschool.com',
    pro: '//portal.visioneschool.com'
  }),
  clientApiHost: getValueByEnv({
    dev: '//u-api.visioneschool.com',
    test: '//t-api.visioneschool.com',
    uat: '//u-api.visioneschool.com',
    pro: '//api.visioneschool.com'
  }),
  AIchatUrl: getValueByEnv({
    dev: 'https://api.visioneschool.com/ai/api/v1/vision_chat',
    test: 'https://t-api.visioneschool.com/ai/api/v1/vision_chat',
    uat: 'https://api.visioneschool.com/ai/api/v1/vision_chat',
    pro: 'https://api.visioneschool.com/ai/api/v1/vision_chat'
  }),
  buriedPointAppKey: '668e367a940d5a4c49888058',
  baseUrl: import.meta.env.MODE === 'development' ? '/dev' : import.meta.env.VITE_BASE_URL,
  fakeToken: import.meta.env.MODE === 'dev' ? 'G1002641487260946432' : ''
}

export default config
