import { ref, computed, readonly } from 'vue'

/**
 * 照片数量管理 Hook
 * 用于管理题目中照片的上传数量限制和当前数量
 */
export function usePhotoCount(limit: number = 9) {
  // 照片数量限制
  const photoCountLimit = ref(limit)

  // 当前题目图片数量
  const currentQuizPhotoCount = ref(0)

  // 剩余可上传的照片数量
  const restPhotoCountLimit = computed(() => {
    return photoCountLimit.value - currentQuizPhotoCount.value
  })

  /**
   * 设置当前照片数量
   * @param count 照片数量
   */
  const setCurrentPhotoCount = (count: number) => {
    currentQuizPhotoCount.value = Math.max(0, Math.min(count, photoCountLimit.value))
  }

  /**
   * 检查是否还能上传照片
   */
  const canUploadMore = computed(() => {
    return currentQuizPhotoCount.value < photoCountLimit.value
  })

  /**
   * 检查是否已达到上传限制
   */
  const isAtLimit = computed(() => {
    return currentQuizPhotoCount.value >= photoCountLimit.value
  })

  /**
   * 更新照片数量限制
   * @param newLimit 新的限制数量
   */
  const updatePhotoLimit = (newLimit: number) => {
    photoCountLimit.value = Math.max(1, newLimit)
    // 如果当前数量超过新限制，调整当前数量
    if (currentQuizPhotoCount.value > photoCountLimit.value) {
      currentQuizPhotoCount.value = photoCountLimit.value
    }
  }

  return {
    // 响应式数据
    photoCountLimit: readonly(photoCountLimit),
    currentQuizPhotoCount: readonly(currentQuizPhotoCount),
    restPhotoCountLimit,
    canUploadMore,
    isAtLimit,

    // 方法
    setCurrentPhotoCount,
    updatePhotoLimit
  }
}
