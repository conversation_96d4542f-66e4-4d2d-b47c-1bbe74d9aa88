import { ref, onMounted, onUnmounted } from 'vue'
import { type ToastWrapperInstance, showLoadingToast } from 'vant'

export function useLoading() {
  const toastIns = ref<ToastWrapperInstance | null>(null)
  const loading = ref<boolean>(false)

  const startLoading = () => {
    toastIns.value = showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
      overlay: true,
      overlayStyle: {
        backgroundColor: 'rgb(255, 255, 255, 1)',
        color: '#666'
      },
      className: 'global-app-loading'
    })
    loading.value = true
  }

  const endLoading = () => {
    toastIns.value?.close()
    loading.value = false
  }

  onUnmounted(() => {
    endLoading()
    toastIns.value = null
  })

  return {
    loading,
    startLoading,
    endLoading
  }
}
