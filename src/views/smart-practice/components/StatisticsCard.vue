<script setup lang="ts">
interface StatItem {
  value: number
  label: string
  unit?: string
}

const props = defineProps({
  items: {
    type: Array as () => StatItem[],
    required: true
  },
  bgColor: {
    type: String,
    default: 'white'
  },
  valueColor: {
    type: String,
    default: '#326EFF'
  },
  labelColor: {
    type: String,
    default: '#8F94A8'
  },
  height: {
    type: String,
    default: '100px'
  }
})

const emit = defineEmits(['item-click'])

// 处理单位显示
const getUnit = (item: StatItem) => {
  if (item.unit) return item.unit
  return item.label.includes('套') ? '套' : '道'
}

// 处理标签显示
const getLabel = (item: StatItem) => {
  if (item.unit) return item.label
  return item.label.replace(/[道套]$/, '')
}

// 处理项目点击
const handleItemClick = (item: StatItem) => {
  emit('item-click', item)
}
</script>

<template>
  <div class="flex items-center justify-around rounded-[10px] h-[94px]" :class="`bg-${bgColor}`">
    <div
      v-for="(item, index) in items"
      :key="index"
      class="flex flex-col items-center cursor-pointer"
      @click="handleItemClick(item)"
    >
      <div class="text-[16px] flex items-baseline">
        <span
          class="artfont text-[22px] font-[700] mr-[4px]"
          :class="index === 0 ? 'text-[#3C4258]' : 'text-[#F97171]'"
          >{{ item.value }}</span
        >
        <span class="text-[#8F94A8] text-[14px]">{{ getUnit(item) }}</span>
      </div>
      <div class="text-[14px] text-[#8F94A8]">{{ getLabel(item) }}</div>
    </div>
  </div>
</template>

<style scoped>
.cursor-pointer {
  transition: opacity 0.2s;
}
.cursor-pointer:active {
  opacity: 0.7;
}
</style>
