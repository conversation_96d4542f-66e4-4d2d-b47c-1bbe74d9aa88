<script setup lang="ts">
import { ref, type ComponentInstance } from 'vue'
import { useRoute, useRouter } from 'vue-router'

import { useCustomInterval } from '@/hooks'
import { formatTime } from '@/utils/time'

import { useQuizs } from '../hooks/useQuizs'

import OriginContent from '../sub-components/OriginContent.vue'
import DoQuiz from '../sub-components/DoQuiz.vue'
import PracticeHistory from '../sub-components/PracticeHistory.vue'

const routes = useRoute()
const router = useRouter()

const { currentQuiz, quizs, loading, submitAnswer } = useQuizs()
const comRef = ref<ComponentInstance<typeof DoQuiz> | null>(null)
const { sec } = useCustomInterval()

const tabs = [
  { label: '题目', value: 'DoQuiz', comp: DoQuiz },
  { label: '原文', value: 'OriginContent', comp: OriginContent },
  { label: '记录', value: 'PracticeHistory', comp: PracticeHistory }
]
const activeTab = ref('DoQuiz')
</script>
<template>
  <div class="quiz-wrap box-content bg-white min-h-[100vh] h-[100%]" v-show="quizs?.length">
    <van-sticky>
      <div
        class="title flex bg-bg-grey items-center justify-center text-text-grey h-[32px] leading-[32px]"
      >
        <div class="time text-[12px] flex items-center w-[70px]">
          <span class="iconfont icondaojishi text-va-blue"></span>
          <span>{{ formatTime(sec) }}</span>
        </div>
      </div>
      <van-tabs v-model:active="activeTab" class="custom-tab">
        <van-tab :title="item.label" :name="item.value" v-for="item in tabs" :key="item.value" />
      </van-tabs>
    </van-sticky>
    <DoQuiz v-show="'DoQuiz' === activeTab" ref="comRef" :initQuizs="quizs" />
    <PracticeHistory :activeTab="activeTab" v-show="'PracticeHistory' === activeTab" />
    <OriginContent
      :article="currentQuiz?.content || currentQuiz?.htmlContent || currentQuiz?.imgContent"
      v-show="'OriginContent' === activeTab"
    />
    <div
      class="save-btn-wrap px-[15px] py-[10px] bg-white w-[100%] fixed bottom-0 left-0 z-30"
      @click="submitAnswer(comRef?.quizs || [], sec)"
    >
      <div
        class="save-btn flex justify-center items-center text-white text-[16px] h-[46px] leading-[46px] font-semibold bg-va-blue rounded-[6px] text-center"
      >
        <van-loading color="#fff" class="mr-[8px]" v-show="loading" />
        <span>提交答案</span>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.custom-tab {
  :deep(.van-tabs__line) {
    width: 16px;
    height: 4px;
    background-color: #6481ee;
  }
}
</style>
