<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted } from 'vue'
// @ts-ignore
import SubMenu from './SubMenu.vue'
import type { ToolType, ToolConfig, ToolbarItem } from './types'
import { defaultToolConfigs } from './config'

defineOptions({
  name: 'DrawToolBar'
})

interface Props {
  modelValue?: ToolType
  configs?: Record<ToolType, ToolConfig>
  disabled?: boolean
  tools?: ToolType[] // 新增：指定要显示的工具类型
}

interface Emits {
  (e: 'update:modelValue', value: ToolType): void
  (e: 'update:configs', configs: Record<ToolType, ToolConfig>): void
  (
    e: 'action',
    action: 'clear' | 'export' | 'history-back' | 'history-forward' | 'background-line' | 'grid'
  ): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: 'brush',
  disabled: false,
  configs: () => defaultToolConfigs
})

const emit = defineEmits<Emits>()

// 子菜单显示状态
const isSubMenuVisible = ref(false)
const toolbarRef = ref<HTMLElement>()

// 完整工具栏配置
const allToolbarItems: ToolbarItem[] = [
  {
    type: 'brush',
    icon: '✏️',
    label: '画笔',
    hasSubMenu: true,
    subMenuItems: [
      { type: 'color', options: [] },
      { type: 'size', options: [] }
    ]
  },
  {
    type: 'eraser',
    icon: '🧽',
    label: '橡皮擦',
    hasSubMenu: true,
    subMenuItems: [{ type: 'size', options: [] }]
  },
  {
    type: 'circle',
    icon: '○',
    label: '圆形',
    hasSubMenu: true,
    subMenuItems: [
      { type: 'color', options: [] },
      { type: 'size', options: [] }
    ]
  },
  {
    type: 'rectangle',
    icon: '□',
    label: '矩形',
    hasSubMenu: true,
    subMenuItems: [
      { type: 'color', options: [] },
      { type: 'size', options: [] }
    ]
  },
  {
    type: 'text',
    icon: 'T',
    label: '文本',
    hasSubMenu: true,
    subMenuItems: [
      { type: 'color', options: [] },
      { type: 'size', options: [] }
    ]
  },
  {
    type: 'background-line',
    icon: '≡',
    label: '背景横线',
    hasSubMenu: false
  },
  {
    type: 'grid',
    icon: '⊞',
    label: '田字格',
    hasSubMenu: false
  },
  {
    type: 'history-back',
    icon: '↩️',
    label: '后退',
    hasSubMenu: false
  },
  {
    type: 'history-forward',
    icon: '↪️',
    label: '前进',
    hasSubMenu: false
  },
  {
    type: 'clear',
    icon: '🗑️',
    label: '清空',
    hasSubMenu: false
  },
  {
    type: 'export',
    icon: '📤',
    label: '导出',
    hasSubMenu: false
  }
]

// 根据props.tools过滤工具栏项
const toolbarItems = computed(() => {
  if (!props.tools || props.tools.length === 0) {
    return allToolbarItems
  }
  return allToolbarItems.filter((item) => props.tools!.includes(item.type))
})

// 当前工具配置
const currentConfig = computed(() => {
  const configs = props.configs || defaultToolConfigs
  return configs[props.modelValue] || {}
})

// 当前工具是否有子菜单
const currentToolHasSubMenu = computed(() => {
  const tool = toolbarItems.value.find((item) => item.type === props.modelValue)
  return tool?.hasSubMenu || false
})

// 是否显示子菜单（计算属性）
const showSubMenu = computed(() => {
  return currentToolHasSubMenu.value && isSubMenuVisible.value
})

// 选择工具
const selectTool = (toolType: ToolType, event: MouseEvent) => {
  if (props.disabled) return

  const tool = toolbarItems.value.find((item) => item.type === toolType)

  // 如果是动作类工具，直接执行
  if (['clear', 'export', 'history-back', 'history-forward'].includes(toolType)) {
    emit('action', toolType as 'clear' | 'export' | 'history-back' | 'history-forward')
    return
  }

  // 如果是背景横线或田字格，发射action事件而不是modelValue
  if (['background-line', 'grid'].includes(toolType)) {
    console.log('DrawToolBar发射action事件:', toolType)
    emit('action', toolType as any)
    isSubMenuVisible.value = false
    return
  }

  // 如果点击的是当前工具且有子菜单，切换子菜单显示状态
  if (props.modelValue === toolType && tool?.hasSubMenu) {
    isSubMenuVisible.value = !isSubMenuVisible.value
    return
  }

  // 设置当前工具
  emit('update:modelValue', toolType)

  // 如果新工具有子菜单，显示子菜单
  if (tool?.hasSubMenu) {
    isSubMenuVisible.value = true
  } else {
    isSubMenuVisible.value = false
  }
}

// 更新工具配置
const updateConfig = (config: { brushSize?: number; color?: string }) => {
  const newConfigs = { ...(props.configs || defaultToolConfigs) }
  Object.assign(newConfigs[props.modelValue], config)
  emit('update:configs', newConfigs)
}

// 关闭子菜单
const closeSubMenu = () => {
  isSubMenuVisible.value = false
}

// 点击外部关闭子菜单
const handleClickOutside = (event: MouseEvent) => {
  const target = event.target as HTMLElement
  // 如果点击的是工具栏或二级菜单内部，不关闭
  if (target.closest('.draw-toolbar-pad') || target.closest('.sub-menu')) {
    return
  }
  isSubMenuVisible.value = false
}

// 生命周期
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<template>
  <div class="draw-toolbar-pad" ref="toolbarRef">
    <div class="toolbar-container">
      <div
        v-for="tool in toolbarItems"
        :key="tool.type"
        class="tool-item"
        :class="{
          active: modelValue === tool.type,
          disabled: disabled,
          'has-submenu': tool.hasSubMenu,
          'submenu-open': showSubMenu && modelValue === tool.type
        }"
        :title="tool.label"
        @click="selectTool(tool.type, $event)"
      >
        <div class="tool-icon">{{ tool.icon }}</div>

        <!-- 每个工具的子菜单 -->
        <SubMenu
          v-if="tool.hasSubMenu && showSubMenu && modelValue === tool.type"
          :tool-type="tool.type"
          :sub-menu-items="tool.subMenuItems || []"
          :current-config="currentConfig"
          @update-config="updateConfig"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.draw-toolbar-pad {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 100;

  // 当工具栏在公共容器中时，使用普通布局
  .common-toolbar-container & {
    position: static;
    top: auto;
    left: auto;
    transform: none;
    z-index: auto;
  }

  .toolbar-container {
    position: relative;
    display: flex;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    padding: 8px 12px;
    gap: 6px;

    // 在公共容器中时调整样式
    .common-toolbar-container & {
      background: rgba(255, 255, 255, 0.8);
      border: 2px solid rgba(59, 130, 246, 0.15);
      box-shadow: 0 4px 16px rgba(59, 130, 246, 0.1);
      backdrop-filter: none;
    }
  }

  .tool-item {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    border-radius: 8px;
    cursor: pointer;
    user-select: none;

    &:hover:not(.disabled) {
      background-color: rgba(0, 0, 0, 0.05);
    }

    &.active {
      background-color: rgba(0, 122, 255, 0.1);
      border: 2px solid #007aff;
    }

    &.submenu-open {
      background-color: rgba(0, 122, 255, 0.15);
    }

    &.disabled {
      opacity: 0.3;
      cursor: not-allowed;
    }

    .tool-icon {
      font-size: 20px;
    }
  }
}

// iPad横屏适配
@media (orientation: landscape) and (min-width: 768px) {
  .draw-toolbar-pad {
    top: 16px;

    .toolbar-container {
      padding: 6px 10px;
      gap: 4px;
    }

    .tool-item {
      width: 28px;
      height: 28px;

      .tool-icon {
        font-size: 18px;
      }
    }
  }
}

// iPad竖屏适配
@media (orientation: portrait) and (min-width: 768px) {
  .draw-toolbar-pad {
    top: 24px;

    .toolbar-container {
      padding: 8px 12px;
      gap: 6px;
    }

    .tool-item {
      width: 30px;
      height: 30px;

      .tool-icon {
        font-size: 20px;
      }
    }
  }
}
</style>
