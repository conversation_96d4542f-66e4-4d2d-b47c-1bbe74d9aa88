import type { ToolType, ToolConfig } from './types'

// 默认工具配置（只读）
const _defaultToolConfigs: Record<ToolType, Partial<ToolConfig>> = {
  brush: { brushSize: 5, color: '#000000' },
  eraser: { brushSize: 10, color: '#808080' },
  circle: { brushSize: 2, color: '#000000' },
  rectangle: { brushSize: 2, color: '#000000' },
  text: { brushSize: 16, color: '#000000' },
  'background-line': { brushSize: 1, color: '#e0e0e0' },
  grid: { brushSize: 1, color: '#e0e0e0' },
  'history-back': {},
  'history-forward': {},
  clear: {},
  export: {}
}

// 导出深拷贝函数，确保每次获取的都是新的配置对象
export const getDefaultToolConfigs = (): Record<ToolType, ToolConfig> => {
  return JSON.parse(JSON.stringify(_defaultToolConfigs))
}

// 为了向后兼容，也导出一个 getter
export const defaultToolConfigs = getDefaultToolConfigs()
