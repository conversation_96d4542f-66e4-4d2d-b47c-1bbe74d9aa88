<script setup lang="ts">
import type { TVolQuizDataVO } from '@/services/practice/types'
import CorrectingDesc from './CorrectingDesc.vue'
const props = withDefaults(
  defineProps<{
    flattenedQuizzes: TVolQuizDataVO[]
    no?: number
    showModifyBtn?: boolean
  }>(),
  {
    flattenedQuizzes: () => [],
    showModifyBtn: () => false
  }
)
const emit = defineEmits(['modify-quiz', 'refresh-quiz'])

const handleModify = (isNeedModify: boolean) => {
  if (!isNeedModify) return
  emit('modify-quiz')
}

const handleRefresh = () => {
  emit('refresh-quiz')
}
</script>

<template>
  <div class="my-answer-content" v-preview-image>
    <div
      class="answer-item pb-[15px] mb-[15px]"
      :class="{ 'border-b border-va-line2': index !== props.flattenedQuizzes.length - 1 }"
      v-for="(item, index) in props.flattenedQuizzes"
      :key="item.uid"
    >
      <div
        class="answer-title flex items-center justify-between text-va-grey-text text-[14px] mb-[20px] pl-[10px]"
      >
        <div class="content flex items-center">
          <span
            class="seq mr-[15px] font-[600] text-[20px] align-middle"
            v-show="item.quizSeqNo || item.quizSeq || props.no"
            >{{ item.quizSeqNo || item.quizSeq || props.no }}.</span
          >
          <span class="label mr-[5px]">本题得分:</span>
          <CorrectingDesc
            v-if="[0, 9].includes(item.answerResult as number) || !item.answerResult"
            @refresh="handleRefresh"
          />
          <span class="score" v-else>
            <span
              ><span
                class="font-[600]"
                :class="{
                  'text-[#EB5858]': item.answerResult === 1,
                  'text-[#0CA840;]': item.answerResult === 2
                }"
                >{{ item.answerScore }}</span
              >分/</span
            >
            <span class="text-va-grey-text-2">{{ item.score }}分</span>
          </span>
        </div>
        <div
          v-show="showModifyBtn && [1, 2].includes(item.answerResult as number)"
          class="modify-btn"
          :class="[
            {
              'text-va-primary-text': item.answerResult === 1,
              'text-va-text-grey': item.answerResult !== 1
            }
          ]"
          @click="handleModify(item.answerResult === 1)"
        >
          <span class="text-[14px]">{{ item.answerResult === 1 ? '去订正' : '已订正' }}</span>
          <i
            :class="[
              'iconfont text-[16px]',
              {
                iconqudingzhengjiantou: item.answerResult === 1,
                iconyidingzhengjiantou: item.answerResult === 2
              }
            ]"
          ></i>
        </div>
      </div>
      <div class="my-answer flex items-center text-[14px] text-va-grey-text pl-[10px]">
        <span class="font-bold mr-[10px]">我的作答:</span>
        <span
          v-show="[1, 6].includes(item.quizType as number) || !item.studentAnswerList?.length"
          >{{ item.studentAnswerList?.join('、') || '暂无' }}</span
        >
      </div>
      <div v-if="![1, 6].includes(item.quizType as number)" class="mt-[10px]">
        <div
          class="rounded-[10px] bg-white border border-va-grey-text h-[88px] mb-[10px] overflow-hidden"
          v-for="(v, answerIdx) in item.studentAnswerList"
          :key="answerIdx"
        >
          <img :src="v" class="w-full h-full object-cover" />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
