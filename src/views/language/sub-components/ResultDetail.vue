<script setup lang="ts">
import { onBeforeMount, ref, computed } from 'vue'
import { useGetComp } from '@/hooks'
import SubResultSummary from './SubResultSummary.vue'
import { formatNumTimes } from '@/utils/num'
const props = withDefaults(
  defineProps<{
    quizs?: any[]
    sec?: number
    rightQty?: number
    totalQty?: number
    courseName?: string
    accuracyRate?: number
  }>(),
  { quizs: () => [], sec: 0, courseName: '', rightQty: 0, totalQty: 0, accuracyRate: 0 }
)

const currentRate = computed(() => {
  return formatNumTimes(props.accuracyRate || 0)
})
</script>
<template>
  <div class="result-detail bg-bg-grey">
    <SubResultSummary
      :info="{
        courseName,
        time: sec,
        rightQty,
        totalQty,
        currentRate
      }"
    />
    <div class="quizs-list">
      <div class="quiz-item mb-[12px]" v-for="(item, index) in quizs" :key="index">
        <component
          :is="useGetComp(item.quizType, false, false)"
          :title="'Question' + (index + 1)"
          :info="item"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>
