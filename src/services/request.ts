import axios, { type AxiosResponse, type Method, type AxiosRequestConfig } from 'axios'
import $config from '@/config'
import { useLocalStorage } from '@vueuse/core'

const SUCCESS_CODE_LIST = ['0000', 200, '200', 0, '0', 'success']
const CODE_SERVER_ERROR = ['9999', '500', '503']

const requestInstance = axios.create()

const toLogin = function (): void {
  // TODO app重新授权
}

const inWhiteList = function (url: string): boolean {
  return !!$config?.whiteList?.some((item) => url.indexOf(item) > -1)
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const checkResponseCode = function (res: AxiosResponse): any {
  const { data } = res
  if (!data) return res

  if (SUCCESS_CODE_LIST.includes(data.code)) {
    return data
  } else {
    const { code } = data
    if (CODE_SERVER_ERROR.includes(code)) {
      showNotify({ type: 'warning', message: '网络繁忙, 请稍等片刻' })
    }
    return data
  }
}

// axios.defaults.withCredentials = true

requestInstance.interceptors.request.use(
  (config) => {
    // #是否需要鉴权
    const needToken = !inWhiteList(config.url || '')

    // 拼接请求地址
    config.url = `${$config.clientApiHost.replace(/\/$/, '')}${config.url}`
    const { value: appInfo } = useLocalStorage('appInfo', { TEMPLATE_USER_ACCOUNT: '' })
    const token = appInfo.TEMPLATE_USER_ACCOUNT

    config.headers = Object.assign({}, config.headers, {
      [$config.studentTokenKey]: token || $config.fakeToken
    })
    return config
  },
  (err) => Promise.reject(err)
)

requestInstance.interceptors.response.use(
  (response) => checkResponseCode(response),
  (err) => {
    showNotify({ type: 'warning', message: '网络繁忙, 请稍等片刻' })
    return Promise.reject(err)
  }
)

export const Abort = axios.CancelToken

export default {
  async run<T, V>(
    url: string,
    method: Method = 'POST',
    payload?: T,
    axiosConfig?: AxiosRequestConfig
  ): Promise<V> {
    const cfg: AxiosRequestConfig = Object.assign(
      {},
      {
        method,
        url
      },
      axiosConfig || {}
    )
    if (payload) {
      if (['POST', 'PUT'].includes(method)) {
        cfg['data'] = payload
      } else {
        // restfull api
        // support: https://domain/api/apiname/{par1}/{par2}?par3=x&par4=y
        const reg = /({[A-Za-zd_-]+})/g
        const mapping = payload as unknown as any
        url.match(reg)?.forEach((parTemplate: string) => {
          const paramName = parTemplate.replace(/[{}]/g, '')
          const param = mapping[paramName] || ''
          cfg.url = cfg.url?.replace(parTemplate, param)
          delete mapping[paramName]
        })
        cfg['params'] = mapping
      }
    }
    const ret = await requestInstance(cfg)

    return ret as unknown as V
  }
}
