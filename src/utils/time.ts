export function getHMSwithSeconds(seconds: number = 0): [number, number, number] {
  const hours = Math.floor(seconds / 3600)
  const remainderSeconds = seconds % 3600
  const minutes = Math.floor(remainderSeconds / 60)
  const remainingSeconds = remainderSeconds % 60

  return [hours, minutes, remainingSeconds]
}

export function formatTime(sec?: number, needHour: boolean = true) {
  const [h, m, s] = getHMSwithSeconds(sec || 0)

  const hStr = (h + '').padStart(2, '0')
  const mStr = ((needHour ? m : m + 60 * h) + '').padStart(2, '0')
  const sStr = parseInt(s + '')
    .toString()
    .padStart(2, '0')
  return needHour ? `${hStr}:${mStr}:${sStr}` : `${mStr}:${sStr}`
}
