import { ref, onBeforeUnmount, createVNode, render } from 'vue'
import { driver, type AllowedButtons, type Config, type Driver, type DriveStep } from 'driver.js'
import 'driver.js/dist/driver.css'
import GuideContent from '@/components/guide-content/index.vue'

interface GuideConfig {
  steps?: DriveStep[]
  showButtons?: AllowedButtons[]
  showProgress?: boolean
  closeBtnText?: string
  nextBtnText?: string
  prevBtnText?: string
  doneBtnText?: string
  popoverClass?: string
  autoStart?: boolean
  closeCb?: () => void
}

export function useGuide(config: GuideConfig) {
  const driverObj = ref<any>(null)

  const initDriver = (extraConfig: GuideConfig = {}) => {
    const allConfig = JSON.parse(JSON.stringify({ ...config, ...extraConfig }))
    driverObj.value = driver({
      animate: true,
      showButtons: allConfig.showButtons,
      showProgress: allConfig.showProgress,
      allowClose: false,
      smoothScroll: true,
      stagePadding: 0,
      overlayColor: 'rgba(0, 0, 0, 0.4)',
      disableActiveInteraction: false,
      popoverClass: allConfig.popoverClass || 'guide-popover',
      steps: allConfig?.steps?.map((step: DriveStep) => ({
        element: step.element,
        popover: {
          ...step.popover,
          doneBtnText: allConfig.doneBtnText,
          closeBtnText: allConfig.closeBtnText,
          nextBtnText: allConfig.nextBtnText,
          prevBtnText: allConfig.prevBtnText
        }
      }))
    })
  }

  const startGuide = () => {
    if (!driverObj?.value) {
      initDriver()
    }
    driverObj.value.drive()
  }

  const openPracticeGuide = (config?: GuideConfig) => {
    if (!driverObj?.value) {
      initDriver(config)
    }
    driverObj.value.highlight({
      element: '.drag-handler-inner',
      popover: {
        description: ' ',
        side: 'top',
        align: 'center',
        onPopoverRender(popover: any, { driver }: { driver: Driver }) {
          // 创建一个容器元素
          const container = document.createElement('div')
          popover.description.innerHTML = ''
          popover.description.appendChild(container)

          // 创建并渲染 Vue 组件
          const vnode = createVNode(GuideContent, {
            onClose: () => {
              driver.destroy()
              config?.closeCb?.()
            }
          })
          render(vnode, container)
        }
      }
    })
  }

  const stopGuide = () => {
    if (driverObj.value) {
      driverObj.value.destroy()
    }
  }

  onBeforeUnmount(() => {
    stopGuide()
  })

  return {
    openPracticeGuide,
    startGuide,
    stopGuide
  }
}
