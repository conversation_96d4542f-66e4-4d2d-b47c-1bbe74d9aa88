<script setup lang="ts">
import type { PickerOption } from 'vant/lib/picker/types'
import { ref, watch } from 'vue'
defineOptions({
  name: 'SettingCountdown'
})
export interface Props {
  minHour?: number
  minMinute?: number
  title?: string
  maxHour?: number
  maxMinute?: number
  show?: boolean
  modelValue?: string[]
}
const props = withDefaults(defineProps<Props>(), {
  minHour: 0,
  minMinute: 0,
  maxHour: 23,
  maxMinute: 59,
  title: '设置倒计时',
  show: false,
  modelValue: () => []
})

const emit = defineEmits(['update:show', 'update:modelValue', 'confirm'])

const currentTime = ref(props.modelValue)

watch(
  () => props.modelValue,
  (val) => {
    currentTime.value = val
  }
)

const formatter = (type: string, option: PickerOption) => {
  if (type === 'hour') {
    option.text += '时'
  }
  if (type === 'minute') {
    option.text += '分'
  }
  return option
}

const onConfirm = () => {
  emit('update:modelValue', currentTime.value)
  emit('update:show', false)
  emit('confirm', currentTime.value)
}

const filter = (type: string, options: PickerOption[]) => {
  if (type === 'minute') {
    return options.filter((option) => Number(option.value) % 5 === 0)
  }
  return options
}

const onCancel = () => {
  emit('update:show', false)
}
</script>
<template>
  <van-dialog
    :show="props.show"
    @update:show="(val) => emit('update:show', val)"
    :title="props.title"
    :show-cancel-button="true"
    confirm-button-color="#0256ff"
    :style="{ width: '80%' }"
    class="count-down-dialog"
    @confirm="onConfirm"
    @cancel="onCancel"
  >
    <van-time-picker
      v-model="currentTime"
      :min-hour="props.minHour"
      :min-minute="props.minMinute"
      :max-hour="props.maxHour"
      :max-minute="props.maxMinute"
      :show-toolbar="false"
      :formatter="formatter"
      :filter="filter"
      class="pt-[26px]"
    />
  </van-dialog>
</template>
<style lang="scss" scoped>
:deep(.van-picker-column__item--selected) {
  color: #0256ff;
  font-weight: 600;
  font-size: 28px;
}
</style>
<style lang="scss">
.count-down-dialog {
  .van-dialog__footer {
    font-weight: 600;
  }
}
</style>
