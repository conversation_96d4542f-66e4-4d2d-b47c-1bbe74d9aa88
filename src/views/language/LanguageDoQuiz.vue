<script setup lang="ts">
import { onBeforeMount, computed, type ComponentInstance } from 'vue'
import { useRoute } from 'vue-router'

import DoListening from './components/DoListening.vue'
import DoSpeaking from './components/DoSpeaking.vue'
import DoWriting from './components/DoWriting.vue'
import DoReading from './components/DoReading.vue'
import type { LanguageType } from './config'

const routes = useRoute()

const finalComp = computed(() => {
  const compMap: { [k in LanguageType]: ComponentInstance<any> } = {
    Speaking: DoSpeaking,
    Reading: DoReading,
    Listening: DoListening,
    Writing: DoWriting
  }
  const key = routes.query.languageType as LanguageType
  return compMap[key]
})

onBeforeMount(() => {
  if (routes.query?.title) {
    document.title = routes.query?.title as string
  }
})
</script>
<template>
  <div class="quiz-wrap h-[100%]">
    <component :is="finalComp" />
  </div>
</template>

<style lang="scss" scoped></style>
