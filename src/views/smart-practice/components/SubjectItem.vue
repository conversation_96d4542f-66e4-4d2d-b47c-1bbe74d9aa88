<script setup lang="ts">
defineProps<{
  subject: {
    name: string
    updated?: string
    selected?: boolean
  }
}>()

defineEmits<{
  (e: 'select'): void
}>()
</script>

<template>
  <div
    class="min-w-[120px] flex-none flex items-center justify-center rounded-lg border px-[8px] h-[36px]"
    :class="[
      subject.selected
        ? 'border-[#326EFF] text-[#326EFF] bg-[#F5F8FF]'
        : 'border-[#EAECF0] text-[#333] bg-white'
    ]"
    @click="$emit('select')"
  >
    <span class="text-[12px]">
      {{ subject.name }}
    </span>
  </div>
</template>
