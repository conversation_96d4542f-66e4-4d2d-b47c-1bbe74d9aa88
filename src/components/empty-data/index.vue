<script setup lang="ts">
defineOptions({
  name: 'EmptyData'
})
import { getImageUrl } from '@/utils/img'

const props = withDefaults(defineProps<{ title?: string; desc?: string }>(), {
  title: '你太牛了，当前没有错题',
  desc: '继续刷题，查漏补缺吧！'
})
</script>
<template>
  <div class="rounded-[12px] p-[16px] mb-[16px] pt-[20vh] flex flex-col items-center">
    <img :src="getImageUrl('practice/img_empty.png')" class="w-[120px] h-[120px]" />
    <div class="text-center mt-[16px]">
      <p class="text-[18px] mt-[20px] text-[#3C4258] font-[600]">{{ props.title }}</p>
      <p class="text-[16px] mt-[8px] text-[#6B7186]">{{ props.desc }}</p>
    </div>
  </div>
</template>
<style scoped></style>
