<script setup lang="ts">
import { ref, onMounted, computed, onBeforeUnmount, onBeforeMount, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showConfirmDialog } from 'vant'
import SettingCountdown from '@/components/setting-countdown/index.vue'
import CountDown from '@/components/setting-countdown/CountDown.vue'
import AnswerCard from './sub-do-practice/AnswerCard.vue'
import { useGuide } from '@/hooks/useGuide'
import DragPopup from '@/components/drag-popup/index.vue'
import CommonNav from '@/components/common-nav/index.vue'
import { useGlobal } from '@/hooks/useGlobal'
import PracticeMainQuizContent from '@/components/practice-quiz-temp/PracticeMainQuizContent.vue'
import MulUploadPhotos from '@/components/upload-photo/MulUploadPhotos.vue'
import VaMulSelectBtn from '@/components/base-ui/VaMulSelectBtn.vue'

import { useUserInfoStore } from '@/stores'
import { storeToRefs } from 'pinia'
import { useLoading, usePhotoCount, useRegWindowMethod } from '@/hooks'
import type {
  ListVolQuizzesReqVO,
  PracticeRecordBaseReqDTO,
  PracticeStartReqDTO,
  PracticeSubmitReqDTO,
  QuizSubmitAnswerReqDTO,
  TVolQuizOutData
} from '@/services/practice/types'
import { TransferToFlutter, TransferType } from '@/utils/msg'
import type { QuizAnswerMapType, DoQuizItemType } from './types/dopractice'
import dayjs from 'dayjs'
import { SmartPracticePageNames } from '@/router/modules/practice'
import config from '@/config'
import { buriedPointsMap } from '@/model/buried-point'

const { startLoading, endLoading, loading } = useLoading()
const router = useRouter()

const userInfoStore = useUserInfoStore()
const { isFirstDoPracticeGuide } = storeToRefs(userInfoStore)
const { globalProperties } = useGlobal()
// 使用照片数量管理 hook
const { restPhotoCountLimit, setCurrentPhotoCount } = usePhotoCount(9)

const timer = ref<ReturnType<typeof setTimeout> | undefined>()
const currentTime = ref(['01', '30'])
const showCountdown = ref(false)
const startCountdown = ref(false)
const showAnswerCard = ref(false)
const currentDragHeight = ref(0)

const currentPracticeRecordUid = ref('') // 当前刷题记录uid
const currentVolName = ref('')
const quizAnswersMap = ref<QuizAnswerMapType>({}) // 答题对象
const currentStartTime = ref(dayjs().format('YYYY-MM-DD HH:mm:ss'))
const currentQuizAnswers = ref([])

const { openPracticeGuide } = useGuide({})

const quizs = ref<TVolQuizOutData[]>([])
const currentQuizIdx = ref(0)
const currentUploadQuizUid = ref('')

const countDownSeconds = computed(() => {
  return Number(currentTime.value[0]) * 60 * 60 + Number(currentTime.value[1]) * 60
})

const navTitle = computed(() => {
  if (!quizs?.value?.length) {
    return ''
  }
  return `${currentQuizIdx.value + 1}/${quizs?.value?.length}`
})

const paddingTop = computed(() => {
  const appInfoStr = localStorage.getItem(config.appInfoKey)
  if (!appInfoStr) return '44px'
  const appInfo = JSON.parse(appInfoStr)
  return `${Number(appInfo.safeAreaTop) + 44}px`
})

const routeQueryInfo = computed(() => {
  const route = useRoute()
  return route?.query
})

const isLastQuiz = computed(() => {
  if (!quizs?.value?.length) {
    return false
  }
  return currentQuizIdx.value + 1 === quizs?.value?.length
})

const currentQuiz = computed(() => {
  return quizs.value[currentQuizIdx.value]
})

// 当前答题对象
const quizAnswerList = computed(() => {
  if (!currentQuiz.value) return []
  const result: DoQuizItemType[] = []

  const processQuiz = (quiz: TVolQuizOutData) => {
    if (quiz.quizType === 3 && quiz.children?.length) {
      quiz.children.forEach((childQuiz) => {
        processQuiz(childQuiz)
      })
    } else {
      result.push({
        quizSeqNo: quiz.quizSeq || quiz.quizSeqNo,
        quizUid: quiz.uid as string,
        quizType: quiz.quizType,
        quizVersion: quiz.objectVersion,
        userAnswerList: quizAnswersMap.value[quiz.uid as string]?.userAnswerList || [],
        score: quiz.score,
        options: quiz.options?.map((v) => ({ label: v.aoVal, value: v.aoVal }))
      })
    }
  }

  processQuiz(currentQuiz.value)
  return result
})

const checkQuizAnswerStatus = (quiz: TVolQuizOutData): { hasAnswer: boolean; isHalf: boolean } => {
  if (quiz.quizType === 3 && quiz.children?.length) {
    const childrenStatus = quiz.children.map((child) => checkQuizAnswerStatus(child))
    const hasAnsweredChild = childrenStatus.some((status) => status.hasAnswer || status.isHalf)
    const allChildrenAnswered = childrenStatus.every((status) => status.hasAnswer)
    return {
      hasAnswer: allChildrenAnswered,
      isHalf: hasAnsweredChild && !allChildrenAnswered
    }
  }
  return {
    hasAnswer: !!quizAnswersMap.value[quiz.uid as string]?.userAnswerList?.length,
    isHalf: false
  }
}

const allQuizAnswerList = computed(() => {
  if (!quizs.value?.length) return []
  return quizs.value.map((quiz, index) => {
    const status = checkQuizAnswerStatus(quiz)
    return {
      index: index + 1,
      quizUid: quiz.uid || '', // Ensure quizUid is always a string
      hasAnswer: status.hasAnswer,
      isHalf: status.isHalf
    }
  })
})

const generateQuizAnswerObject = (quiz: TVolQuizOutData) => {
  const result: Record<string, any> = {}
  const processQuiz = (currentQuiz: TVolQuizOutData) => {
    if (currentQuiz.quizType === 3 && currentQuiz.children?.length) {
      currentQuiz.children.forEach((childQuiz) => {
        processQuiz(childQuiz)
      })
    } else {
      result[currentQuiz.uid as string] = {
        quizType: currentQuiz.quizType,
        quizUid: currentQuiz.uid,
        quizVersion: currentQuiz.objectVersion,
        userAnswerList: (currentQuiz as any)?.studentAnswerList || []
      }
    }
  }

  processQuiz(quiz)
  return result
}

const getQuizs = async () => {
  try {
    if ([1, '1'].includes(routeQueryInfo.value?.practiceState as string)) {
      const { data } =
        await globalProperties.$http.Practice.VolQuizManagerController.getVolAnswerDetailList({
          practiceRecordUid: (routeQueryInfo.value?.practiceRecordUid as string) || ''
        })
      quizs.value = data?.quizList || []
      // 生成答题对象  TODO 所有的答题对象 数组 回显
      quizAnswersMap.value = quizs.value?.reduce<QuizAnswerMapType>((acc, quiz) => {
        return { ...acc, ...generateQuizAnswerObject(quiz) }
      }, {})
      currentVolName.value = data?.volName || ''
    } else {
      const params: ListVolQuizzesReqVO = {
        volUid: routeQueryInfo.value?.paperUid as string,
        volVersion: routeQueryInfo.value?.paperVersion as unknown as number
      }
      const { data } =
        await globalProperties.$http.Practice.VolQuizManagerController.getVolQuizList(params)
      quizs.value = data?.quizs || []
      // 生成答题对象
      quizAnswersMap.value = quizs.value?.reduce<QuizAnswerMapType>((acc, quiz) => {
        return { ...acc, ...generateQuizAnswerObject(quiz) }
      }, {})

      currentVolName.value = data?.volName || ''
    }
    return Promise.resolve()
  } catch (error) {
    return Promise.reject(error)
  }
}

const initActions = () => {
  if (isFirstDoPracticeGuide.value) {
    openPracticeGuide({
      popoverClass: 'practice-guide-popover',
      closeCb: () => {
        userInfoStore.updateFirstDoPracticeGuide(false)
        showCountdown.value = true
      }
    })
  } else {
    showCountdown.value = true
  }
}

const selectPic = (quizUid?: string) => {
  try {
    // 使用项目中已有的通信工具
    const picCount = (quizAnswersMap.value[quizUid as string]?.userAnswerList || []).length
    setCurrentPhotoCount(picCount)

    if (restPhotoCountLimit.value <= 0) {
      showToast('已达到最大上传数量')
      return
    }

    currentUploadQuizUid.value = quizUid || ''
    TransferToFlutter({
      type: TransferType.getPhotoListCrop,
      data: {
        title: '选择图片',
        url: '',
        max: restPhotoCountLimit.value
      }
    })
    currentUploadQuizUid.value = quizUid || ''

    // 显示提示信息
    showToast('已请求打开相册')
  } catch (error) {
    showToast('调用相册功能失败')
  }
}
const removePic = (quizUid: string, idx?: number) => {
  const userAnswerList = quizAnswersMap.value[quizUid]?.userAnswerList || []
  if (idx === undefined) {
    return
  }
  userAnswerList.splice(idx, 1)
  quizAnswersMap.value[quizUid as string] = {
    ...(quizAnswersMap.value[quizUid as string] || {}),
    userAnswerList
  }
}

const getPhotoInfos = (allFile: any) => {
  const preUserAnswerList = quizAnswersMap.value[currentUploadQuizUid.value]?.userAnswerList || []
  const picList = typeof allFile?.url === 'string' ? [allFile?.url] : [...(allFile as string[])]
  const userAnswerList = preUserAnswerList.concat(picList)
  quizAnswersMap.value[currentUploadQuizUid.value] = {
    ...quizAnswersMap.value[currentUploadQuizUid.value],
    userAnswerList
  }
}

const updateChooseAnswer = (params: any) => {
  quizAnswersMap.value[params.quizUid] = {
    ...quizAnswersMap.value[params.quizUid],
    userAnswerList: params.value
  }
}

const handleShowAnswerCard = () => {
  showAnswerCard.value = true
}

const confirmCountDown = () => {
  startCountdown.value = true
}

const handleBack = () => {
  const hasHalfAnsweredQuizzes = allQuizAnswerList.value.some((quiz) => quiz.isHalf)
  const hasAnsweredQuizzes = allQuizAnswerList.value.some((quiz) => quiz.hasAnswer)
  if (hasAnsweredQuizzes || hasHalfAnsweredQuizzes) {
    showAnswerCard.value = true
  } else {
    if (currentPracticeRecordUid.value) {
      removePractice()
    }
    router.go(-1)
  }
}

const removePractice = async () => {
  const params: PracticeRecordBaseReqDTO = {
    practiceRecordUid: currentPracticeRecordUid.value as string
  }
  await globalProperties.$http.Practice.QuizAnswerProcessController.deletePractice(params)
}

const handlePrevQuiz = () => {
  if (loading.value) {
    return
  }
  // 检查答案是否有变化
  submitQuiz()
  currentQuizIdx.value--
  currentStartTime.value = dayjs().format('YYYY-MM-DD HH:mm:ss')
  currentQuizAnswers.value = JSON.parse(JSON.stringify(quizAnswerList.value))
}

const handleNextQuiz = async () => {
  await submitQuiz()
  if (isLastQuiz.value) {
    if (loading.value) {
      showToast('正在提交中，请稍等片刻')
      return
    }

    // 检查是否有未完成的题目
    const unfinishedQuizzes = allQuizAnswerList.value.filter((quiz) => !quiz.hasAnswer)
    const hasHalfAnsweredQuizzes = allQuizAnswerList.value.some((quiz) => quiz.isHalf)

    if (unfinishedQuizzes.length > 0 || hasHalfAnsweredQuizzes) {
      try {
        await showConfirmDialog({
          title: '提示',
          message: `还有题目未提交答案，确认交卷吗？`,
          confirmButtonText: '确认提交试卷',
          cancelButtonText: '继续作答',
          confirmButtonColor: '#326eff'
        })
      } catch {
        return
      }
    }
    submitTestPaper()
  } else {
    currentQuizIdx.value++
    currentQuizAnswers.value = JSON.parse(JSON.stringify(quizAnswerList.value))
    currentStartTime.value = dayjs().format('YYYY-MM-DD HH:mm:ss')
  }
}

const startPractice = async () => {
  const isInProgress = [1, '1'].includes(routeQueryInfo.value?.practiceState as string)
  const params: PracticeStartReqDTO = {
    practiceRecordUid: isInProgress
      ? (routeQueryInfo.value?.practiceRecordUid as string)
      : undefined,
    paperVersion: routeQueryInfo.value?.paperVersion as unknown as number,
    paperUid: (routeQueryInfo.value?.paperUid as string) || ''
  }
  const { data } =
    await globalProperties.$http.Practice.QuizAnswerProcessController.startPractice(params)
  currentPracticeRecordUid.value = data?.practiceRecordUid as string
  if (!data) {
    return Promise.reject(new Error('practice record is empty'))
  }
  if (isInProgress) {
    currentQuizIdx.value = data?.beforeOffsetQuizSeq ? data?.beforeOffsetQuizSeq - 1 : 0
  }
  return data
}

const submitQuiz = async () => {
  const isChanged =
    JSON.stringify(currentQuizAnswers.value) !== JSON.stringify(quizAnswerList.value)
  if (!isChanged) {
    return
  }
  const { quizType } = currentQuiz.value
  const params: QuizSubmitAnswerReqDTO = {
    quizSeq: currentQuiz.value.quizSeq,
    practiceRecordUid: currentPracticeRecordUid.value as string,
    startTime: currentStartTime.value,
    submitTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    quizUid: currentQuiz.value.uid,
    quizVersion: currentQuiz.value.objectVersion,
    userAnswerList: quizType === 3 ? undefined : quizAnswerList.value?.[0]?.userAnswerList,
    userQuizAnswerDetailList:
      quizType === 3
        ? quizAnswerList.value.map((v) => ({
            quizUid: v.quizUid,
            quizVersion: v.quizVersion,
            userAnswerList: v.userAnswerList
          }))
        : undefined
  }
  try {
    startLoading()
    const res =
      await globalProperties.$http.Practice.QuizAnswerProcessController.submitQuizAnswer(params)
    if (res.code === 'success') {
      return Promise.resolve()
    } else {
      return Promise.reject(res.message)
    }
  } finally {
    endLoading()
  }
}

const handleSubmitPractice = async (needConfirm = true) => {
  if (loading.value) {
    showToast('正在提交中，请稍等片刻')
    return
  }

  // 检查是否有未完成的题目
  const unfinishedQuizzes = allQuizAnswerList.value.filter((quiz) => !quiz.hasAnswer)
  const hasHalfAnsweredQuizzes = allQuizAnswerList.value.some((quiz) => quiz.isHalf)

  if ((unfinishedQuizzes.length > 0 || hasHalfAnsweredQuizzes) && needConfirm) {
    try {
      await showConfirmDialog({
        title: '提示',
        message: `还有题目未提交答案，确认交卷吗？`,
        confirmButtonText: '确认提交试卷',
        cancelButtonText: '继续作答',
        confirmButtonColor: '#326eff'
      })
    } catch {
      return
    }
  }
  try {
    await submitQuiz()
    submitTestPaper()
  } catch (error) {
    console.log(error)
  }
}

const submitTestPaper = async () => {
  const params: PracticeSubmitReqDTO = {
    practiceRecordUid: currentPracticeRecordUid.value as string
  }
  try {
    startLoading()
    const res =
      await globalProperties.$http.Practice.QuizAnswerProcessController.submitPractice(params)
    if ([10001, '10001', 'success'].includes(res.code as any)) {
      showNotify({ type: 'success', message: '练习已提交, 请稍等片刻' })
      router.replace({
        name: SmartPracticePageNames.PracticeResult,
        query: {
          practiceRecordUid: currentPracticeRecordUid.value
        }
      })
    }
  } finally {
    endLoading()
  }
}

onBeforeUnmount(() => {
  if (timer.value) {
    clearTimeout(timer.value)
  }

  // 通知Flutter重置状态，避免影响其他页面的返回操作
  TransferToFlutter({
    type: TransferType.handlePopupBack,
    data: {
      title: 'PracticeDopractice',
      url: window.location.href,
      action: 'reset',
      status: 'close'
    }
  })
})

const handleSaveProgress = () => {
  submitQuiz().then(() => {
    router.go(-1)
    showNotify({ type: 'primary', message: '试卷已保存，可前往刷题记录中继续作答' })
  })
}

const handleToggleQuizItem = (index: number) => {
  submitQuiz()
  currentQuizIdx.value = index
  currentQuizAnswers.value = JSON.parse(JSON.stringify(quizAnswerList.value))
  currentStartTime.value = dayjs().format('YYYY-MM-DD HH:mm:ss')
}

// 监听倒计时弹窗状态变化
watch(
  () => showCountdown.value,
  (newVal) => {
    if (!newVal) {
      // 弹窗关闭时，通知Flutter执行handleBack逻辑
      TransferToFlutter({
        type: TransferType.handlePopupBack,
        data: {
          title: 'SettingCountdown',
          url: window.location.href,
          status: 'show'
        }
      })
    } else {
      // 弹窗打开时，通知Flutter
      TransferToFlutter({
        type: TransferType.handlePopupBack,
        data: {
          title: 'SettingCountdown',
          url: window.location.href,
          action: 'open'
        }
      })
    }
  }
)
const countDownEnd = () => {
  showConfirmDialog({
    title: '提示',
    message: '倒计时结束',
    confirmButtonText: '提交答案',
    cancelButtonText: '继续作答',
    confirmButtonColor: '#326eFF'
  }).then(() => {
    handleSubmitPractice(false)
  })
}

const toggleCountDown = () => {
  showCountdown.value = !showCountdown.value
}

onMounted(async () => {
  globalProperties.$aplusPush.pushEvent({ eventCode: buriedPointsMap.practice_dopractice })
  try {
    startLoading()
    await Promise.all([startPractice(), getQuizs()])
    initActions()
    currentQuizAnswers.value = JSON.parse(JSON.stringify(quizAnswerList.value))
    currentStartTime.value = dayjs().format('YYYY-MM-DD HH:mm:ss')
  } finally {
    endLoading()
  }
})

useRegWindowMethod([
  { methodName: 'backPageHandle', method: handleBack },
  { methodName: 'getPhotoListCrop', method: getPhotoInfos }
])
</script>
<template>
  <div class="bg-[#F3F5F8] min-h-[100vh]" :style="{ paddingTop }">
    <!-- 自定义导航 -->
    <common-nav title="" :show-background="true">
      <template #left>
        <div class="flex items-center">
          <van-icon name="arrow-left" @click="handleBack" />
          <CountDown
            :time="countDownSeconds"
            v-if="startCountdown"
            @end="countDownEnd"
            @count-down-click="toggleCountDown"
            class="text-va-grey-text text-[14px] font-bold ml-[28px]"
          />
        </div>
      </template>
      <template #right>
        <span
          class="iconfont icondatika1 text-va-grey-text text-[20px]"
          @click="handleShowAnswerCard"
        ></span>
      </template>
      <template #title>
        <span>{{ navTitle }}</span>
      </template>
    </common-nav>
    <!-- 题目展示 -->
    <div
      class="rounded-[10px] mx-[10px] bg-white quizs-container my-[20px]"
      :style="{ paddingBottom: `${currentDragHeight + 50}px` }"
    >
      <PracticeMainQuizContent :currentIndex="currentQuizIdx" :currentQuizInfo="currentQuiz" />
    </div>

    <AnswerCard
      v-model:show="showAnswerCard"
      :quiz-list="allQuizAnswerList"
      :volName="currentVolName"
      :currentQuizIdx="currentQuizIdx"
      @click-quiz-item="handleToggleQuizItem"
      @save-progress="handleSaveProgress"
      @submit-test="handleSubmitPractice"
    />
    <!-- 设置倒计时 -->
    <SettingCountdown
      v-model="currentTime"
      v-model:show="showCountdown"
      @confirm="confirmCountDown"
    />
    <!-- 倒计时 -->

    <DragPopup
      v-model="currentDragHeight"
      max-height="75vh"
      :isFirstDoPracticeGuide="isFirstDoPracticeGuide"
    >
      <template #content>
        <div class="op-wrap flex flex-col h-[100%]">
          <div class="do-quizs-op-wrap flex-1 p-[16px] overflow-y-auto">
            <div class="do-quiz-item mb-[20px]" v-for="item in quizAnswerList" :key="item.quizUid">
              <div class="mb-[6px] text-va-grey-text flex items-center">
                <span class="text-[14px] font-bold mr-[6px] align-middle">{{
                  item.quizSeqNo
                }}</span>
                <span class="text-va-grey-text text-[12px]">{{
                  item.score ? ` (${item.score}分) ` : ''
                }}</span>
              </div>
              <VaMulSelectBtn
                v-if="[1, 6].includes(item.quizType as number)"
                :options="item.options || []"
                :multiple="[6].includes(item.quizType as number)"
                :model-value="item.userAnswerList || []"
                @update:model-value="
                  (value) => updateChooseAnswer({ quizUid: item.quizUid, value })
                "
              />
              <MulUploadPhotos
                v-else
                class="mb-[6px]"
                :pics="item.userAnswerList || []"
                @select="selectPic(item.quizUid)"
                @remove="(idx) => removePic(item?.quizUid as string, idx)"
              />
            </div>
          </div>
          <div
            v-show="quizs?.length"
            class="btns-wrap border-t-[2px] border-[#E6E9F3] flex-shrink-0 flex justify-between px-[16px] py-[14px]"
          >
            <div class="flex items-center">
              <VaBaseButton
                :disabled="!currentQuizIdx"
                type="text"
                text="上一题"
                class="text-[14px] px-[32px] py-[8px]"
                @click="handlePrevQuiz"
              />
            </div>
            <div class="flex items-center">
              <VaBaseButton
                v-show="currentQuizIdx <= quizs.length - 1"
                :text="isLastQuiz ? '交卷' : '下一题'"
                class="text-[14px] px-[32px] py-[8px]"
                @click="handleNextQuiz"
              />
            </div>
          </div>
        </div>
      </template>
    </DragPopup>
  </div>
</template>

<style lang="scss" scoped>
.first-child {
  justify-content: flex-end;
}
</style>
<style lang="scss">
.practice-guide-popover {
  transform: translateY(-20px);
  .driver-popover-arrow {
    display: none;
  }
}
</style>
