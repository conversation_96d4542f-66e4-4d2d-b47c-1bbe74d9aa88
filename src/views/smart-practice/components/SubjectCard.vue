<script setup lang="ts">
interface Props {
  name: string
  icon?: string
  description?: string
  selected?: boolean
  practiceCount?: number
  correctCount?: number
  tags?: string[]
}

defineProps<Props>()

defineEmits<{
  (e: 'select'): void
  (e: 'practice'): void
  (e: 'correct'): void
}>()
</script>

<template>
  <div
    class="w-full bg-white rounded-[10px] p-4 mb-4 hover:bg-[#F5F8FF] transition-colors shadow-sm"
    :class="[selected ? 'border-[1px] border-[#326EFF]' : '']"
  >
    <div class="flex flex-col">
      <div class="flex items-center justify-between mb-2">
        <span class="text-[18px] font-medium">{{ name }}</span>
        <div v-if="correctCount" class="text-[14px] text-[#646B77]">
          待订正: <span class="text-[#326EFF]">{{ correctCount }}道</span>
        </div>
      </div>

      <div class="flex gap-2 mb-3" v-if="tags && tags.length">
        <span
          v-for="tag in tags"
          :key="tag"
          class="px-3 py-1 bg-[#F5F8FF] text-[#646B77] text-[14px] rounded-md"
        >
          {{ tag }}
        </span>
      </div>

      <div class="flex items-center justify-between">
        <div class="text-[14px] text-[#646B77]">
          已刷<span class="text-[#326EFF] mx-1">{{ practiceCount || 0 }}</span
          >套
        </div>
        <button
          class="px-6 py-2 bg-[#326EFF] text-white rounded-full text-[14px] hover:bg-[#4B80FF]"
          @click="$emit('practice')"
        >
          去刷题
        </button>
      </div>

      <div v-if="description" class="text-[12px] text-[#646B77] mt-2">
        {{ description }}
      </div>
    </div>
  </div>
</template>
