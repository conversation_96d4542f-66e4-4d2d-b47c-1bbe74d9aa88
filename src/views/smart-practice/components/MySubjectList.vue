<script setup lang="ts">
import { useGlobal } from '@/hooks/useGlobal'
import { useRouter } from 'vue-router'
import { SmartPracticePageNames } from '@/router/modules/practice'
import type { UserSubjectTagInfoData, UserSubjectTagRemoveReqDTO } from '@/services/practice'
import { showToast } from 'vant'
const { globalProperties } = useGlobal()
const props = defineProps<{
  subjects: UserSubjectTagInfoData[]
}>()

const emit = defineEmits<{
  (e: 'practice', subjectId: string | number | undefined): void
  (e: 'delete', subject: UserSubjectTagInfoData): void
}>()

const router = useRouter()

const handleDelete = async (subject: UserSubjectTagInfoData) => {
  try {
    await showConfirmDialog({
      title: '确认删除',
      message: `确定要删除${subject.subjectTitle}吗？`,
      confirmButtonText: '删除',
      confirmButtonColor: '#ee0a24'
    })
    // 用户点击了确认按钮
    await handleDeleteSubject(subject)
  } catch (e) {
    // 用户点击了取消按钮，不做任何操作
  }
}

// 删除科目
const handleDeleteSubject = async (subject: UserSubjectTagInfoData) => {
  try {
    const params: UserSubjectTagRemoveReqDTO = {
      userTagId: subject.userTagId
    }
    const { data } =
      await globalProperties.$http.Practice.UserManagerController.removeUserSubscribe(params)
    // if (data) {
    // 删除成功
    showToast('删除成功')
    emit('delete', subject)
    // }
  } catch (e) {
    // 用户点击了取消按钮，不做任何操作
  }
}

const goPractice = (subject: UserSubjectTagInfoData) => {
  // 确保subject.subject不为undefined
  if (subject.subject !== undefined) {
    // 跳转到刷题列表页面
    router.push({
      name: SmartPracticePageNames.PracticeQuizList,
      query: {
        exam: subject.exam,
        examTitle: subject.examTitle,
        examBoardTitle: subject.examBoardTitle,
        examBoard: subject.examBoard,
        subject: subject.subject,
        subjectTitle: subject.subjectTitle
      }
    })
  }
}

const goToWrongQuestions = (subject: any) => {
  router.push({
    name: 'PracticeWrongbk',
    query: {
      exam: subject.exam,
      examBoard: subject.examBoard,
      subject: subject.subject
    }
  })
}
</script>

<template>
  <van-swipe-cell
    v-for="subject in props.subjects"
    :key="subject.subject"
    class="mb-[12px] swipe-cell"
  >
    <div class="bg-white px-[16px] py-[10px] rounded-[10px]" @click="goPractice(subject)">
      <div class="flex justify-between items-end">
        <div class="text-[18px] font-[500] text-[#3C4258]">{{ subject.subjectTitle }}</div>
        <div
          class="text-[12px] text-[#646B77] flex items-center cursor-pointer"
          @click.stop="goToWrongQuestions(subject)"
        >
          待订正: <span class="text-[#326EFF] ml-[11px]">{{ subject.unRectifyCount || 0 }}道</span>
          <van-icon name="arrow" size="16" color="#C9CDD4" class="ml-[11px]" />
        </div>
      </div>

      <div class="flex mt-[6px]">
        <span
          class="bg-[#F4F6FA] text-[#8F94A8] text-[12px] rounded-[4px] px-[4px] h-[20px] flex items-center justify-center"
        >
          {{ subject.examTitle }}
        </span>
        <span
          class="bg-[#F4F6FA] text-[#8F94A8] text-[12px] rounded-[4px] px-[4px] h-[20px] flex items-center justify-center ml-[4px]"
        >
          {{ subject.examBoardTitle }}
        </span>
      </div>

      <div class="flex justify-between items-end mt-2 mb-[10px]">
        <div class="text-[14px] text-[#646B77]">
          <span class="text-[#8F94A8] text-[11px]"
            >已刷{{ subject.practicePaperCount || 0 }}套</span
          >
        </div>
        <VaBaseButton
          style="font-size: 13px"
          text="去刷题"
          type="primary"
          @click="goPractice(subject)"
        />
      </div>
    </div>
    <template #right>
      <div class="delete-btn" @click="handleDelete(subject)">删除</div>
    </template>
  </van-swipe-cell>
</template>

<style scoped>
.delete-btn {
  height: 100%;
  width: 65px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ee0a24;
  font-size: 14px;
  border-radius: 0 10px 10px 0;
}

.swipe-cell {
  background-color: rgba(249, 113, 113, 0.2);
  border-radius: 10px;
}
</style>
