<script setup lang="ts">
// import reportAnimation from '@/assets/animation/report.json'
import AstronautJSON from '@/assets/animation/report.json'
import config from '@/config'
import { useGlobal } from '@/hooks'
import { SmartPracticePageNames } from '@/router/modules/practice'
import type {
  PracticeAnswerResultItem,
  PracticeRecordBaseReqDTO,
  UserPracticeReportCalcReqDTO
} from '@/services/practice'
import { getImageUrl } from '@/utils/img'
import { computed, onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import MarkdowRender from '@/components/markdown-render/index.vue'
import TestInfoCard from './components/TestInfoCard.vue'
import { buriedPointsMap } from '@/model/buried-point'
import { useDeviceStore } from '@/stores'
import { TransferToFlutter, TransferType } from '@/utils/msg'
const router = useRouter()
const { globalProperties } = useGlobal()
const deviceStore = useDeviceStore()

// 下拉刷新状态
const refreshing = ref(false)

// 累计刷题套数
const practiceCount = ref(0)
// 累计刷题天数
const practiceDays = ref(0)
// 用时（分钟）
const usedMinutes = ref('')
// 科目名称
const volName = ref('')
// 正确率
const currentRate = ref('')
// 总分
const totalScore = ref('')
// 学生得分
const studentScore = ref('')
// 错题数量
const wrongCount = ref(0)
// 试卷解析结果
const paperAnalysis = ref<string>('')
// 试卷的exam、examBoard、subject
const exam = ref(0)
const examTitle = ref('')
const examBoard = ref(0)
const examBoardTitle = ref('')
const subject = ref(0)
const subjectTitle = ref('')

// 存储题目状态列表
const questionStatusList = ref<PracticeAnswerResultItem[]>([])

// 显示静态图片
const showAnimation = ref(false)

// 处理下拉刷新
const onRefresh = async () => {
  try {
    refreshing.value = true
    await Promise.all([initData(), initQuestionStatus()])
  } finally {
    refreshing.value = false
  }
}

// 获取题目状态样式
const getQuestionStatusClass = (item: PracticeAnswerResultItem) => {
  // 使用API返回的数据判断题目状态

  //answerState===3 未作答
  if (item.answerState === 2 || item.answerState === 3) {
    return 'bg-white border-[1.5px] border-[#E9EAEE] text-[#3C4258]' // 未作答
  }

  if (item.answerResult === 2) {
    // 背景色#4CC775 无边框 rgba(76, 199, 117, 0.2)
    return 'bg-[#4CC775]/20 text-[#0CA840]' // 正确
  }
  if (item.answerResult === 1) {
    // 背景色#F97171 无边框 rgba(249, 113, 113, 0.2)
    return 'bg-[#F97171]/20 text-[#EB5858]' // 错误
  }

  return 'bg-[#F5F7FA] text-[#333]' // 其他状态
}

// 检查是否需要打开Flutter界面 - 题目详情
const checkAndOpenFlutterInterfaceForDetail = async (params: {
  practiceRecordUid: string
  pageNumber: number
}) => {
  console.log('deviceStore.deviceType', deviceStore.deviceType)

  if (deviceStore.deviceType === 'pad') {
    try {
      // 构建题目详情的URL参数
      let url = 'PracticeDetail?'
      const urlParams = new URLSearchParams()

      // 添加基础参数
      urlParams.append('practiceRecordUid', params.practiceRecordUid)
      urlParams.append('pageNumber', String(params.pageNumber))

      url += urlParams.toString()

      TransferToFlutter({
        type: TransferType.jump,
        data: {
          title: '',
          url: url
        }
      })
      return true // 已经打开Flutter界面
    } catch (error) {
      console.error('发送消息给Flutter失败:', error)
      // 如果发送失败，继续使用当前逻辑
      return false
    }
  }
  return false // 不是pad设备，使用当前逻辑
}

// 处理题目点击事件，跳转到答题详情页面
const handleQuestionClick = async (item: PracticeAnswerResultItem, index: number) => {
  // 检查是否需要打开Flutter界面
  const params = {
    practiceRecordUid: router.currentRoute.value.query.practiceRecordUid as string,
    pageNumber: index + 1
  }

  const openedFlutter = await checkAndOpenFlutterInterfaceForDetail(params)
  if (openedFlutter) {
    return // 已经打开Flutter界面，不需要继续执行
  }

  // 跳转到答题详情页面，传递必要的参数
  router.push({
    name: SmartPracticePageNames.PracticeDetail,
    query: params
  })
}

// 查询试卷解析结果接口
const getPaperAnalysis = async () => {
  const params: PracticeRecordBaseReqDTO = {
    practiceRecordUid: router.currentRoute.value.query.practiceRecordUid as string
  }
  const { data } =
    await globalProperties.$http.Practice.PracticeResultQueryController.queryResultAnalysis(params)
  if (data) {
    paperAnalysis.value = data.analysisData || ''
  }
}

// 初始化数据
const initData = async () => {
  // 获取练习报告数据
  const params: UserPracticeReportCalcReqDTO = {
    practiceRecordUid: router.currentRoute.value.query.practiceRecordUid as string
  }

  const { data } =
    await globalProperties.$http.Practice.PracticeResultQueryController.calcUserPracticeReport(
      params
    )
  if (data) {
    practiceCount.value = data.practiceVolCount || 0
    practiceDays.value = data.practiceDays || 0
    usedMinutes.value = data.timeLength || ''
  }

  // 获取试卷解析结果
  await getPaperAnalysis()
}

// 查询试卷回答结果每题是否答对答错
const initQuestionStatus = async () => {
  const params: PracticeRecordBaseReqDTO = {
    practiceRecordUid: router.currentRoute.value.query.practiceRecordUid as string
  }
  const { data } =
    await globalProperties.$http.Practice.PracticeResultQueryController.queryAnswerResult(params)
  if (data) {
    exam.value = data.exam || 0
    examTitle.value = data.examTitle || ''
    examBoard.value = data.examBoard || 0
    examBoardTitle.value = data.examBoardTitle || ''
    subject.value = data.subject || 0
    subjectTitle.value = data.subjectTitle || ''
    // 保存题目状态列表
    questionStatusList.value = data.practiceAnswerResultItemList || []

    // 设置正确率
    currentRate.value = data.rightRate || '0'
    // 总分
    totalScore.value = data.paperScore || '0'
    // 学生得分
    studentScore.value = data.studentScore || '0'
    // 错题数量
    wrongCount.value = data.wrongBookCount || 0

    volName.value = data.volName || ''
  }
}

// 页面加载时初始化数据
onMounted(async () => {
  globalProperties.$aplusPush.pushEvent({ eventCode: buriedPointsMap.practice_result })
  try {
    await initData()
    await initQuestionStatus()
    setTimeout(() => {
      showAnimation.value = true
    }, 100)
  } catch (error) {
    console.error('初始化数据失败:', error)
  }
})

// 页面激活时重新初始化动画
// onActivated(() => {
//   showAnimation.value = true
// })

// 页面失活时销毁动画
// onDeactivated(() => {
//   showAnimation.value = false
// })

// // 组件销毁前清理动画
// onBeforeUnmount(() => {
//   showAnimation.value = false
// })

// 检查是否需要打开Flutter界面 - 错题练习
const checkAndOpenFlutterInterfaceForWrongQuiz = async (params: {
  practiceRecordUid: string
  pageNumber: number
  modifyFirst: string
}) => {
  console.log('deviceStore.deviceType', deviceStore.deviceType)

  if (deviceStore.deviceType === 'pad') {
    try {
      // 构建错题练习的URL参数
      let url = 'PracticeDowrongquiz?'
      const urlParams = new URLSearchParams()

      // 添加基础参数
      urlParams.append('practiceRecordUid', params.practiceRecordUid)
      urlParams.append('pageNumber', String(params.pageNumber))
      urlParams.append('modifyFirst', params.modifyFirst)

      url += urlParams.toString()

      TransferToFlutter({
        type: TransferType.jump,
        data: {
          title: '',
          url: url
        }
      })
      return true // 已经打开Flutter界面
    } catch (error) {
      console.error('发送消息给Flutter失败:', error)
      // 如果发送失败，继续使用当前逻辑
      return false
    }
  }
  return false // 不是pad设备，使用当前逻辑
}

const handleCheck = async () => {
  // 检查是否需要打开Flutter界面
  const params = {
    practiceRecordUid: router.currentRoute.value.query.practiceRecordUid as string,
    pageNumber: 1,
    modifyFirst: '1'
  }

  const openedFlutter = await checkAndOpenFlutterInterfaceForWrongQuiz(params)
  if (openedFlutter) {
    return // 已经打开Flutter界面，不需要继续执行
  }

  router.push({
    name: SmartPracticePageNames.PracticeDowrongquiz,
    query: params
  })
}

const handleContinue = () => {
  // 获取当前试卷的exam、examBoard、subject
  // TODO: 获取当前试卷的examTitle、examBoardTitle、subjectTitle
  router.push({
    name: SmartPracticePageNames.PracticeQuizList,
    query: {
      exam: exam.value,
      examTitle: examTitle.value,
      examBoardTitle: examBoardTitle.value,
      examBoard: examBoard.value,
      subject: subject.value,
      subjectTitle: subjectTitle.value
    }
  })
}

const handleBack = () => {
  router.go(-1)
}

const paddingTop = computed(() => {
  const appInfoStr = localStorage.getItem(config.appInfoKey)
  if (!appInfoStr) return '44px'
  const appInfo = JSON.parse(appInfoStr)
  return `${Number(appInfo.safeAreaTop) + 44}px`
})

const containerStyle = computed(() => {
  return {
    paddingTop: paddingTop.value
  }
})
</script>
<template>
  <div
    class="min-h-screen relative pb-[80px]"
    :class="[
      'bg-[url(@/assets/images/practice/score_bg_2.png)] md:bg-[url(@/assets/images/practice/score_bg.png)] bg-no-repeat bg-[length:100%_auto] bg-top'
    ]"
  >
    <common-nav
      :leftArrow="true"
      :showBackground="false"
      :scrollOpacity="true"
      :maxScrollForOpacity="100"
      :enableTransition="false"
      title=""
    >
      <template #left>
        <div class="flex items-center">
          <van-icon name="arrow-left" color="#000000" @click="handleBack" />
        </div>
      </template>
    </common-nav>

    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <div class="relative z-10 py-[8px] md:py-[14px] px-[32px] md:px-[48px]">
        <!-- 恭喜完成提示 -->
        <div class="h-[60px] md:h-[24px]"></div>
        <div class="text-white text-[18px] font-medium mt-[30px]">恭喜你完成本次练习!</div>

        <div class="flex items-baseline">
          <div class="text-white text-[12px] mt-[12px] opacity-70">您已累计刷题</div>
          <div class="mr-[10px] flex items-baseline">
            <div class="artfont text-white text-[20px] font-bold ml-[8px] translate-y-[2px]">
              {{ practiceCount }}
            </div>
            <div class="text-white text-[12px] ml-[4px] opacity-70">套</div>
          </div>
          <div class="flex items-baseline">
            <div class="artfont text-white text-[20px] font-bold translate-y-[2px]">
              {{ practiceDays }}
            </div>
            <div class="text-white text-[12px] ml-[4px] opacity-70">天</div>
          </div>
        </div>
      </div>

      <!-- 试卷信息卡片 -->
      <div class="mx-[16px] mt-[22px] relative z-10">
        <div class="w-[305px] h-[407px] absolute top-[-238px] right-[-20px] z-[-1] overflow-hidden">
          <div class="absolute w-[305px] h-[407px] top-[-12px] right-[-64px] md:right-[-20px]">
            <Vue3Lottie :animationData="AstronautJSON" />
          </div>
        </div>
        <TestInfoCard
          v-if="questionStatusList.length"
          :course-name="volName"
          :current-rate="currentRate"
          :time-used="usedMinutes"
          :total-score="totalScore"
          :student-score="studentScore"
          :get-question-status-class="getQuestionStatusClass"
          :question-status-list="questionStatusList"
          @question-click="(item, index) => handleQuestionClick(item, index)"
        />

        <div
          v-else
          class="flex items-center justify-between flex-col bg-[url(@/assets/images/practice/img_result_bg.png)] bg-no-repeat bg-cover rounded-[10px] pb-[40px]"
        >
          <!-- 试卷名称 -->
          <div class="text-[14px] text-[#3C4258] font-medium mb-[16px] text-center">
            {{ volName }}
          </div>
          <img :src="getImageUrl('practice/ic_correct.png')" class="w-[91px] h-[81px] mt-[26px]" />
          <div class="flex items-center">
            <span class="desc text-va-grey-text-1 text-[14px] flex items-center"
              >唯小寻努力批改中...</span
            >
            <span
              class="iconfont iconshuaxin text-va-primary-btn text-[18px] ml-[5px]"
              @click="onRefresh"
            ></span>
          </div>
        </div>
      </div>

      <!-- 分析结果卡片 -->
      <div
        class="mx-[16px] mt-[16px] relative bg-[url(@/assets/images/practice/img_result_bg.png)] bg-no-repeat bg-cover rounded-[10px] pb-[40px]"
      >
        <div class="p-[16px]">
          <div class="text-[16px] font-bold text-[#3C4258] mb-[16px]">答题结果分析反馈</div>
          <!-- lineHeight: 24px -->
          <p class="text-[14px] text-[#3C4258] leading-[24px]" v-if="paperAnalysis">
            <MarkdowRender :content="paperAnalysis" />
          </p>
          <div v-else class="flex items-center justify-between flex-col">
            <img :src="getImageUrl('practice/ic_analysis.png')" class="w-[91px] h-[81px]" />
            <div class="flex items-center mt-[16px]">
              <span class="desc text-va-grey-text-1 text-[14px] flex items-center"
                >唯小寻努力分析中...</span
              >
              <span
                class="iconfont iconshuaxin text-va-primary-btn text-[18px] ml-[5px]"
                @click="onRefresh"
              ></span>
            </div>
          </div>
        </div>
      </div>
    </van-pull-refresh>

    <!-- 底部按钮 -->
    <div
      class="fixed bottom-0 left-0 right-0 bg-white flex gap-[16px] z-20 px-[16px] items-start py-[12px]"
    >
      <div class="flex flex-col items-center px-3" @click="handleCheck">
        <div class="relative">
          <img
            src="@/assets/images/practice/ic_check.png"
            alt="订正错题"
            class="w-[24px] h-[24px] mb-[4px]"
          />
          <div
            v-if="wrongCount > 0"
            class="absolute -top-[12px] left-[16px] bg-[#FF5252] text-white text-[10px] rounded-full min-w-[16px] h-[16px] flex items-center justify-center px-[4px]"
          >
            {{ wrongCount > 99 ? '99+' : wrongCount }}
          </div>
        </div>
        <span class="text-[12px] text-[#326EFF] font-medium">订正错题</span>
      </div>
      <VaBaseButton
        type="primary"
        text="继续刷题"
        label-style="font-size: 16px; font-weight: 500"
        class="flex-1 h-[40px] rounded-full"
        @click="handleContinue"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped></style>
