<script setup lang="ts">
import { onMounted, onUnmounted, ref, watch, onBeforeUnmount } from 'vue'
import lottie from 'lottie-web'
import type { AnimationConfigWithData, AnimationConfigWithPath, RendererType } from 'lottie-web'

interface Props {
  // 动画数据
  animationData?: any
  // 动画路径
  path?: string
  // 循环播放
  loop?: boolean
  // 自动播放
  autoplay?: boolean
  // 动画速度
  speed?: number
  // 动画方向
  direction?: 1 | -1
  // 动画容器样式
  style?: string | Record<string, string | number>
}

const props = withDefaults(defineProps<Props>(), {
  loop: true,
  autoplay: true,
  speed: 1,
  direction: 1,
  style: ''
})

const emit = defineEmits<{
  (e: 'complete'): void
  (e: 'error', error: any): void
}>()

const container = ref<HTMLElement | null>(null)
const animation = ref<any>(null)

// 初始化动画
const initAnimation = () => {
  if (!container.value) return

  // 确保先清理之前的动画实例
  destroyAnimation()

  const baseOptions = {
    container: container.value,
    renderer: 'svg' as RendererType,
    loop: props.loop,
    autoplay: props.autoplay,
    rendererSettings: {
      progressiveLoad: true,
      preserveAspectRatio: 'xMidYMid slice',
      clearCanvas: true
    }
  }

  let options: AnimationConfigWithData<RendererType> | AnimationConfigWithPath<RendererType>

  // 根据是否有 path 或 animationData 来设置不同的配置
  if (props.path) {
    options = {
      ...baseOptions,
      path: props.path
    } as AnimationConfigWithPath<RendererType>
  } else if (props.animationData) {
    options = {
      ...baseOptions,
      animationData: props.animationData
    } as AnimationConfigWithData<RendererType>
  } else {
    return
  }

  try {
    animation.value = lottie.loadAnimation(options)

    // 设置动画速度和方向
    if (animation.value) {
      animation.value.setSpeed(props.speed)
      animation.value.setDirection(props.direction)
    }

    // 监听动画完成事件
    animation.value.addEventListener('complete', () => {
      emit('complete')
    })

    // 监听动画错误事件
    animation.value.addEventListener('error', (error: any) => {
      emit('error', error)
    })
  } catch (error) {
    console.error('Failed to initialize animation:', error)
    emit('error', error)
  }
}

// 销毁动画
const destroyAnimation = () => {
  if (animation.value) {
    animation.value.destroy()
    animation.value = null
  }
}

// 播放动画
const play = () => {
  if (animation.value) {
    animation.value.play()
  }
}

// 暂停动画
const pause = () => {
  if (animation.value) {
    animation.value.pause()
  }
}

// 停止动画
const stop = () => {
  if (animation.value) {
    animation.value.stop()
  }
}

// 设置动画速度
const setSpeed = (speed: number) => {
  if (animation.value) {
    animation.value.setSpeed(speed)
  }
}

// 设置动画方向
const setDirection = (direction: 1 | -1) => {
  if (animation.value) {
    animation.value.setDirection(direction)
  }
}

// 监听属性变化
watch(
  () => props.speed,
  (newSpeed) => {
    setSpeed(newSpeed)
  }
)

watch(
  () => props.direction,
  (newDirection) => {
    setDirection(newDirection)
  }
)

// 监听动画数据变化，重新初始化动画
watch(
  () => [props.animationData, props.path],
  () => {
    if (container.value) {
      initAnimation()
    }
  },
  { deep: true }
)

// 组件挂载时初始化动画
onMounted(() => {
  // 使用requestAnimationFrame确保DOM已完全渲染
  requestAnimationFrame(() => {
    initAnimation()
  })
})

// 组件卸载前销毁动画
onBeforeUnmount(() => {
  destroyAnimation()
})

// 组件卸载时确保销毁动画
onUnmounted(() => {
  destroyAnimation()
})

// 暴露方法给父组件
defineExpose({
  play,
  pause,
  stop,
  setSpeed,
  setDirection,
  destroyAnimation,
  initAnimation
})
</script>

<template>
  <div ref="container" :style="style"></div>
</template>

<style scoped>
div {
  width: 100%;
  height: 100%;
}
</style>
