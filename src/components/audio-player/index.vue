<script setup lang="ts">
import { ref, watch, computed, defineOptions, nextTick, toRefs, onBeforeUnmount } from 'vue'
import { showToast } from 'vant'
import ProgressBar from './ProgressBar.vue'

import { formatTime } from '@/utils/time'
import { getImageUrl } from '@/utils/img'

defineOptions({
  name: 'AudioPlayer'
})

export interface AudioPlayerProps {
  url?: string
  autoPlay?: boolean
  playbackRateList?: number[]
  defaultRateIdx?: number
  loading?: boolean
  showSubmitBtn?: boolean
}
const props = withDefaults(defineProps<AudioPlayerProps>(), {
  autoPlay: false,
  playbackRateList: () => [0.5, 1, 1.5, 2],
  defaultRateIdx: 1,
  loading: false,
  showSubmitBtn: true
})

interface Emits {
  (e: 'submit-answers'): void
}
const emit = defineEmits<Emits>()

const audioVM = ref<HTMLAudioElement | null>(null)
const audioInfo = ref({
  playing: false,
  currentTime: 0,
  duration: 0,
  playbackRate: props.playbackRateList[props.defaultRateIdx]
})

const currentBackIdx = ref(props.defaultRateIdx)

const progress = computed(() => {
  return audioInfo.value.currentTime / audioInfo.value?.duration
})
const remainLabel = computed(() => {
  return formatTime(audioInfo.value.currentTime, false)
})
const totalTimeLabel = computed(() => {
  return formatTime(audioInfo.value.duration, false)
})

const playIcon = computed(() => {
  const suffix = audioInfo.value.playing ? 'pause' : 'play'
  return getImageUrl(`language/${suffix}.png`)
})

watch(
  () => props?.url,
  (newVal?: string) => {
    if (newVal) {
      nextTick(() => {
        audioInfo.value.currentTime = 0
        const audioEl = audioVM.value
        audioEl!.src = newVal
        audioEl?.load()
      })
    }
  },
  { immediate: true }
)
watch(
  () => props?.defaultRateIdx,
  (newVal: number) => {
    if (newVal) {
      nextTick(() => {
        const audioEl = audioVM.value
        audioEl!.playbackRate = newVal
      })
    }
  },
  { immediate: true }
)

onBeforeUnmount(() => {
  audioVM.value?.pause()
  audioInfo.value = {
    playing: false,
    currentTime: 0,
    duration: 0,
    playbackRate: props.playbackRateList[props.defaultRateIdx]
  }
})

const progressChanging = (progress: number) => {
  audioInfo.value.currentTime = audioInfo.value.duration * progress
}
const progressEnd = (progress: number) => {
  audioInfo.value.currentTime = audioInfo.value.duration * progress
  audioVM.value!.currentTime = audioInfo.value.currentTime
  if (progress >= 1) {
    audioInfo.value.playing = false
  }
}

const onCanplay = () => {
  if (!props.url) {
    showToast('音频链接不能为空')
    return
  }
  const audioEl = audioVM.value
  if (!audioInfo.value?.duration) {
    audioInfo.value.duration = audioEl?.duration || 0
  }
}

const loadedmetadata = () => {
  if (!props.url) {
    showToast('音频链接不能为空')
    return
  }
  const audioEl = audioVM.value
  audioInfo.value.duration = audioEl?.duration || 0
}

const playError = () => {
  audioInfo.value.playing = false
}

const updateTime = (e: any) => {
  audioInfo.value.currentTime = parseInt(e?.target?.currentTime)
  audioInfo.value.duration = parseInt(e?.target?.duration)
}
const playEnd = () => {
  const audioEl = audioVM.value
  audioInfo.value.duration = audioEl?.duration || 0
  audioInfo.value.currentTime = audioInfo.value.duration
  audioInfo.value.playing = false
}

const playAudio = () => {
  if (!props.url) {
    showToast('音频链接不能为空')
    return
  }
  const audioEl = audioVM.value
  if (audioEl) {
    if (audioInfo.value.playing) {
      audioEl.pause()
    } else {
      audioEl.play()
    }
    audioInfo.value.playing = !audioInfo.value.playing
  }
}

const changeRate = () => {
  const { playbackRateList } = toRefs(props)
  const idx = currentBackIdx.value + 1
  if (idx > playbackRateList.value.length - 1) {
    currentBackIdx.value = 0
  } else {
    currentBackIdx.value = idx
  }
  audioInfo.value.playbackRate = playbackRateList.value[currentBackIdx.value]
  audioVM.value!.playbackRate = audioInfo.value.playbackRate
}

const submitAnswers = () => {
  emit('submit-answers')
}
</script>
<template>
  <div class="bg-white px-[10px] box-border w-[100%]">
    <ProgressBar
      :progress="progress"
      @progress-changing="progressChanging"
      @progress-changed="progressEnd"
    />
    <div class="time-wrap text-center text-[12px]">
      <span class="current-time text-va-blue">{{ remainLabel }}</span>
      <span class="total-time text-[#C2C6CC]"> / {{ totalTimeLabel }}</span>
    </div>
    <div
      class="operate-wrap flex justify-between items-center h-[44px] px-[35px] pb-[24px] pt-[2px] box-content"
    >
      <div class="rate-btn text-va-blue text-[14px] font-semibold" @click="changeRate">
        {{ audioInfo.playbackRate }}X
      </div>
      <div class="play" @click="playAudio">
        <img :src="playIcon" alt="play" class="inline-block h-[44px] w-[44px]" />
      </div>
      <div
        v-show="showSubmitBtn"
        class="submit text-va-blue text-[14px] font-semibold flex items-center"
        @click="submitAnswers"
      >
        <van-loading color="#6481EE" class="mr-[4px]" v-show="props.loading" />
        <span>提交答案</span>
      </div>
    </div>
    <audio
      class="hidden"
      ref="audioVM"
      v-bind="$attrs"
      preload="metadata"
      @canplay="onCanplay"
      @loadedmetadata="loadedmetadata"
      @error="playError"
      @timeupdate="updateTime"
      @ended="playEnd"
    >
      浏览器版本不支持~
    </audio>
  </div>
</template>

<style lang="scss" scoped>
.operate-wrap {
  position: relative;
  .play {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }
}
</style>
