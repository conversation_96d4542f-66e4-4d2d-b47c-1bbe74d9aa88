<template>
  <van-popup
    :show="show"
    @update:show="$emit('update:show', $event)"
    position="bottom"
    round
    :style="{ height: 'auto', maxHeight: '60%' }"
    class="selection-popup"
  >
    <div class="popup-container">
      <!-- 固定标题区域 -->
      <div class="fixed-title">
        <div class="text-center text-[16px] font-medium">{{ title }}</div>
      </div>

      <!-- 可滚动内容区域 -->
      <div class="scrollable-content">
        <div class="flex flex-wrap gap-[12px] options-grid pb-[80px]">
          <div
            v-for="item in options"
            :key="item.value"
            class="h-[32px] min-w-[104px] flex-shrink-0 flex items-center justify-center rounded-full border text-[14px] cursor-pointer option-item px-3 overflow-hidden"
            :class="[
              isItemSelected(item)
                ? 'border-[#326EFF] text-[#326EFF] bg-[#F5F8FF]'
                : 'border-[#E5E6EB] text-[#333]'
            ]"
            @click.stop="selectItem(item.value)"
          >
            <span class="truncate">{{ item.label || item.title }}</span>
          </div>
        </div>
      </div>
    </div>
  </van-popup>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, onMounted, nextTick, watch, computed } from 'vue'

interface Option {
  label?: string
  title?: string
  value: string | number
  exam?: number
  examBoard?: number
  subject?: number
}

// 定义组件属性
const props = defineProps<{
  // 控制弹窗显示/隐藏
  show: boolean
  // 弹窗标题
  title: string
  // 选项列表
  options: Option[]
  // 当前选中的值
  modelValue: string | number
}>()

// 定义组件事件
const emit = defineEmits<{
  // 更新弹窗显示状态
  (e: 'update:show', value: boolean): void
  // 更新选中的值
  (e: 'update:modelValue', value: string | number): void
  // 选择事件，当用户选择一个选项时触发
  (e: 'select', value: string | number): void
}>()

// 监听显示状态变化
watch(
  () => props.show,
  (newVal) => {
    if (newVal) {
      // 当弹窗显示时，确保滚动功能正常
      nextTick(() => {
        enableScrolling()
      })
    }
  },
  { immediate: true }
)

// 启用滚动功能
const enableScrolling = () => {
  const contentEl = document.querySelector('.scrollable-content')
  if (contentEl) {
    // 确保内容区域可以滚动
    contentEl.addEventListener(
      'touchmove',
      (e) => {
        e.stopPropagation()
      },
      { passive: true }
    )
  }
}

// 判断项目是否被选中
const isItemSelected = (item: Option) => {
  // 如果是科目选择（有exam、examBoard和subject字段）
  if (item.exam !== undefined && item.examBoard !== undefined && item.subject !== undefined) {
    // 找到当前选中的项
    const selectedItem = props.options.find((option) => option.value === props.modelValue)
    if (
      selectedItem &&
      selectedItem.exam !== undefined &&
      selectedItem.examBoard !== undefined &&
      selectedItem.subject !== undefined
    ) {
      // 比较完整的科目信息
      return (
        item.exam === selectedItem.exam &&
        item.examBoard === selectedItem.examBoard &&
        item.subject === selectedItem.subject
      )
    }
  }
  // 默认比较value
  return props.modelValue === item.value
}

// 选择项目并关闭弹窗
const selectItem = (value: string | number) => {
  // 更新选中的值
  emit('update:modelValue', value)
  // 触发选择事件
  emit('select', value)
  // 关闭弹窗
  emit('update:show', false)
}
</script>

<style scoped>
.selection-popup {
  touch-action: pan-y;
}

.popup-container {
  display: flex;
  flex-direction: column;
  max-height: 100%;
  width: 100%;
}

.fixed-title {
  position: sticky;
  top: 0;
  background-color: white;
  padding: 16px 16px 12px;
  z-index: 1;
  border-bottom: 1px solid #f5f5f5;
  width: 100%;
}

.scrollable-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  padding-top: 12px;
  -webkit-overflow-scrolling: touch;
  touch-action: pan-y;
  width: 100%;
  overscroll-behavior: contain;
  height: 100%;
  max-height: 400px; /* 确保有足够的高度可滚动 */
}

.options-grid {
  width: 100%;
}

.option-item {
  user-select: none;
  touch-action: manipulation;
}

/* 确保在iOS上滚动正常工作 */
@supports (-webkit-touch-callout: none) {
  .scrollable-content {
    -webkit-user-select: none;
    user-select: none;
  }
}

/* 修复Vant弹窗内部滚动问题 */
:deep(.van-popup) {
  overflow: hidden;
  max-height: 60%;
}

:deep(.van-popup--bottom) {
  overflow: hidden;
}
</style>
