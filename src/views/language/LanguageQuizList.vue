<script setup lang="ts">
import { languagePageNames } from '@/router/modules/language'
import { ref, onBeforeMount, onMounted, nextTick, onBeforeUnmount } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useTitle } from '@vueuse/core'
import { transferAppMsg } from '@/utils/msg'
import { useGlobal, useLoading } from '@/hooks'
import type { VolSubjectGroupOutVO, VolSubjectVO, VolUidVO } from 'va.study.service.volquiz'
import { useLanguageCategoryStore } from '@/stores'
import { storeToRefs } from 'pinia'

const { globalProperties } = useGlobal()
const { startLoading, endLoading, loading } = useLoading()
const languageCategoryStore = useLanguageCategoryStore()
const { lastCategoryId } = storeToRefs(languageCategoryStore)

const router = useRouter()
const routes = useRoute()
const pageTitle = useTitle()

const catgoryList = ref<VolSubjectGroupOutVO[]>([])
const quizsVM = ref<HTMLDivElement | null>(null)
const subCardList = ref<VolSubjectGroupOutVO[]>([])

const activeCategory = ref(0)

onBeforeMount(() => {
  if (routes.query?.title) {
    pageTitle.value = routes.query?.title as string
  }
})

onMounted(() => {
  getCategoryList()
})

onBeforeUnmount(() => {
  const currentVolUid = catgoryList.value[activeCategory.value]?.volUid
  languageCategoryStore.updateCategory(currentVolUid)
})

const getCategoryList = async () => {
  try {
    startLoading()
    const params: VolSubjectVO = {
      volType: Number(routes.query.volType),
      subjectCode: Number(routes.query.subjectCode)
    }
    const { dataList } = await globalProperties.$api.VolQuiz.getVolList(params)
    catgoryList.value = dataList || []
    if (dataList?.length) {
      const idx = dataList.findIndex((item) => item.volUid === lastCategoryId.value)
      let volUid = dataList?.[0].volUid
      if (idx !== -1) {
        activeCategory.value = idx
        volUid = lastCategoryId.value
      }
      await getGroupList(volUid)
    }
  } finally {
    endLoading()
  }
}

const getGroupList = async (volUid: string) => {
  try {
    startLoading()
    const params: VolUidVO = {
      volUid
    }
    const res = await globalProperties.$api.VolQuiz.getGroupList(params)
    subCardList.value = res?.children || []
    nextTick(() => {
      quizsVM.value?.scrollTo({ top: 0, behavior: 'smooth' })
    })
    return res
  } finally {
    endLoading()
  }
}

const handleCategoryItem = (item: VolSubjectGroupOutVO, index: number) => {
  activeCategory.value = index
  getGroupList(item.volUid)
}

const jumpToQuiz = (item: VolSubjectGroupOutVO, subItem: VolSubjectGroupOutVO) => {
  const currentCategory = catgoryList.value[activeCategory.value]
  const label = `${currentCategory.groupAlias || currentCategory.groupName}-${item.groupAlias}-${subItem.groupAlias}`
  router.push({
    name: languagePageNames.LanguageDoQuiz,
    query: {
      title: label,
      languageType: routes.query.languageType,
      volGroupUid: subItem.volGroupUid,
      groupUid: subItem.groupUid
    }
  })
  transferAppMsg(label)
}
</script>
<template>
  <div class="quiz-list-wrap flex bg-bg-grey h-[100%]" v-show="!loading && catgoryList?.length">
    <div class="category-list w-[88px] bg-white mr-[10px] h-[100%] overflow-y-auto">
      <div
        class="category-item h-[62px] text-center leading-[62px] box-border text-[#3F454F] font-semibold text-[14px] truncate pl-[6px]"
        :class="{ active: index === activeCategory }"
        v-for="(item, index) in catgoryList"
        :key="index"
        @click="handleCategoryItem(item, index)"
      >
        {{ item.groupName }}
      </div>
    </div>
    <div class="quiz-list flex-1 h-[100%] text-[#3F454F] overflow-y-auto" ref="quizsVM">
      <div class="card-item bg-white mb-[10px]" v-for="(item, idx) in subCardList" :key="idx">
        <div class="title text-[16px] px-[15px] pt-[15px] pb-[5px] font-semibold truncate">
          {{ item.groupName }}
        </div>
        <div
          class="card-item-sub px-[15px] py-[16px] flex justify-between border-b border-[#eee]"
          v-for="(subItem, subIdx) in item.children"
          :key="subIdx"
          @click="jumpToQuiz(item, subItem)"
        >
          <span class="text-[15px] font-semibold truncate">{{ subItem.groupName }}</span>
          <van-icon class="arr-icon text-[16px] text-va-text-grey" name="arrow" />
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.category-item {
  &.active {
    position: relative;
    color: #6481ee;
    font-weight: 400;
    background: linear-gradient(90deg, #ffffff 0%, #dfe6ff 100%) no-repeat;
    &::before {
      position: absolute;
      content: '';
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 5px;
      height: 14px;
      background: linear-gradient(133deg, #4866fb 0%, #a069ff 100%) no-repeat;
      border-radius: 0px 4px 4px 0px;
    }
  }
}
.card-item {
  border-radius: 6px 0px 0px 6px;
}
.arr-icon {
  font-size: 16px;
}
</style>
