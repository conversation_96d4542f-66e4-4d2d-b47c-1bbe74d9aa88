<script setup lang="ts">
import { useGlobal, useLoading } from '@/hooks'
import { SmartPracticePageNames } from '@/router/modules/practice'
import type {
  UserSubjectTagData,
  WrongBookCategoryStatsRespDTO,
  WrongBookStatsReqDTO
} from '@/services/practice'
import { usePracticeStore,useDeviceStore } from '@/stores'
import { getImageUrl } from '@/utils/img'
import { computed, onMounted, ref, watch, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'

import CategoryGrid from './components/CategoryGrid.vue'
import QuestionTimeList from './components/QuestionTimeList.vue'
import SegmentedControl from './components/SegmentedControl.vue'
import SubjectSelector from './components/SubjectSelector.vue'
import { buriedPointsMap } from '@/model/buried-point'
import { TransferToFlutter, TransferType } from '@/utils/msg'
const deviceStore = useDeviceStore()

// 导入练习状态管理

const router = useRouter()
const route = useRoute()
const { globalProperties } = useGlobal()
const { startLoading, endLoading, loading } = useLoading()
const practiceStore = usePracticeStore()

// 是否已选择科目
const hasSelectedSubject = ref(false)

// 定义筛选器接口
interface FilterItem extends UserSubjectTagData {
  label: string
  value: string
  active: boolean
}

// 定义统计项接口
interface StatisticsItem {
  label: string
  value: number
}

// 科目筛选
const subjectFilters = ref<FilterItem[]>([])

// 科目选择弹窗
const subjectFilterRef = ref<InstanceType<typeof SubjectSelector> | null>(null)

// 获取当前选中的科目
const currentSubject = computed(() => {
  return subjectFilters.value.find((subject) => subject.active)
})

// 获取考试局信息显示文本
const examBoardText = computed(() => {
  const subject = currentSubject.value
  if (!subject) return ''
  return `${subject.examTitle || ''}-${subject.examBoardTitle || ''}`
})

// 监听当前选中的科目变化，保存到 store
watch(
  () => currentSubject.value,
  (newSubject) => {
    if (newSubject) {
      practiceStore.setWrongbkSelectedSubject({
        exam: newSubject.exam,
        examBoard: newSubject.examBoard,
        subject: newSubject.subject,
        examTitle: newSubject.examTitle,
        examBoardTitle: newSubject.examBoardTitle,
        subjectTitle: newSubject.subjectTitle
      })
    }
  },
  { deep: true }
)

// 统计数据
const statisticsItems = ref<StatisticsItem[]>([
  { label: '全部错题', value: 0 },
  { label: '待订正', value: 0 },
  { label: '已订正', value: 0 }
])

// 错题分类数据
const wrongCategories = ref<WrongBookCategoryStatsRespDTO[]>([])

// 分类筛选标签
const categoryTabs = ref([
  { label: '按paper', value: 'paper' },
  { label: '按错题时间', value: 'time' }
])

// 当前选中的分类标签
const selectedCategoryTab = ref<'paper' | 'time'>('paper')

// 当前选中的分类
const selectedCategory = ref<WrongBookCategoryStatsRespDTO | null>(null)

// 科目变更时，确保时间列表被重置
const resetTimeList = () => {
  // 如果当前显示的是按时间分类的错题，不需要手动重置
  // QuestionTimeList 组件会自动通过 watch 监听器检测到科目变化并重新加载数据
  // 不再需要手动切换标签强制重新挂载组件
  // 如果遇到极端情况组件没有自动更新，用户可以手动切换标签或下拉刷新
}

// 切换分类筛选标签
const toggleCategoryTab = (value: string) => {
  selectedCategoryTab.value = value as 'paper' | 'time'
  // 根据筛选条件获取相应的数据
  if (value === 'paper') {
    // 按paper分类时，只需要获取分类统计数据
    getWrongBkCategoryStatistics()
  }
  // 时间分类由 QuestionTimeList 组件内部处理
}

// 去订正
const goToCorrection = () => {
  // 跳转到订正页面的逻辑
  // 跳转做错题的页面
  router.push({
    name: SmartPracticePageNames.PracticeDowrongquiz,
    query: {
      modifyFirst: '1',
      exam: currentSubject.value?.exam,
      examBoard: currentSubject.value?.examBoard,
      subject: currentSubject.value?.subject,
      pageNumber: 1,
      rectifyState: 0
    }
  })
}

// 获取错题本科目统计数据 1、全部错题 2、待订正 3、已订正
const getWrongBkStatistics = async () => {
  const params: WrongBookStatsReqDTO = {
    examBoard: currentSubject.value?.examBoard || 0,
    exam: currentSubject.value?.exam || 0,
    subject: currentSubject.value?.subject || 0
  }
  const res = await globalProperties.$http.Practice.WrongBookAnswerController.subjectStats(params)
  statisticsItems.value = [
    {
      label: '全部错题',
      value: Number(res.data?.wrongCount || 0)
    },
    {
      label: '待订正',
      value: Number(res.data?.unRectifyCount || 0)
    },
    {
      label: '已订正',
      value: Number(res.data?.rectifyCount || 0)
    }
  ]
}

// 获取错题本分类统计
const getWrongBkCategoryStatistics = async () => {
  const params: WrongBookStatsReqDTO = {
    examBoard: Number(currentSubject.value?.examBoard) || 0,
    exam: Number(currentSubject.value?.exam) || 0,
    subject: Number(currentSubject.value?.subject) || 0
  }
  const { data } =
    await globalProperties.$http.Practice.WrongBookAnswerController.categoryStats(params)
  wrongCategories.value = data || []
}

// 处理分类选择
const handleCategorySelect = (category: WrongBookCategoryStatsRespDTO, index: number) => {
  selectedCategory.value = category

  // 根据当前选中的标签执行不同的操作
  if (selectedCategoryTab.value === 'time') {
    // 按错题时间标签下不需要特殊处理，QuestionTimeList组件会自行加载数据
  } else {
    // 按paper标签下，不需要调用wrongBookPage接口
  }
}

// 处理查看问题详情
const handleViewQuestionDetail = (question: any, index: number) => {
  // 跳转到问题详情页面
  router.push({
    name: SmartPracticePageNames.PracticeDowrongquiz,
    query: {
      id: question.id,
      exam: question.exam,
      examBoard: question.examBoard,
      subject: question.subject,
      // 直接使用传递过来的索引 + 1 作为题目位置
      pageNumber: index + 1
    }
  })
}

// 注册全局方法，供Flutter调用
onMounted(() => {
  // 初始化数据
  getSubscribedSubjects()
})

// 切换科目筛选
const toggleSubjectFilter = (index: number) => {
  subjectFilters.value.forEach((filter, i) => {
    filter.active = i === index
  })

  // 设置已选择科目状态
  hasSelectedSubject.value = true

  // 根据选中的科目获取错题统计数据
  Promise.all([getWrongBkStatistics(), getWrongBkCategoryStatistics()]).then(() => {
    // 重置时间列表
    resetTimeList()

    // 保存当前选中的科目到 store
    const currentActiveSubject = currentSubject.value
    if (currentActiveSubject) {
      practiceStore.setWrongbkSelectedSubject({
        exam: currentActiveSubject.exam,
        examBoard: currentActiveSubject.examBoard,
        subject: currentActiveSubject.subject,
        examTitle: currentActiveSubject.examTitle,
        examBoardTitle: currentActiveSubject.examBoardTitle,
        subjectTitle: currentActiveSubject.subjectTitle
      })
    }

    // 滚动到当前选中的科目
    nextTick(() => {
      if (subjectFilterRef.value) {
        subjectFilterRef.value.scrollToActiveItem(
          'active-subject-item',
          false, // 当前选中项不是"全部"选项
          subjectFilters.value.length
        )
      }
    })
  })
}

// 处理科目选择
const handleSubjectSelect = (value: string | number) => {
  // 设置已选择科目状态
  hasSelectedSubject.value = true

  // 获取错题数据
  Promise.all([getWrongBkStatistics(), getWrongBkCategoryStatistics()]).then(() => {
    // 重置时间列表
    resetTimeList()

    // 保存当前选中的科目到 store
    const currentActiveSubject = currentSubject.value
    if (currentActiveSubject) {
      practiceStore.setWrongbkSelectedSubject({
        exam: currentActiveSubject.exam,
        examBoard: currentActiveSubject.examBoard,
        subject: currentActiveSubject.subject,
        examTitle: currentActiveSubject.examTitle,
        examBoardTitle: currentActiveSubject.examBoardTitle,
        subjectTitle: currentActiveSubject.subjectTitle
      })
    }

    // 滚动到当前选中的科目
    nextTick(() => {
      if (subjectFilterRef.value) {
        subjectFilterRef.value.scrollToActiveItem(
          'active-subject-item',
          false, // 当前选中项不是"全部"选项
          subjectFilters.value.length
        )
      }
    })
  })
}

// 去刷题
const goToPractice = () => {
  // 跳转到刷题页面的逻辑
  router.replace({
    name: SmartPracticePageNames.PracticeQuizList,
    query: {
      exam: currentSubject.value?.exam,
      examTitle: currentSubject.value?.examTitle,
      examBoard: currentSubject.value?.examBoard,
      examBoardTitle: currentSubject.value?.examBoardTitle,
      subject: currentSubject.value?.subject,
      subjectTitle: currentSubject.value?.subjectTitle
    }
  })
}

// 移除未使用的函数
// initSelectedSubject 不再需要，其功能已被整合到 getSubscribedSubjects 中

onMounted(async () => {
  globalProperties.$aplusPush.pushEvent({ eventCode: buriedPointsMap['practice_wrongbk'] })
  try {
    startLoading()
    await getSubscribedSubjects()
  } finally {
    endLoading()
  }
})

const paddingTop = computed(() => {
  const appInfo = (window as any)?.appInfo
  const safeAreaTop = appInfo?.safeAreaTop || 10

  // 检测是否为iPad设备或大屏设备
  const isLargeDevice = window.innerWidth >= 768 // iPad通常宽度至少为768px

  // 对于iPad或大屏设备使用更大的padding值
  const additionalPadding = isLargeDevice ? 52 : 44

  return `${Number(safeAreaTop) + additionalPadding}px`
})

const containerStyle = computed(() => {
  return {
    paddingTop: paddingTop.value
  }
})

const goToWrongBk = (index: number, item: any) => {
  switch (index) {
    case 0:
      router.push({
        name: SmartPracticePageNames.PracticeDowrongquiz,
        query: {
          exam: currentSubject.value?.exam || 0,
          examBoard: currentSubject.value?.examBoard || 0,
          subject: currentSubject.value?.subject || 0
        }
      })
      break
    case 1:
      router.push({
        name: SmartPracticePageNames.PracticeDowrongquiz,
        query: {
          exam: currentSubject.value?.exam || 0,
          examBoard: currentSubject.value?.examBoard || 0,
          subject: currentSubject.value?.subject || 0,
          rectifyState: 0
        }
      })
      break
    case 2:
      router.push({
        name: SmartPracticePageNames.PracticeDowrongquiz,
        query: {
          exam: currentSubject.value?.exam || 0,
          examBoard: currentSubject.value?.examBoard || 0,
          subject: currentSubject.value?.subject || 0,
          rectifyState: 1
        }
      })
      break
  }
}

// 显示科目选择弹窗
const goToChooseSubject = () => {
  // 跳转到科目选择页面
  router.push({
    name: SmartPracticePageNames.PracticeChooseSubject
  })
}

// 获取订阅的科目列表并处理选中状态
const getSubscribedSubjects = async () => {
  const res = await globalProperties.$http.Practice.UserManagerController.getUserSubjectTagList()
  if (res.data?.dataList) {
    // 将接口返回的科目数据转换为筛选器格式
    subjectFilters.value = res.data.dataList.map(
      (subject): FilterItem => ({
        ...subject,
        label: subject.subjectTitle || '',
        value: String(subject.subject || '0'),
        active: false // 默认不激活任何科目
      })
    )

    // 从 store 中获取之前选中的科目
    const storedSubject = practiceStore.wrongbkSelectedSubject

    let hasMatch = false

    // 如果有存储的科目数据，优先使用
    if (storedSubject && storedSubject.exam && storedSubject.examBoard && storedSubject.subject) {
      hasMatch = activateSubjectByIds(
        storedSubject.exam,
        storedSubject.examBoard,
        storedSubject.subject
      )
    }

    // 如果没有匹配到存储的科目，尝试使用URL参数
    if (!hasMatch && route.query.exam && route.query.examBoard && route.query.subject) {
      hasMatch = activateSubjectByIds(
        Number(route.query.exam),
        Number(route.query.examBoard),
        Number(route.query.subject)
      )
    }

    // 如果仍然没有匹配的科目且列表不为空，设置第一个为默认选中
    if (!hasMatch && subjectFilters.value.length > 0) {
      subjectFilters.value[0].active = true
    }

    hasSelectedSubject.value = subjectFilters.value.some((subject) => subject.active)

    // 获取错题本相关数据
    if (hasSelectedSubject.value) {
      await Promise.all([getWrongBkStatistics(), getWrongBkCategoryStatistics()])

      // 保存当前选中的科目到 store
      const currentActiveSubject = currentSubject.value
      if (currentActiveSubject) {
        practiceStore.setWrongbkSelectedSubject({
          exam: currentActiveSubject.exam,
          examBoard: currentActiveSubject.examBoard,
          subject: currentActiveSubject.subject,
          examTitle: currentActiveSubject.examTitle,
          examBoardTitle: currentActiveSubject.examBoardTitle,
          subjectTitle: currentActiveSubject.subjectTitle
        })
      }

      // 滚动到当前选中的科目
      nextTick(() => {
        if (subjectFilterRef.value) {
          subjectFilterRef.value.scrollToActiveItem(
            'active-subject-item',
            false, // 当前选中项不是"全部"选项
            subjectFilters.value.length
          )
        }
      })
    }
  }
}

// 根据科目ID激活对应科目
const activateSubjectByIds = (exam: number, examBoard: number, subject: number): boolean => {
  const matchingSubject = subjectFilters.value.find(
    (item) => item.exam === exam && item.examBoard === examBoard && item.subject === subject
  )

  if (matchingSubject) {
    // 将所有科目设为非激活
    subjectFilters.value.forEach((item) => (item.active = false))
    // 将匹配的科目设为激活
    matchingSubject.active = true
    return true
  }

  return false
}

// 检查是否需要打开Flutter界面
const checkAndOpenFlutterInterface = async (quiz: QuizItem, practiceRecordUid?: string) => {
  console.log('deviceStore.deviceType', deviceStore.deviceType)

  if (deviceStore.deviceType === 'pad') {
    try {
      // 构建URL参数
      let url = `PracticeDopractice?paperUid=${quiz.originalData.volUid}&paperVersion=${quiz.originalData.volVersion}`

      // 如果有practiceRecordUid，添加到URL中
      if (practiceRecordUid) {
        url += `&practiceRecordUid=${practiceRecordUid}&practiceState=${quiz.originalData.practiceState}`
      }

      TransferToFlutter({
        type: TransferType.jump,
        data: {
          title: '',
          url: url
        }
      })
      return true // 已经打开Flutter界面
    } catch (error) {
      console.error('发送消息给Flutter失败:', error)
      // 如果发送失败，继续使用当前逻辑
      return false
    }
  }
  return false // 不是pad设备，使用当前逻辑
}

</script>

<template>
  <div class="practice-wrongbk" :style="containerStyle" v-if="!loading">
    <common-nav
      :leftArrow="true"
      :showBackground="true"
      :scrollOpacity="false"
      :maxScrollForOpacity="100"
      :enableTransition="false"
    />

    <!-- 主体内容 -->
    <div>
      <!-- 科目选择和考试局信息仅在有科目且已选择科目时显示 -->
      <template v-if="subjectFilters.length > 0 && hasSelectedSubject">
        <!-- 科目选择 -->
        <div class="bg-white px-[16px] py-[6px]">
          <SubjectSelector
            ref="subjectFilterRef"
            :filters="subjectFilters"
            type="subject"
            :showPopupOnFirstVisit="true"
            storeKey="hasVisitedWrongbkPage"
            @toggle="toggleSubjectFilter"
            @select="handleSubjectSelect"
          />
        </div>
        <!-- 考试局信息移到统计卡片中 -->
      </template>

      <!-- 统计卡片和空状态2在同一个卡片中 -->
      <div v-if="hasSelectedSubject && subjectFilters.length > 0" class="px-[16px]">
        <!-- 统计卡片 -->
        <div class="bg-white rounded-[12px] p-[16px] mb-[20px] mt-[20px] pb-[24px]">
          <!-- 考试局信息 -->
          <div class="flex">
            <span
              class="bg-[#F4F6FA] text-[#8F94A8] text-[12px] rounded-[4px] px-[4px] h-[20px] flex items-center justify-center"
            >
              {{ currentSubject?.examTitle }}
            </span>
            <span
              class="bg-[#F4F6FA] text-[#8F94A8] text-[12px] rounded-[4px] px-[4px] h-[20px] flex items-center justify-center ml-[4px]"
            >
              {{ currentSubject?.examBoardTitle }}
            </span>
          </div>

          <div class="flex justify-between mt-[20px]">
            <div
              v-for="(item, index) in statisticsItems"
              :key="index"
              class="flex-1 flex flex-col items-center"
              :class="{ 'cursor-pointer hover:opacity-80': index === 2 }"
              @click="goToWrongBk(index, item)"
            >
              <div class="flex items-baseline">
                <span
                  class="artfont text-[22px] font-bold"
                  :class="{
                    'text-[#3C4258]': index === 0,
                    'text-[#F97171]': index === 1,
                    'text-[#4CC775]': index === 2
                  }"
                  >{{ item.value }}</span
                >
                <span class="text-[13px] text-[#8F94A8] ml-[2px]">题</span>
              </div>
              <div class="text-[14px] text-[#8F94A8]">
                {{ item.label }}
              </div>
            </div>
          </div>
        </div>

        <!-- 错题练习按钮卡片 -->
        <div v-if="statisticsItems[0].value !== 0" class="rounded-[12px] mb-[16px]">
          <div
            class="w-full h-[80px] rounded-[10px] p-[16px] flex justify-between items-center wrong-practice-bg"
          >
            <div class="flex items-center">
              <span class="text-[20px] font-[600] text-[#3C4258]">错题练习</span>
            </div>
            <VaBaseButton
              text="去订正"
              label-style="font-size: 13px; font-weight: 600"
              @click="goToCorrection"
              type="primary"
            />
          </div>
        </div>

        <!-- 错题分类 -->
        <!-- 错题分类标题 -->
        <div class="flex justify-between items-center mb-[15px]">
          <div class="text-[18px] font-medium text-[#3C4258]">错题分类</div>
          <SegmentedControl
            :options="categoryTabs"
            v-model:selected-value="selectedCategoryTab"
            @change="toggleCategoryTab"
          />
        </div>
        <!-- 有错题数据时显示 -->
        <div
          v-if="statisticsItems[0].value !== 0"
          :class="[
            selectedCategoryTab === 'paper'
              ? 'bg-white rounded-[12px] p-[16px] mb-[16px]'
              : 'mb-[16px]'
          ]"
        >
          <!-- 错题分类列表 -->
          <CategoryGrid
            v-if="selectedCategoryTab === 'paper'"
            :categories="wrongCategories"
            :examBoard="currentSubject?.examBoard ?? 0"
            :exam="currentSubject?.exam ?? 0"
            :subject="currentSubject?.subject ?? 0"
            @select="handleCategorySelect"
          />
          <!-- 按错题时间显示错题 -->
          <QuestionTimeList
            v-else
            :exam="currentSubject?.exam"
            :examBoard="currentSubject?.examBoard"
            :subject="currentSubject?.subject"
            @view-detail="handleViewQuestionDetail"
          />
        </div>

        <!-- 空状态2 - 已选择科目但没有错题 -->
        <div
          class="bg-white rounded-[12px] p-[16px] mb-[16px] flex flex-col items-center pb-[180px]"
          v-else
        >
          <img :src="getImageUrl('practice/img_empty.png')" class="w-[120px] h-[120px] mt-[60px]" />
          <div class="text-center mt-[16px]">
            <p class="text-[18px] font-medium text-[#3C4258] mb-[8px]">
              你太牛了，当前科目没有错题
            </p>
            <p class="text-[14px] text-[#6B7186]">继续刷题，查漏补缺吧！</p>
          </div>
          <VaBaseButton
            text="去刷题"
            label-style="font-size: 16px; font-weight: 500"
            @click="goToPractice"
            type="primary"
            class="w-full h-[40px] mt-[16px]"
          />
        </div>
      </div>

      <!-- 空状态1 - 未选择科目 -->
      <div class="px-[16px]" v-if="!hasSelectedSubject || subjectFilters.length === 0">
        <div class="flex flex-col items-center pt-[86px]">
          <img
            :src="getImageUrl('practice/img_empty_no_subject.png')"
            class="w-[110px] h-[104px]"
          />
          <div class="text-center mt-[16px]">
            <p class="text-[18px] font-medium text-[#3C4258] mb-[4px]">选择科目，开始刷题</p>
            <p class="text-[14px] text-[#6B7186]">小寻陪你高效刷题提分！</p>
          </div>
          <VaBaseButton
            text="选择科目"
            @click="goToChooseSubject"
            type="primary"
            class="w-[247px] h-[40px] mt-[24px] text-[16px] font-[500]"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.practice-wrongbk {
  min-height: 100vh;
  background-color: #f3f5f8;
  padding-top: 44px;

  .hide-scrollbar {
    scrollbar-width: none;
    -ms-overflow-style: none;
    &::-webkit-scrollbar {
      display: none;
    }
  }

  .exam-board {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 12px;
    color: #333;
  }

  //.wrong-practice-button {
  //background-image: url('{{ getImageUrl("practice/button.png") }}');
  //background-size: cover;
  //}

  .wrong-practice-bg {
    background-image: url('@/assets/images/practice/img_wrong_work_bg.png');
    background-size: cover;
  }
}
</style>
