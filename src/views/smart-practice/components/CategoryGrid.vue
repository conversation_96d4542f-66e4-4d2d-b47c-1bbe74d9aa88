<script setup lang="ts">
import { ref, defineProps, defineEmits, onMounted, watch } from 'vue'
import type { WrongBookCategoryStatsRespDTO } from '@/services/practice'
const props = defineProps<{
  categories: WrongBookCategoryStatsRespDTO[]
  examBoard?: number
  exam?: number
  subject?: number
}>()

const emit = defineEmits<{
  (e: 'select', category: WrongBookCategoryStatsRespDTO, index: number): void
  (e: 'click', category: WrongBookCategoryStatsRespDTO, index: number): void
}>()

const selectedIndex = ref<number | null>(null)

// 处理分类点击，触发点击事件（用户主动点击）
const handleCategoryClick = (index: number, category: WrongBookCategoryStatsRespDTO) => {
  selectCategory(index, category)
  emit('click', category, index)
}

// 只处理选中逻辑，不包含导航
const selectCategory = (index: number, category: WrongBookCategoryStatsRespDTO) => {
  selectedIndex.value = index
  emit('select', category, index)
}

// 组件挂载时，如果有分类数据，默认选中第一个（但不导航）
onMounted(() => {
  if (props.categories.length > 0) {
    selectCategory(0, props.categories[0])
  }
})

// 监听分类数据变化，如果数据变化，重置选中状态并默认选中第一个（但不导航）
watch(
  () => props.categories,
  (newCategories) => {
    if (newCategories.length > 0) {
      selectCategory(0, newCategories[0])
    } else {
      selectedIndex.value = null
    }
  },
  { deep: true }
)
</script>
<template>
  <div class="grid grid-cols-2 gap-[12px]">
    <div
      v-for="(category, index) in categories"
      :key="index"
      class="h-[46px] bg-white rounded-[10px] p-[12px] flex items-center cursor-pointer border border-[#EAEAEA]"
      @click="handleCategoryClick(index, category)"
    >
      <div class="flex-1 flex justify-between items-center">
        <div class="text-[16px] font-medium text-[#3C4258]">{{ category.tagTitle }}</div>
        <div class="text-[14px] text-[#326EFF]">
          <span>{{ category.count }}道</span>
          <van-icon name="arrow" size="16" color="#C9CDD4" class="ml-[4px]" />
        </div>
      </div>
    </div>
  </div>
</template>
