// src/global.d.ts
import * as AppMsg from '@/utils/msg'
import { type Service } from '@/utils/ajax'
import { type Service as HttpService } from '@/services'
import aplusPush from '@/utils/aplus_push'

export {}

declare global {
  interface Window {
    webkit: any
    WebViewBridge: any //全局变量名
  }
}

declare module 'vue' {
  interface ComponentCustomProperties {
    $msg: typeof AppMsg
    $api: Service
    $http: HttpService
    $aplusPush: typeof aplusPush
  }
}
